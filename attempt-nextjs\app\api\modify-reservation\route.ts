import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { callPythonBackend } from '@/lib/backend'

export async function POST(req: NextRequest) {
  try {
    const payload = await req.json()
    const { id, date, time, title, type } = payload
    
    // Validate required fields
    if (!id || !date) {
      return NextResponse.json({
        success: false,
        message: 'Missing required fields: id and date'
      }, { status: 400 })
    }
    
    console.log('Modifying reservation via Python backend:', { id, date, time, title, type })
    
    // Map Next.js payload to Python backend format
    const backendPayload = {
      new_date: date,
      new_time_slot: time,
      new_name: title,
      new_type: type,
      hijri: false,
      ar: false
    }
    
    // Make request to Python backend
    const backendResponse = await callPythonBackend(`/reservations/${id}/modify`, {
      method: 'POST',
      body: JSON.stringify(backendPayload)
    })
    
    console.log('Python backend modify response:', backendResponse)
    
    return NextResponse.json(backendResponse)
    
  } catch (error) {
    console.error('Error modifying reservation via Python backend:', error)
    return NextResponse.json({
      success: false,
      message: `Failed to modify reservation: ${error instanceof Error ? error.message : 'Unknown error'}`
    }, { status: 500 })
  }
} 