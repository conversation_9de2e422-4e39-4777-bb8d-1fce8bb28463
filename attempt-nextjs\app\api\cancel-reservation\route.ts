import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { callPythonBackend } from '@/lib/backend'

export async function POST(req: NextRequest) {
  try {
    const payload = await req.json()
    const { id, date } = payload
    
    // Validate required fields
    if (!id || !date) {
      return NextResponse.json({
        success: false,
        message: 'Missing required fields: id and date'
      }, { status: 400 })
    }
    
    console.log('Cancelling reservation via Python backend:', { id, date })
    
    // Map Next.js payload to Python backend format
    const backendPayload = {
      date_str: date,
      hijri: false,
      ar: false
    }
    
    // Make request to Python backend
    const backendResponse = await callPythonBackend(`/reservations/${id}/cancel`, {
      method: 'POST',
      body: JSON.stringify(backendPayload)
    })
    
    console.log('Python backend cancel response:', backendResponse)
    
    return NextResponse.json(backendResponse)
    
  } catch (error) {
    console.error('Error cancelling reservation via Python backend:', error)
    return NextResponse.json({
      success: false,
      message: `Failed to cancel reservation: ${error instanceof Error ? error.message : 'Unknown error'}`
    }, { status: 500 })
  }
} 