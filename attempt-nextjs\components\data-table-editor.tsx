"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON>er<PERSON><PERSON><PERSON>, DrawerDescription } from "@/components/ui/drawer"
import { DataEditor, GridCellKind, GridColumn, Item, TextCell, EditableGridCell } from "@glideapps/glide-data-grid"
import "@glideapps/glide-data-grid/dist/index.css"
import { useTheme } from "next-themes"
import { Button } from "@/components/ui/button"

interface CalendarEvent {
  id: string
  title: string
  start: string
  end?: string
  type: "reservation" | "conversation" | "cancellation"
  extendedProps: {
    description?: string
    customerName?: string
    phone?: string
    status?: string
  }
}

interface DataTableEditorProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  events: CalendarEvent[]
  selectedDateRange: { start: string; end: string } | null
  isRTL: boolean
  slotDurationHours: number
  freeRoam: boolean
  onSave: (events: CalendarEvent[]) => void
  onEventClick: (event: CalendarEvent) => void
}

export function DataTableEditor({
  open,
  onOpenChange,
  events,
  selectedDateRange,
  isRTL,
  slotDurationHours,
  freeRoam,
  onSave,
  onEventClick,
}: DataTableEditorProps) {
  const [editingEvents, setEditingEvents] = useState<CalendarEvent[]>([])
  const { theme } = useTheme()
  const [gridKey, setGridKey] = useState(0)

  // Dynamic grid and dialog height based on row count
  const [gridHeight, setGridHeight] = useState(300)
  useEffect(() => {
    const rowHeight = 35 // px per row
    const headerHeight = 60 // px for DataEditor header row
    
    let calculatedHeight;
    if (editingEvents.length === 0) {
      // Height for header + single trailing row if no data
      calculatedHeight = headerHeight + rowHeight 
    } else {
      // Height for header + data rows
      calculatedHeight = editingEvents.length * rowHeight + headerHeight
    }
    
    // Cap at 80% of viewport height
    const maxHeight = typeof window !== 'undefined' ? window.innerHeight * 0.8 : calculatedHeight
    setGridHeight(Math.min(calculatedHeight, maxHeight))
  }, [editingEvents])

  useEffect(() => {
    if (selectedDateRange) {
      const filteredEvents = events.filter((event) => {
        const eventStart = new Date(event.start);
        const rangeStartOriginal = new Date(selectedDateRange.start);

        // Check if the original rangeStart from the click info has time component
        if (selectedDateRange.start.includes("T")) {
          // Click was on a specific time slot
          let rangeStartForSlot = new Date(selectedDateRange.start);
          let rangeEndForSlot = new Date(rangeStartForSlot);
          rangeEndForSlot.setHours(rangeStartForSlot.getHours() + slotDurationHours);

          // Ensure event.start is within this specific slot [rangeStartForSlot, rangeEndForSlot)
          return eventStart >= rangeStartForSlot && eventStart < rangeEndForSlot;
        } else {
          // Click was on a whole day (or date range select)
          // For day clicks, rangeStartOriginal and rangeEndOriginal will be the same day, time 00:00:00
          const rangeStartDay = new Date(selectedDateRange.start);
          rangeStartDay.setHours(0, 0, 0, 0);

          const rangeEndDay = new Date(selectedDateRange.end);
          rangeEndDay.setHours(23, 59, 59, 999);

          return eventStart >= rangeStartDay && eventStart <= rangeEndDay;
        }
      })
      setEditingEvents([...filteredEvents])
      setGridKey(prevKey => prevKey + 1)
    } else {
      setEditingEvents([])
      setGridKey(prevKey => prevKey + 1)
    }
  }, [events, selectedDateRange, slotDurationHours])

  // Auto-save when events change
  // useEffect(() => {
  //   if (open && editingEvents.length >= 0) {
  //     const otherEvents = events.filter((event) => {
  //       if (!selectedDateRange) return true
  //       const eventDate = new Date(event.start).toISOString().split("T")[0]
  //       return !(eventDate >= selectedDateRange.start && eventDate <= selectedDateRange.end)
  //     })
  //     onSave([...otherEvents, ...editingEvents])
  //   }
  // }, [editingEvents])

  const handleAddEvent = () => {
    const newEvent: CalendarEvent = {
      id: Date.now().toString(),
      title: "New Event",
      start: selectedDateRange?.start ? `${selectedDateRange.start}T09:00:00` : new Date().toISOString(),
      type: "reservation",
      extendedProps: {
        customerName: "",
        phone: "",
        description: "",
        status: "pending",
      },
    }
    setEditingEvents((prev) => [...prev, newEvent])
  }

  const handleEditEvent = (id: string, field: string, value: string) => {
    setEditingEvents((prev) =>
      prev.map((event) => {
        if (event.id === id) {
          if (field.startsWith("extendedProps.")) {
            const propName = field.replace("extendedProps.", "")
            return {
              ...event,
              extendedProps: {
                ...event.extendedProps,
                [propName]: value,
              },
            }
          }
          if (field === "eventDate") {
            const time = new Date(event.start).toTimeString().slice(0, 8)
            return { ...event, start: `${value}T${time}` }
          }
          if (field === "eventTime") {
            const date = new Date(event.start).toISOString().split("T")[0]
            return { ...event, start: `${date}T${value}:00` }
          }
          return { ...event, [field]: value }
        }
        return event
      }),
    )
  }

  const formatDateRange = () => {
    if (!selectedDateRange) return ""

    const formatOptions: Intl.DateTimeFormatOptions = {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      ...(isRTL && { calendar: "islamic" }),
    };

    const startDate = new Date(selectedDateRange.start);

    if (selectedDateRange.start.includes("T")) {
      // Single slot selection
      const startTimeFormatted = startDate.toLocaleTimeString(isRTL ? "ar-SA" : "en-US", {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true,
        ...(isRTL && { calendar: "islamic" }),
      });
      const endDate = new Date(startDate);
      endDate.setHours(startDate.getHours() + slotDurationHours);
      const endTimeFormatted = endDate.toLocaleTimeString(isRTL ? "ar-SA" : "en-US", {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true,
      ...(isRTL && { calendar: "islamic" }),
      });
      const dayFormatted = startDate.toLocaleDateString(isRTL ? "ar-SA" : "en-US", formatOptions);
      return `${dayFormatted}, ${startTimeFormatted} - ${endTimeFormatted}`;
    } else {
      // Date or date range selection
      const startDateFormatted = startDate.toLocaleDateString(isRTL ? "ar-SA" : "en-US", formatOptions);
      const endDate = new Date(selectedDateRange.end);
      const endDateFormatted = endDate.toLocaleDateString(isRTL ? "ar-SA" : "en-US", formatOptions);
      
      if (selectedDateRange.start.split('T')[0] === selectedDateRange.end.split('T')[0]) {
        return startDateFormatted; // Single day
      } else {
        return `${startDateFormatted} - ${endDateFormatted}`; // Date range
      }
    }
  }

  // Only allow editing for non-cancelled reservations
  const isEditable = (event: CalendarEvent) => !event.extendedProps?.status || event.extendedProps.status !== 'cancelled';

  const handleClose = async (open: boolean) => {
    if (!open && editingEvents.length >= 0) {
      // Save when closing
      const otherEvents = events.filter((event) => {
        if (!selectedDateRange) return true
        const eventStart = new Date(event.start);
        if (selectedDateRange.start.includes("T")) {
            let rangeStartForSlot = new Date(selectedDateRange.start);
            let rangeEndForSlot = new Date(rangeStartForSlot);
            rangeEndForSlot.setHours(rangeStartForSlot.getHours() + slotDurationHours);
            return !(eventStart >= rangeStartForSlot && eventStart < rangeEndForSlot);
        } else {
            const rangeStartDay = new Date(selectedDateRange.start);
            rangeStartDay.setHours(0, 0, 0, 0);
            const rangeEndDay = new Date(selectedDateRange.end);
            rangeEndDay.setHours(23, 59, 59, 999);
            return !(eventStart >= rangeStartDay && eventStart <= rangeEndDay);
        }
      })
      // Call onSave for changed events
      await onSave([...otherEvents, ...editingEvents]);
    }
    onOpenChange(open)
  }

  // Define grid columns for Glide Data Grid
  const columns: GridColumn[] = [
    { title: isRTL ? "اسم العميل" : "Customer Name", width: 150 },
    { title: isRTL ? "الهاتف" : "Phone", width: 120 },
    { title: isRTL ? "تاريخ الحدث" : "Event Date", width: 120 },
    { title: isRTL ? "وقت الحدث" : "Event Time", width: 80 },
    { title: isRTL ? "نوع الحدث" : "Event Type", width: 120 },
  ]

  // Provide cell content for each cell
  const getCellContent = (cell: Item): TextCell => {
    const [col, row] = cell
    const event = editingEvents[row]
    const emptyCell: TextCell = { kind: GridCellKind.Text, data: "", displayData: "", allowOverlay: true }
    if (!event) return emptyCell
    switch (col) {
      case 0:
        return { kind: GridCellKind.Text, data: event.extendedProps.customerName || "", displayData: event.extendedProps.customerName || "", allowOverlay: true }
      case 1:
        return { kind: GridCellKind.Text, data: event.extendedProps.phone || "", displayData: event.extendedProps.phone || "", allowOverlay: true }
      case 2:
        const dateStr = new Date(event.start).toISOString().split("T")[0]
        return { kind: GridCellKind.Text, data: dateStr, displayData: dateStr, allowOverlay: true }
      case 3:
        const timeStr = new Date(event.start).toTimeString().slice(0, 5)
        return { kind: GridCellKind.Text, data: timeStr, displayData: timeStr, allowOverlay: true }
      case 4:
        return { kind: GridCellKind.Text, data: event.type, displayData: event.type, allowOverlay: true }
      default:
        return emptyCell
    }
  }

  // Handle cell edits and update events
  const onCellEdited = (cell: Item, newValue: EditableGridCell) => {
    const [col, row] = cell
    const textCell = newValue as TextCell
    const value = textCell.data
    const event = editingEvents[row]
    if (!event) return
    switch (col) {
      case 0:
        handleEditEvent(event.id, "extendedProps.customerName", value)
        break
      case 1:
        handleEditEvent(event.id, "extendedProps.phone", value)
        break
      case 2:
        handleEditEvent(event.id, "eventDate", value)
        break
      case 3:
        handleEditEvent(event.id, "eventTime", value)
        break
      case 4:
        handleEditEvent(event.id, "type", value)
        break
    }
  }

  // Handle adding new rows
  const onRowAppended = () => {
    const newEvent: CalendarEvent = {
      id: Date.now().toString(),
      title: "New Event",
      start: selectedDateRange?.start ? `${selectedDateRange.start}T09:00:00` : new Date().toISOString(),
      type: "reservation",
      extendedProps: {
        customerName: "",
        phone: "",
        description: "",
        status: "pending",
      },
    }
    setEditingEvents((prev) => [...prev, newEvent])
  }

  // Adaptive theme for dark/light mode
  const gridTheme = {
    accentColor: theme === 'dark' ? '#3b82f6' : '#2563eb',
    accentFg: '#ffffff',
    accentLight: theme === 'dark' ? '#1e40af' : '#dbeafe',
    textDark: theme === 'dark' ? '#f8fafc' : '#1e293b',
    textMedium: theme === 'dark' ? '#cbd5e1' : '#64748b',
    textLight: theme === 'dark' ? '#94a3b8' : '#94a3b8',
    textBubble: theme === 'dark' ? '#f8fafc' : '#1e293b',
    bgIconHeader: theme === 'dark' ? '#374151' : '#f1f5f9',
    fgIconHeader: theme === 'dark' ? '#d1d5db' : '#64748b',
    textHeader: theme === 'dark' ? '#f9fafb' : '#374151',
    textGroupHeader: theme === 'dark' ? '#e5e7eb' : '#4b5563',
    textHeaderSelected: theme === 'dark' ? '#ffffff' : '#000000',
    bgCell: theme === 'dark' ? '#1f2937' : '#ffffff',
    bgCellMedium: theme === 'dark' ? '#374151' : '#f8fafc',
    bgHeader: theme === 'dark' ? '#111827' : '#f8fafc',
    bgHeaderHasFocus: theme === 'dark' ? '#1f2937' : '#f1f5f9',
    bgHeaderHovered: theme === 'dark' ? '#374151' : '#e2e8f0',
    bgBubble: theme === 'dark' ? '#374151' : '#f1f5f9',
    bgBubbleSelected: theme === 'dark' ? '#1e40af' : '#3b82f6',
    bgSearchResult: theme === 'dark' ? '#fbbf24' : '#fcd34d',
    borderColor: theme === 'dark' ? '#374151' : '#e2e8f0',
    drilldownBorder: theme === 'dark' ? '#6b7280' : '#9ca3af',
    linkColor: theme === 'dark' ? '#60a5fa' : '#3b82f6',
    headerFontStyle: '600 13px',
    baseFontStyle: '13px',
    fontFamily: 'Inter, Roboto, -apple-system, BlinkMacSystemFont, avenir next, avenir, segoe ui, helvetica neue, helvetica, Ubuntu, noto, arial, sans-serif',
  }

  // Trailing row options for the plus sign
  const trailingRowOptions = {
    tint: true,
    sticky: true,
    hint: "", // Remove the "click to add" text
    themeOverride: {
      bgCell: theme === 'dark' ? '#374151' : '#f8fafc',
      textMedium: theme === 'dark' ? '#9ca3af' : '#6b7280',
      borderColor: theme === 'dark' ? '#4b5563' : '#d1d5db',
    }
  }

  return (
    <Drawer open={open} onOpenChange={handleClose}>
      <DrawerContent className="max-h-[95vh] flex flex-col p-4" style={{ height: `${Math.min(gridHeight + 200, typeof window !== 'undefined' ? window.innerHeight * 0.95 : 800)}px` }}>
        <DrawerHeader className="px-0 pb-4">
          <DrawerTitle className={`flex items-center gap-2 ${isRTL ? "flex-row-reverse" : ""}`}>
            {isRTL ? "محرر البيانات" : "Data Editor"} - {formatDateRange()}
            <span className="text-sm font-normal text-muted-foreground">
              ({editingEvents.length} {isRTL ? "أحداث" : "events"})
            </span>
          </DrawerTitle>
          <DrawerDescription>
            {isRTL ? "تحرير أحداث التقويم للفترة المحددة" : "Edit calendar events for the selected date range"}
          </DrawerDescription>
        </DrawerHeader>

        <div className="flex flex-col gap-4 flex-1 overflow-hidden">
          {/* Events Grid */}
          <div className="border rounded-lg overflow-hidden flex-1 w-full">
            <DataEditor
              key={gridKey}
              getCellContent={getCellContent}
              columns={columns}
              rows={editingEvents.length}
              onCellEdited={(cell, newValue) => {
                const row = cell[1];
                if (!freeRoam || isEditable(editingEvents[row])) {
                  onCellEdited(cell, newValue);
                }
              }}
              onRowAppended={onRowAppended}
              trailingRowOptions={trailingRowOptions}
              width="100%"
              height={gridHeight}
              rowMarkers="checkbox"
              theme={gridTheme}
              smoothScrollX
              smoothScrollY
              scaleToRem={true}
            />
          </div>
        </div>
      </DrawerContent>
    </Drawer>
  )
}
