/* FullCalendar Custom Styles */
.fc-event-past {
  opacity: 0.6;
}

.fc-event-time {
  font-style: italic;
}

.fc-event.conversation-event .fc-event-time {
  display: none;
}

.fc-toolbar-title {
  font-size: 2rem;
}

/* Change cursor to pointer for all calendar events */
.fc-event {
  cursor: pointer !important;
}

.fc-scrollgrid {
  border-radius: 16px !important;
  overflow: hidden !important;
}

.fc-scrollgrid-section-header > td {
  border-radius: 0 !important;
}

.fc-scrollgrid-section-body > td {
  border-radius: 0 !important;
}

.fc-col-header-cell {
  border-radius: 0 !important;
}

.fc-daygrid-day {
  border-radius: 0 !important;
}

.fc .fc-toolbar-title {
  font-size: 1.25rem !important;
  margin: 0;
  padding: 20px 0 0px 20px;
}

.fc .fc-button {
  background-color: #006082 !important;
  border-color: #006082 !important;
}

.fc-day-today {
  background-color: #edf5f7 !important;
}

[data-theme="dark"] .fc-day-today,
.dark .fc-day-today {
  background-color: #1e293b !important;
}

.fc-theme-standard td,
.fc-theme-standard th {
  border: 1px solid #e5e7eb !important;
}

.dark .fc-theme-standard td,
.dark .fc-theme-standard th {
  border: 1px solid #374151 !important;
}

.fc-scrollgrid-sync-table {
  border-collapse: separate !important;
  border-spacing: 0 !important;
}

.fc-day-other {
  background: #FAFAFB !important;
}

.dark .fc-day-other {
  background: #111827 !important;
}

.fc-daygrid-day-frame {
  position: relative !important;
  overflow: hidden !important;
}

.fc-daygrid-day-bg {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
}

.fc .fc-button .fc-icon {
  font-size: 0.875rem !important;
}

a.fc-col-header-cell-cushion {
  font-size: .85em !important;
  line-height: 2.2rem !important;
}

.fc .fc-daygrid-day-top {
  flex-direction: inherit !important;
  padding: 5px !important;
  font-size: .75em !important;
  color: #6b7280 !important;
}

.dark .fc .fc-daygrid-day-top {
  color: #9ca3af !important;
}

.fc .fc-button-primary:disabled {
  background-color: #eeeeee !important;
  color: black !important;
  border-color: #eeeeee !important;
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
  text-transform: capitalize !important;
}

.dark .fc .fc-button-primary:disabled {
  background-color: #374151 !important;
  color: white !important;
  border-color: #374151 !important;
}

/* Dark mode styles for calendar */
.dark .fc {
  color: #f9fafb;
}

.dark .fc-theme-standard th {
  background-color: #374151;
  border-color: #4b5563;
}

.dark .fc-theme-standard td {
  background-color: #1f2937;
  border-color: #374151;
}

.dark .fc-col-header-cell-cushion {
  color: #f9fafb;
}

.dark .fc-daygrid-day-number {
  color: #d1d5db;
}

.dark .fc-button-primary {
  background-color: #006082 !important;
  border-color: #006082 !important;
  color: white !important;
}

.dark .fc-button-primary:hover {
  background-color: #004d66 !important;
  border-color: #004d66 !important;
}

/* RTL-specific styles */
.fc[dir="rtl"] .fc-toolbar-title {
  padding: 20px 20px 0px 0 !important;
}

.fc[dir="rtl"] .fc-button-group {
  direction: rtl !important;
}

.fc[dir="rtl"] .fc-daygrid-day-top {
  direction: rtl !important;
}

.fc[dir="rtl"] .fc-col-header-cell-cushion {
  direction: rtl !important;
}

.fc[dir="rtl"] .fc-event {
  direction: rtl !important;
}

/* Calendar cell hover styles - Different from background */
.fc-daygrid-day:hover {
  background-color: #f3f4f6 !important;
  cursor: pointer !important;
}

.dark .fc-daygrid-day:hover {
  background-color: #374151 !important;
}

.fc-timegrid-slot:hover {
  background-color: #e0f2fe !important;
  cursor: pointer !important;
  z-index: 2 !important;
  box-shadow: 0 0 0 2px #38bdf8 inset !important;
}

.dark .fc-timegrid-slot:hover {
  background-color: #374151 !important;
}

.fc-timeGridWeek-view .fc-scroller {
  height: auto !important;
  overflow-y: auto !important;
}

/* Remove empty space at top of week view */
.fc-timeGridWeek-view .fc-scrollgrid-section-header {
  height: auto !important;
}

.fc-timeGridWeek-view .fc-col-header {
  padding: 8px 0 !important;
}

/* Ensure week view has proper row heights */
.fc-timeGridWeek-view .fc-timegrid-slot {
  height: 3em !important;
  min-height: 3em !important;
}

.fc-timeGridWeek-view .fc-timegrid-axis {
  width: 4em !important;
}



/* Better spacing for event content */
.fc-event-main {
  padding: 2px 4px !important;
}

.fc-event-title {
  font-size: 0.85em !important;
  line-height: 1.2 !important;
}

.fc-event-time {
  font-size: 0.75em !important;
  line-height: 1.1 !important;
}

/* Enhanced conversation event styling (matching Python implementation) */
.fc-event.conversation-event {
  background-color: #EDAE49 !important;
  border-color: #EDAE49 !important;
  color: #1f2937 !important;
}

.dark .fc-event.conversation-event {
  color: #ffffff !important;
}

/* Hide time display for conversation events (matching Python implementation) */
.fc-event.conversation-event .fc-event-time {
  display: none !important;
}

/* Cancelled reservation styling (matching Python implementation) */
.fc-event[data-cancelled="true"] {
  background-color: #e5e1e0 !important;
  border-color: #e5e1e0 !important;
  color: #908584 !important;
}

/* Past event styling (matching Python implementation) */
.fc-event-past {
  opacity: 0.6 !important;
}

/* Performance optimizations */
.fc {
  will-change: auto !important;
  transform: translateZ(0) !important;
}

.fc-scrollgrid {
  will-change: auto !important;
}

.fc-view-harness {
  will-change: auto !important;
} 