# This token expires after 24 hours. You can get a new one from the your Meta App Dashboard > API Setup
# Or you can create an access token at the System User level from the Meta Business Settings: https://business.facebook.com/settings/system-users
TIMEZONE = ""
APP_URL="" # Replace with your app URL (e.g, 16.121.222.111:8501)

ACCESS_TOKEN="" # Replace with your WhatsApp access token
# You can get a new one from the your Meta App Dashboard > API Setup
APP_ID="" # Replace with your WhatsApp app ID, You can get a new one from the your Meta App Dashboard > API Setup
# Or you can create an access token at the System User level from the Meta Business Settings: https://business.facebook.com/settings/system-users
APP_SECRET="" 
VERSION="" 
PHONE_NUMBER_ID="" 
VERIFY_TOKEN="" 

SYSTEM_PROMPT= "" # Replace with your system prompt (Only if using Anthropic Claude)

# OpenAI
OPENAI_API_KEY="" # Replace with your OpenAI API key (Only if using OpenAI GPT)
OPENAI_ASSISTANT_ID="" # Replace with your OpenAI Assistant ID (Only if using OpenAI GPT)
VEC_STORE_ID = "" # Replace with your OpenAI Assistant ID (Only if using OpenAI GPT, and you have a vector store attached to your assistant)

# Anthropic
ANTHROPIC_API_KEY="" # Replace with your Anthropic API key (Only if using Anthropic Claude)

BUSINESS_LATITUDE = 32.396617840878587 # Replace with your business latitude (e.g, 21.397677880868557)

BUSINESS_LONGITUDE = 11.78361301228732 # Replace with your business longitude (e.g, 39.77371602208732)
BUSINESS_NAME = "" # Replace with your business name (e.g, "My Business")
BUSINESS_ADDRESS = "" # Replace with your business address (e.g, "123 Main St, City, Country")

VACATION_START_DATES = "" # Replace with your vacation start dates (e.g, "2023-10-01,2023-10-15")
# You can set the vacation start dates in the format "YYYY-MM-DD" separated by commas.
VACATION_DURATIONS = "" # Replace with your vacation durations (e.g, "7,14")
VACATION_MESSAGE = "" # Replace with your vacation message (e.g, "We are on vacation from {start_date} to {end_date}. We will get back to you as soon as possible.")
UNSUPPORTED_MEDIA_MESSAGE = "" # Replace with the message you want the customer to recieve if they sent a photo or audio (can't be processed by current design).

# Discord Webhook for Alertmanager notifications
DISCORD_WEBHOOK="" # e.g. https://discord.com/api/webhooks/xxx/yyy