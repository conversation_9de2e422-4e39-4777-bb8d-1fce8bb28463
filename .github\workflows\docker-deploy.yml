name: Docker Build, Push to Docker Hub & Deploy to EC2

on:
  push:
    branches:
      - main

env:
  DOCKER_HUB_USERNAME: dahshury
  BACKEND_REPO: python-whatsapp-bot-backend
  FRONTEND_REPO: python-whatsapp-bot-frontend

jobs:
  build_and_push:
    environment: ec2
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ env.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_TOKEN }}

      - name: Build and push backend image
        run: |
          docker build -t ${{ env.DOCKER_HUB_USERNAME }}/${{ env.BACKEND_REPO }}:latest -f Dockerfile.backend .
          docker push ${{ env.DOCKER_HUB_USERNAME }}/${{ env.BACKEND_REPO }}:latest

      - name: Build and push frontend image
        run: |
          docker build -t ${{ env.DOCKER_HUB_USERNAME }}/${{ env.FRONTEND_REPO }}:latest -f Dockerfile.frontend .
          docker push ${{ env.DOCKER_HUB_USERNAME }}/${{ env.FRONTEND_REPO }}:latest

      - name: Setup directory on EC2
        uses: appleboy/ssh-action@v1
        with:
          host: ${{ secrets.EC2_HOST }}
          username: ${{ secrets.EC2_USER }}
          key: ${{ secrets.EC2_SSH_KEY }}
          script: |
            # Create base directory if it doesn't exist
            mkdir -p /home/<USER>/python-whatsapp-bot
            
            # Remove existing directories that might have permission issues
            sudo rm -rf /home/<USER>/python-whatsapp-bot/prometheus
            
            # Create fresh directories with proper permissions
            mkdir -p /home/<USER>/python-whatsapp-bot/prometheus
            
            # Ensure proper ownership (use sudo)
            sudo chown -R ubuntu:ubuntu /home/<USER>/python-whatsapp-bot
            # Create swap file if not exists (1G)
            if ! sudo swapon --show | grep -q '/swapfile'; then
              sudo fallocate -l 1G /swapfile
              sudo chmod 600 /swapfile
              sudo mkswap /swapfile
              sudo swapon /swapfile
              echo '/swapfile none swap sw 0 0' | sudo tee -a /etc/fstab
            fi

      - name: Copy deployment files to EC2
        uses: appleboy/scp-action@master
        with:
          host: ${{ secrets.EC2_HOST }}
          username: ${{ secrets.EC2_USER }}
          key: ${{ secrets.EC2_SSH_KEY }}
          source: "docker-compose.prod.yml,prometheus/**,.streamlit/**,users.yaml"
          target: "/home/<USER>/python-whatsapp-bot"
          overwrite: true

      - name: Deploy to EC2
        uses: appleboy/ssh-action@v1
        with:
          host: ${{ secrets.EC2_HOST }}
          username: ${{ secrets.EC2_USER }}
          key: ${{ secrets.EC2_SSH_KEY }}
          timeout: 600s
          script: |
            # Retry the deployment steps up to 3 times
            for i in {1..3}; do
              echo "Deploy attempt $i..."
              set -e
              # Login to Docker Hub
              echo ${{ secrets.DOCKER_HUB_TOKEN }} | docker login -u ${{ env.DOCKER_HUB_USERNAME }} --password-stdin

              # Move to project directory
              cd /home/<USER>/python-whatsapp-bot

              # Fix permissions if needed (use sudo)
              if [ ! -w prometheus ]; then
                echo "Fixing prometheus directory permissions..."
                sudo chown -R ubuntu:ubuntu prometheus
              fi

              # Warn if .env doesn't exist
              if [ ! -f .env ]; then
                echo "WARNING: .env not found"
              fi
              # Warn if threads_db.sqlite doesn't exist
              if [ ! -f threads_db.sqlite ]; then
                echo "WARNING: threads_db.sqlite not found"
              fi

              # First, clean up space BEFORE pulling new images
              
              # Get container IDs but don't stop them yet
              running_containers=$(docker ps -q)
              
              # Clean all unused volumes (not used by running containers)
              docker volume prune -f
              
              # Clean all unused networks
              docker network prune -f
              
              # Remove all dangling images (not used and not tagged)
              docker image prune -f
              
              # Clean build cache
              docker builder prune -f
              
              # Now pull the new images - this ensures we have space for them
              echo "Pulling updated images..."
              docker compose -f docker-compose.prod.yml pull

              # Now safe to stop running containers
              if [ -n "$running_containers" ]; then
                echo "Stopping running containers..."
                docker stop $running_containers
              fi
              
              # Start new containers
              echo "Starting updated services..."
              docker compose -f docker-compose.prod.yml up -d --remove-orphans
              
              # After new containers are running, remove ALL unused images
              echo "Removing ALL unused images to reclaim space..."
              docker image prune -a -f
              
              # Final system prune to clean everything else
              echo "Final system cleanup..."
              docker system prune -f
              
              echo "Deployment succeeded"
              exit 0
            done
            echo "Deployment failed after 3 attempts"
            exit 1 