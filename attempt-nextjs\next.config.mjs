/** @type {import('next').NextConfig} */
const nextConfig = {
  env: {
    // Python backend URL - can be overridden by environment variables
    PYTHON_BACKEND_URL: process.env.PYTHON_BACKEND_URL || process.env.BACKEND_URL || 'http://localhost:8000',
    NEXT_PUBLIC_TIMEZONE: process.env.NEXT_PUBLIC_TIMEZONE || process.env.TIMEZONE || 'UTC',
  },
  
  // Webpack configuration to fix Glide Data Grid module resolution issues
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // Fix for Glide Data Grid module resolution
    config.resolve.alias = {
      ...config.resolve.alias,
      // Fix the math.js module resolution issue
      '@glideapps/glide-data-grid/dist/esm/internal/common/math.js': '@glideapps/glide-data-grid/dist/esm/internal/common/math',
    }
    
    // Add fallbacks for Node.js modules
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
      net: false,
      tls: false,
      crypto: false,
    }
    
    // Ensure proper module resolution for .mjs files
    config.module.rules.push({
      test: /\.mjs$/,
      include: /node_modules/,
      type: "javascript/auto",
    })
    
    return config
  },

  // Transpile Glide Data Grid for compatibility with Next.js
  transpilePackages: ['@glideapps/glide-data-grid'],
  
  // Disable strict mode temporarily for Glide Data Grid compatibility
  reactStrictMode: false
};

export default nextConfig;
