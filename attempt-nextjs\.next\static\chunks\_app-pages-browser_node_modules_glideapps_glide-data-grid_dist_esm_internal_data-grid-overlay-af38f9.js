"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_glideapps_glide-data-grid_dist_esm_internal_data-grid-overlay-af38f9"],{

/***/ "(app-pages-browser)/./node_modules/@glideapps/glide-data-grid/dist/esm/internal/data-grid-overlay-editor/private/number-overlay-editor-style.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/@glideapps/glide-data-grid/dist/esm/internal/data-grid-overlay-editor/private/number-overlay-editor-style.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NumberOverlayEditorStyle: () => (/* binding */ NumberOverlayEditorStyle)\n/* harmony export */ });\n/* harmony import */ var _linaria_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @linaria/react */ \"(app-pages-browser)/./node_modules/@linaria/react/dist/index.mjs\");\n\nconst NumberOverlayEditorStyle = /*#__PURE__*/ (0,_linaria_react__WEBPACK_IMPORTED_MODULE_0__.styled)('div')({\n    name: \"NumberOverlayEditorStyle\",\n    class: \"gdg-n15fjm3e\",\n    propsAsIs: false\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AZ2xpZGVhcHBzL2dsaWRlLWRhdGEtZ3JpZC9kaXN0L2VzbS9pbnRlcm5hbC9kYXRhLWdyaWQtb3ZlcmxheS1lZGl0b3IvcHJpdmF0ZS9udW1iZXItb3ZlcmxheS1lZGl0b3Itc3R5bGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBd0M7QUFFakMsTUFBTSx3QkFBd0IsR0FBRyxNQUFNLENBQUMsR0FBRyIsInNvdXJjZXMiOlsiRTpcXHNyY1xcaW50ZXJuYWxcXGRhdGEtZ3JpZC1vdmVybGF5LWVkaXRvclxccHJpdmF0ZVxcbnVtYmVyLW92ZXJsYXktZWRpdG9yLXN0eWxlLnRzeCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@glideapps/glide-data-grid/dist/esm/internal/data-grid-overlay-editor/private/number-overlay-editor-style.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@glideapps/glide-data-grid/dist/esm/internal/data-grid-overlay-editor/private/number-overlay-editor.js":
/*!*****************************************************************************************************************************!*\
  !*** ./node_modules/@glideapps/glide-data-grid/dist/esm/internal/data-grid-overlay-editor/private/number-overlay-editor.js ***!
  \*****************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _number_overlay_editor_style_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./number-overlay-editor-style.js */ \"(app-pages-browser)/./node_modules/@glideapps/glide-data-grid/dist/esm/internal/data-grid-overlay-editor/private/number-overlay-editor-style.js\");\n/* harmony import */ var react_number_format__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-number-format */ \"(app-pages-browser)/./node_modules/react-number-format/dist/react-number-format.es.js\");\nvar _s = $RefreshSig$();\n\n\n\nfunction getDecimalSeparator() {\n    var _Intl_NumberFormat_formatToParts_find, _Intl_NumberFormat_formatToParts, _Intl_NumberFormat;\n    const numberWithDecimalSeparator = 1.1;\n    const result = (_Intl_NumberFormat = Intl.NumberFormat()) === null || _Intl_NumberFormat === void 0 ? void 0 : (_Intl_NumberFormat_formatToParts = _Intl_NumberFormat.formatToParts(numberWithDecimalSeparator)) === null || _Intl_NumberFormat_formatToParts === void 0 ? void 0 : (_Intl_NumberFormat_formatToParts_find = _Intl_NumberFormat_formatToParts.find((part)=>part.type === \"decimal\")) === null || _Intl_NumberFormat_formatToParts_find === void 0 ? void 0 : _Intl_NumberFormat_formatToParts_find.value;\n    return result !== null && result !== void 0 ? result : \".\";\n}\nfunction getThousandSeprator() {\n    return getDecimalSeparator() === \".\" ? \",\" : \".\";\n}\nconst NumberOverlayEditor = (p)=>{\n    _s();\n    const { value, onChange, disabled, highlight, validatedSelection, fixedDecimals, allowNegative, thousandSeparator, decimalSeparator } = p;\n    const inputRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n    react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect({\n        \"NumberOverlayEditor.useLayoutEffect\": ()=>{\n            if (validatedSelection !== undefined) {\n                var _inputRef_current;\n                const range = typeof validatedSelection === \"number\" ? [\n                    validatedSelection,\n                    null\n                ] : validatedSelection;\n                (_inputRef_current = inputRef.current) === null || _inputRef_current === void 0 ? void 0 : _inputRef_current.setSelectionRange(range[0], range[1]);\n            }\n        }\n    }[\"NumberOverlayEditor.useLayoutEffect\"], [\n        validatedSelection\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(_number_overlay_editor_style_js__WEBPACK_IMPORTED_MODULE_1__.NumberOverlayEditorStyle, null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react_number_format__WEBPACK_IMPORTED_MODULE_2__.NumericFormat, {\n        autoFocus: true,\n        getInputRef: inputRef,\n        className: \"gdg-input\",\n        onFocus: (e)=>e.target.setSelectionRange(highlight ? 0 : e.target.value.length, e.target.value.length),\n        disabled: disabled === true,\n        decimalScale: fixedDecimals,\n        allowNegative: allowNegative,\n        thousandSeparator: thousandSeparator !== null && thousandSeparator !== void 0 ? thousandSeparator : getThousandSeprator(),\n        decimalSeparator: decimalSeparator !== null && decimalSeparator !== void 0 ? decimalSeparator : getDecimalSeparator(),\n        value: Object.is(value, -0) ? \"-\" : value !== null && value !== void 0 ? value : \"\",\n        // decimalScale={3}\n        // prefix={\"$\"}\n        onValueChange: onChange\n    }));\n};\n_s(NumberOverlayEditor, \"4ccH288x/j4aKId6NWrmpLGxvTs=\");\n_c = NumberOverlayEditor;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NumberOverlayEditor); //# sourceMappingURL=number-overlay-editor.js.map\nvar _c;\n$RefreshReg$(_c, \"NumberOverlayEditor\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@glideapps/glide-data-grid/dist/esm/internal/data-grid-overlay-editor/private/number-overlay-editor.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-number-format/dist/react-number-format.es.js":
/*!*************************************************************************!*\
  !*** ./node_modules/react-number-format/dist/react-number-format.es.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NumberFormatBase: () => (/* binding */ NumberFormatBase),\n/* harmony export */   NumericFormat: () => (/* binding */ NumericFormat),\n/* harmony export */   PatternFormat: () => (/* binding */ PatternFormat),\n/* harmony export */   getNumericCaretBoundary: () => (/* binding */ getCaretBoundary),\n/* harmony export */   getPatternCaretBoundary: () => (/* binding */ getCaretBoundary$1),\n/* harmony export */   numericFormatter: () => (/* binding */ format),\n/* harmony export */   patternFormatter: () => (/* binding */ format$1),\n/* harmony export */   removeNumericFormat: () => (/* binding */ removeFormatting),\n/* harmony export */   removePatternFormat: () => (/* binding */ removeFormatting$1),\n/* harmony export */   useNumericFormat: () => (/* binding */ useNumericFormat),\n/* harmony export */   usePatternFormat: () => (/* binding */ usePatternFormat)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * react-number-format - 5.4.4\n * Author : Sudhanshu Yadav\n * Copyright (c) 2016, 2025 to Sudhanshu Yadav, released under the MIT license.\n * https://github.com/s-yadav/react-number-format\n */\n\n\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nfunction __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) { if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        { t[p] = s[p]; } }\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        { for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                { t[p[i]] = s[p[i]]; }\r\n        } }\r\n    return t;\r\n}\n\nvar SourceType;\n(function (SourceType) {\n    SourceType[\"event\"] = \"event\";\n    SourceType[\"props\"] = \"prop\";\n})(SourceType || (SourceType = {}));\n\n// basic noop function\nfunction noop() { }\nfunction memoizeOnce(cb) {\n    var lastArgs;\n    var lastValue = undefined;\n    return function () {\n        var args = [], len = arguments.length;\n        while ( len-- ) args[ len ] = arguments[ len ];\n\n        if (lastArgs &&\n            args.length === lastArgs.length &&\n            args.every(function (value, index) { return value === lastArgs[index]; })) {\n            return lastValue;\n        }\n        lastArgs = args;\n        lastValue = cb.apply(void 0, args);\n        return lastValue;\n    };\n}\nfunction charIsNumber(char) {\n    return !!(char || '').match(/\\d/);\n}\nfunction isNil(val) {\n    return val === null || val === undefined;\n}\nfunction isNanValue(val) {\n    return typeof val === 'number' && isNaN(val);\n}\nfunction isNotValidValue(val) {\n    return isNil(val) || isNanValue(val) || (typeof val === 'number' && !isFinite(val));\n}\nfunction escapeRegExp(str) {\n    return str.replace(/[-[\\]/{}()*+?.\\\\^$|]/g, '\\\\$&');\n}\nfunction getThousandsGroupRegex(thousandsGroupStyle) {\n    switch (thousandsGroupStyle) {\n        case 'lakh':\n            return /(\\d+?)(?=(\\d\\d)+(\\d)(?!\\d))(\\.\\d+)?/g;\n        case 'wan':\n            return /(\\d)(?=(\\d{4})+(?!\\d))/g;\n        case 'thousand':\n        default:\n            return /(\\d)(?=(\\d{3})+(?!\\d))/g;\n    }\n}\nfunction applyThousandSeparator(str, thousandSeparator, thousandsGroupStyle) {\n    var thousandsGroupRegex = getThousandsGroupRegex(thousandsGroupStyle);\n    var index = str.search(/[1-9]/);\n    index = index === -1 ? str.length : index;\n    return (str.substring(0, index) +\n        str.substring(index, str.length).replace(thousandsGroupRegex, '$1' + thousandSeparator));\n}\nfunction usePersistentCallback(cb) {\n    var callbackRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(cb);\n    // keep the callback ref upto date\n    callbackRef.current = cb;\n    /**\n     * initialize a persistent callback which never changes\n     * through out the component lifecycle\n     */\n    var persistentCbRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(function () {\n        var args = [], len = arguments.length;\n        while ( len-- ) args[ len ] = arguments[ len ];\n\n        return callbackRef.current.apply(callbackRef, args);\n    });\n    return persistentCbRef.current;\n}\n//spilt a float number into different parts beforeDecimal, afterDecimal, and negation\nfunction splitDecimal(numStr, allowNegative) {\n    if ( allowNegative === void 0 ) allowNegative = true;\n\n    var hasNegation = numStr[0] === '-';\n    var addNegation = hasNegation && allowNegative;\n    numStr = numStr.replace('-', '');\n    var parts = numStr.split('.');\n    var beforeDecimal = parts[0];\n    var afterDecimal = parts[1] || '';\n    return {\n        beforeDecimal: beforeDecimal,\n        afterDecimal: afterDecimal,\n        hasNegation: hasNegation,\n        addNegation: addNegation,\n    };\n}\nfunction fixLeadingZero(numStr) {\n    if (!numStr)\n        { return numStr; }\n    var isNegative = numStr[0] === '-';\n    if (isNegative)\n        { numStr = numStr.substring(1, numStr.length); }\n    var parts = numStr.split('.');\n    var beforeDecimal = parts[0].replace(/^0+/, '') || '0';\n    var afterDecimal = parts[1] || '';\n    return (\"\" + (isNegative ? '-' : '') + beforeDecimal + (afterDecimal ? (\".\" + afterDecimal) : ''));\n}\n/**\n * limit decimal numbers to given scale\n * Not used .fixedTo because that will break with big numbers\n */\nfunction limitToScale(numStr, scale, fixedDecimalScale) {\n    var str = '';\n    var filler = fixedDecimalScale ? '0' : '';\n    for (var i = 0; i <= scale - 1; i++) {\n        str += numStr[i] || filler;\n    }\n    return str;\n}\nfunction repeat(str, count) {\n    return Array(count + 1).join(str);\n}\nfunction toNumericString(num) {\n    var _num = num + ''; // typecast number to string\n    // store the sign and remove it from the number.\n    var sign = _num[0] === '-' ? '-' : '';\n    if (sign)\n        { _num = _num.substring(1); }\n    // split the number into cofficient and exponent\n    var ref = _num.split(/[eE]/g);\n    var coefficient = ref[0];\n    var exponent = ref[1];\n    // covert exponent to number;\n    exponent = Number(exponent);\n    // if there is no exponent part or its 0, return the coffiecient with sign\n    if (!exponent)\n        { return sign + coefficient; }\n    coefficient = coefficient.replace('.', '');\n    /**\n     * for scientific notation the current decimal index will be after first number (index 0)\n     * So effective decimal index will always be 1 + exponent value\n     */\n    var decimalIndex = 1 + exponent;\n    var coffiecientLn = coefficient.length;\n    if (decimalIndex < 0) {\n        // if decimal index is less then 0 add preceding 0s\n        // add 1 as join will have\n        coefficient = '0.' + repeat('0', Math.abs(decimalIndex)) + coefficient;\n    }\n    else if (decimalIndex >= coffiecientLn) {\n        // if decimal index is less then 0 add leading 0s\n        coefficient = coefficient + repeat('0', decimalIndex - coffiecientLn);\n    }\n    else {\n        // else add decimal point at proper index\n        coefficient =\n            (coefficient.substring(0, decimalIndex) || '0') + '.' + coefficient.substring(decimalIndex);\n    }\n    return sign + coefficient;\n}\n/**\n * This method is required to round prop value to given scale.\n * Not used .round or .fixedTo because that will break with big numbers\n */\nfunction roundToPrecision(numStr, scale, fixedDecimalScale) {\n    //if number is empty don't do anything return empty string\n    if (['', '-'].indexOf(numStr) !== -1)\n        { return numStr; }\n    var shouldHaveDecimalSeparator = (numStr.indexOf('.') !== -1 || fixedDecimalScale) && scale;\n    var ref = splitDecimal(numStr);\n    var beforeDecimal = ref.beforeDecimal;\n    var afterDecimal = ref.afterDecimal;\n    var hasNegation = ref.hasNegation;\n    var floatValue = parseFloat((\"0.\" + (afterDecimal || '0')));\n    var floatValueStr = afterDecimal.length <= scale ? (\"0.\" + afterDecimal) : floatValue.toFixed(scale);\n    var roundedDecimalParts = floatValueStr.split('.');\n    var intPart = beforeDecimal;\n    // if we have cary over from rounding decimal part, add that on before decimal\n    if (beforeDecimal && Number(roundedDecimalParts[0])) {\n        intPart = beforeDecimal\n            .split('')\n            .reverse()\n            .reduce(function (roundedStr, current, idx) {\n            if (roundedStr.length > idx) {\n                return ((Number(roundedStr[0]) + Number(current)).toString() +\n                    roundedStr.substring(1, roundedStr.length));\n            }\n            return current + roundedStr;\n        }, roundedDecimalParts[0]);\n    }\n    var decimalPart = limitToScale(roundedDecimalParts[1] || '', scale, fixedDecimalScale);\n    var negation = hasNegation ? '-' : '';\n    var decimalSeparator = shouldHaveDecimalSeparator ? '.' : '';\n    return (\"\" + negation + intPart + decimalSeparator + decimalPart);\n}\n/** set the caret positon in an input field **/\nfunction setCaretPosition(el, caretPos) {\n    el.value = el.value;\n    // ^ this is used to not only get 'focus', but\n    // to make sure we don't have it everything -selected-\n    // (it causes an issue in chrome, and having it doesn't hurt any other browser)\n    if (el !== null) {\n        /* @ts-ignore */\n        if (el.createTextRange) {\n            /* @ts-ignore */\n            var range = el.createTextRange();\n            range.move('character', caretPos);\n            range.select();\n            return true;\n        }\n        // (el.selectionStart === 0 added for Firefox bug)\n        if (el.selectionStart || el.selectionStart === 0) {\n            el.focus();\n            el.setSelectionRange(caretPos, caretPos);\n            return true;\n        }\n        // fail city, fortunately this never happens (as far as I've tested) :)\n        el.focus();\n        return false;\n    }\n}\n/**\n * TODO: remove dependency of findChangeRange, findChangedRangeFromCaretPositions is better way to find what is changed\n * currently this is mostly required by test and isCharacterSame util\n * Given previous value and newValue it returns the index\n * start - end to which values have changed.\n * This function makes assumption about only consecutive\n * characters are changed which is correct assumption for caret input.\n */\nvar findChangeRange = memoizeOnce(function (prevValue, newValue) {\n    var i = 0, j = 0;\n    var prevLength = prevValue.length;\n    var newLength = newValue.length;\n    while (prevValue[i] === newValue[i] && i < prevLength)\n        { i++; }\n    //check what has been changed from last\n    while (prevValue[prevLength - 1 - j] === newValue[newLength - 1 - j] &&\n        newLength - j > i &&\n        prevLength - j > i) {\n        j++;\n    }\n    return {\n        from: { start: i, end: prevLength - j },\n        to: { start: i, end: newLength - j },\n    };\n});\nvar findChangedRangeFromCaretPositions = function (lastCaretPositions, currentCaretPosition) {\n    var startPosition = Math.min(lastCaretPositions.selectionStart, currentCaretPosition);\n    return {\n        from: { start: startPosition, end: lastCaretPositions.selectionEnd },\n        to: { start: startPosition, end: currentCaretPosition },\n    };\n};\n/*\n  Returns a number whose value is limited to the given range\n*/\nfunction clamp(num, min, max) {\n    return Math.min(Math.max(num, min), max);\n}\nfunction geInputCaretPosition(el) {\n    /*Max of selectionStart and selectionEnd is taken for the patch of pixel and other mobile device caret bug*/\n    return Math.max(el.selectionStart, el.selectionEnd);\n}\nfunction addInputMode() {\n    return (typeof navigator !== 'undefined' &&\n        !(navigator.platform && /iPhone|iPod/.test(navigator.platform)));\n}\nfunction getDefaultChangeMeta(value) {\n    return {\n        from: {\n            start: 0,\n            end: 0,\n        },\n        to: {\n            start: 0,\n            end: value.length,\n        },\n        lastValue: '',\n    };\n}\nfunction getMaskAtIndex(mask, index) {\n    if ( mask === void 0 ) mask = ' ';\n\n    if (typeof mask === 'string') {\n        return mask;\n    }\n    return mask[index] || ' ';\n}\nfunction defaultIsCharacterSame(ref) {\n    var currentValue = ref.currentValue;\n    var formattedValue = ref.formattedValue;\n    var currentValueIndex = ref.currentValueIndex;\n    var formattedValueIndex = ref.formattedValueIndex;\n\n    return currentValue[currentValueIndex] === formattedValue[formattedValueIndex];\n}\nfunction getCaretPosition(newFormattedValue, lastFormattedValue, curValue, curCaretPos, boundary, isValidInputCharacter, \n/**\n * format function can change the character, the caret engine relies on mapping old value and new value\n * In such case if character is changed, parent can tell which chars are equivalent\n * Some example, all allowedDecimalCharacters are updated to decimalCharacters, 2nd case if user is coverting\n * number to different numeric system.\n */\nisCharacterSame) {\n    if ( isCharacterSame === void 0 ) isCharacterSame = defaultIsCharacterSame;\n\n    /**\n     * if something got inserted on empty value, add the formatted character before the current value,\n     * This is to avoid the case where typed character is present on format characters\n     */\n    var firstAllowedPosition = boundary.findIndex(function (b) { return b; });\n    var prefixFormat = newFormattedValue.slice(0, firstAllowedPosition);\n    if (!lastFormattedValue && !curValue.startsWith(prefixFormat)) {\n        lastFormattedValue = prefixFormat;\n        curValue = prefixFormat + curValue;\n        curCaretPos = curCaretPos + prefixFormat.length;\n    }\n    var curValLn = curValue.length;\n    var formattedValueLn = newFormattedValue.length;\n    // create index map\n    var addedIndexMap = {};\n    var indexMap = new Array(curValLn);\n    for (var i = 0; i < curValLn; i++) {\n        indexMap[i] = -1;\n        for (var j = 0, jLn = formattedValueLn; j < jLn; j++) {\n            var isCharSame = isCharacterSame({\n                currentValue: curValue,\n                lastValue: lastFormattedValue,\n                formattedValue: newFormattedValue,\n                currentValueIndex: i,\n                formattedValueIndex: j,\n            });\n            if (isCharSame && addedIndexMap[j] !== true) {\n                indexMap[i] = j;\n                addedIndexMap[j] = true;\n                break;\n            }\n        }\n    }\n    /**\n     * For current caret position find closest characters (left and right side)\n     * which are properly mapped to formatted value.\n     * The idea is that the new caret position will exist always in the boundary of\n     * that mapped index\n     */\n    var pos = curCaretPos;\n    while (pos < curValLn && (indexMap[pos] === -1 || !isValidInputCharacter(curValue[pos]))) {\n        pos++;\n    }\n    // if the caret position is on last keep the endIndex as last for formatted value\n    var endIndex = pos === curValLn || indexMap[pos] === -1 ? formattedValueLn : indexMap[pos];\n    pos = curCaretPos - 1;\n    while (pos > 0 && indexMap[pos] === -1)\n        { pos--; }\n    var startIndex = pos === -1 || indexMap[pos] === -1 ? 0 : indexMap[pos] + 1;\n    /**\n     * case where a char is added on suffix and removed from middle, example 2sq345 becoming $2,345 sq\n     * there is still a mapping but the order of start index and end index is changed\n     */\n    if (startIndex > endIndex)\n        { return endIndex; }\n    /**\n     * given the current caret position if it closer to startIndex\n     * keep the new caret position on start index or keep it closer to endIndex\n     */\n    return curCaretPos - startIndex < endIndex - curCaretPos ? startIndex : endIndex;\n}\n/* This keeps the caret within typing area so people can't type in between prefix or suffix or format characters */\nfunction getCaretPosInBoundary(value, caretPos, boundary, direction) {\n    var valLn = value.length;\n    // clamp caret position to [0, value.length]\n    caretPos = clamp(caretPos, 0, valLn);\n    if (direction === 'left') {\n        while (caretPos >= 0 && !boundary[caretPos])\n            { caretPos--; }\n        // if we don't find any suitable caret position on left, set it on first allowed position\n        if (caretPos === -1)\n            { caretPos = boundary.indexOf(true); }\n    }\n    else {\n        while (caretPos <= valLn && !boundary[caretPos])\n            { caretPos++; }\n        // if we don't find any suitable caret position on right, set it on last allowed position\n        if (caretPos > valLn)\n            { caretPos = boundary.lastIndexOf(true); }\n    }\n    // if we still don't find caret position, set it at the end of value\n    if (caretPos === -1)\n        { caretPos = valLn; }\n    return caretPos;\n}\nfunction caretUnknownFormatBoundary(formattedValue) {\n    var boundaryAry = Array.from({ length: formattedValue.length + 1 }).map(function () { return true; });\n    for (var i = 0, ln = boundaryAry.length; i < ln; i++) {\n        // consider caret to be in boundary if it is before or after numeric value\n        boundaryAry[i] = Boolean(charIsNumber(formattedValue[i]) || charIsNumber(formattedValue[i - 1]));\n    }\n    return boundaryAry;\n}\nfunction useInternalValues(value, defaultValue, valueIsNumericString, format, removeFormatting, onValueChange) {\n    if ( onValueChange === void 0 ) onValueChange = noop;\n\n    var getValues = usePersistentCallback(function (value, valueIsNumericString) {\n        var formattedValue, numAsString;\n        if (isNotValidValue(value)) {\n            numAsString = '';\n            formattedValue = '';\n        }\n        else if (typeof value === 'number' || valueIsNumericString) {\n            numAsString = typeof value === 'number' ? toNumericString(value) : value;\n            formattedValue = format(numAsString);\n        }\n        else {\n            numAsString = removeFormatting(value, undefined);\n            formattedValue = format(numAsString);\n        }\n        return { formattedValue: formattedValue, numAsString: numAsString };\n    });\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(function () {\n        return getValues(isNil(value) ? defaultValue : value, valueIsNumericString);\n    });\n    var values = ref[0];\n    var setValues = ref[1];\n    var _onValueChange = function (newValues, sourceInfo) {\n        if (newValues.formattedValue !== values.formattedValue) {\n            setValues({\n                formattedValue: newValues.formattedValue,\n                numAsString: newValues.value,\n            });\n        }\n        // call parent on value change if only if formatted value is changed\n        onValueChange(newValues, sourceInfo);\n    };\n    // if value is switch from controlled to uncontrolled, use the internal state's value to format with new props\n    var _value = value;\n    var _valueIsNumericString = valueIsNumericString;\n    if (isNil(value)) {\n        _value = values.numAsString;\n        _valueIsNumericString = true;\n    }\n    var newValues = getValues(_value, _valueIsNumericString);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {\n        setValues(newValues);\n    }, [newValues.formattedValue]);\n    return [values, _onValueChange];\n}\n\nfunction defaultRemoveFormatting(value) {\n    return value.replace(/[^0-9]/g, '');\n}\nfunction defaultFormat(value) {\n    return value;\n}\nfunction NumberFormatBase(props) {\n    var type = props.type; if ( type === void 0 ) type = 'text';\n    var displayType = props.displayType; if ( displayType === void 0 ) displayType = 'input';\n    var customInput = props.customInput;\n    var renderText = props.renderText;\n    var getInputRef = props.getInputRef;\n    var format = props.format; if ( format === void 0 ) format = defaultFormat;\n    var removeFormatting = props.removeFormatting; if ( removeFormatting === void 0 ) removeFormatting = defaultRemoveFormatting;\n    var defaultValue = props.defaultValue;\n    var valueIsNumericString = props.valueIsNumericString;\n    var onValueChange = props.onValueChange;\n    var isAllowed = props.isAllowed;\n    var onChange = props.onChange; if ( onChange === void 0 ) onChange = noop;\n    var onKeyDown = props.onKeyDown; if ( onKeyDown === void 0 ) onKeyDown = noop;\n    var onMouseUp = props.onMouseUp; if ( onMouseUp === void 0 ) onMouseUp = noop;\n    var onFocus = props.onFocus; if ( onFocus === void 0 ) onFocus = noop;\n    var onBlur = props.onBlur; if ( onBlur === void 0 ) onBlur = noop;\n    var propValue = props.value;\n    var getCaretBoundary = props.getCaretBoundary; if ( getCaretBoundary === void 0 ) getCaretBoundary = caretUnknownFormatBoundary;\n    var isValidInputCharacter = props.isValidInputCharacter; if ( isValidInputCharacter === void 0 ) isValidInputCharacter = charIsNumber;\n    var isCharacterSame = props.isCharacterSame;\n    var otherProps = __rest(props, [\"type\", \"displayType\", \"customInput\", \"renderText\", \"getInputRef\", \"format\", \"removeFormatting\", \"defaultValue\", \"valueIsNumericString\", \"onValueChange\", \"isAllowed\", \"onChange\", \"onKeyDown\", \"onMouseUp\", \"onFocus\", \"onBlur\", \"value\", \"getCaretBoundary\", \"isValidInputCharacter\", \"isCharacterSame\"]);\n    var ref = useInternalValues(propValue, defaultValue, Boolean(valueIsNumericString), format, removeFormatting, onValueChange);\n    var ref_0 = ref[0];\n    var formattedValue = ref_0.formattedValue;\n    var numAsString = ref_0.numAsString;\n    var onFormattedValueChange = ref[1];\n    var caretPositionBeforeChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    var lastUpdatedValue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({ formattedValue: formattedValue, numAsString: numAsString });\n    var _onValueChange = function (values, source) {\n        lastUpdatedValue.current = { formattedValue: values.formattedValue, numAsString: values.value };\n        onFormattedValueChange(values, source);\n    };\n    var ref$1 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    var mounted = ref$1[0];\n    var setMounted = ref$1[1];\n    var focusedElm = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    var timeout = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n        setCaretTimeout: null,\n        focusTimeout: null,\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n        setMounted(true);\n        return function () {\n            clearTimeout(timeout.current.setCaretTimeout);\n            clearTimeout(timeout.current.focusTimeout);\n        };\n    }, []);\n    var _format = format;\n    var getValueObject = function (formattedValue, numAsString) {\n        var floatValue = parseFloat(numAsString);\n        return {\n            formattedValue: formattedValue,\n            value: numAsString,\n            floatValue: isNaN(floatValue) ? undefined : floatValue,\n        };\n    };\n    var setPatchedCaretPosition = function (el, caretPos, currentValue) {\n        // don't reset the caret position when the whole input content is selected\n        if (el.selectionStart === 0 && el.selectionEnd === el.value.length)\n            { return; }\n        /* setting caret position within timeout of 0ms is required for mobile chrome,\n        otherwise browser resets the caret position after we set it\n        We are also setting it without timeout so that in normal browser we don't see the flickering */\n        setCaretPosition(el, caretPos);\n        timeout.current.setCaretTimeout = setTimeout(function () {\n            if (el.value === currentValue && el.selectionStart !== caretPos) {\n                setCaretPosition(el, caretPos);\n            }\n        }, 0);\n    };\n    /* This keeps the caret within typing area so people can't type in between prefix or suffix */\n    var correctCaretPosition = function (value, caretPos, direction) {\n        return getCaretPosInBoundary(value, caretPos, getCaretBoundary(value), direction);\n    };\n    var getNewCaretPosition = function (inputValue, newFormattedValue, caretPos) {\n        var caretBoundary = getCaretBoundary(newFormattedValue);\n        var updatedCaretPos = getCaretPosition(newFormattedValue, formattedValue, inputValue, caretPos, caretBoundary, isValidInputCharacter, isCharacterSame);\n        //correct caret position if its outside of editable area\n        updatedCaretPos = getCaretPosInBoundary(newFormattedValue, updatedCaretPos, caretBoundary);\n        return updatedCaretPos;\n    };\n    var updateValueAndCaretPosition = function (params) {\n        var newFormattedValue = params.formattedValue; if ( newFormattedValue === void 0 ) newFormattedValue = '';\n        var input = params.input;\n        var source = params.source;\n        var event = params.event;\n        var numAsString = params.numAsString;\n        var caretPos;\n        if (input) {\n            var inputValue = params.inputValue || input.value;\n            var currentCaretPosition = geInputCaretPosition(input);\n            /**\n             * set the value imperatively, this is required for IE fix\n             * This is also required as if new caret position is beyond the previous value.\n             * Caret position will not be set correctly\n             */\n            input.value = newFormattedValue;\n            //get the caret position\n            caretPos = getNewCaretPosition(inputValue, newFormattedValue, currentCaretPosition);\n            //set caret position imperatively\n            if (caretPos !== undefined) {\n                setPatchedCaretPosition(input, caretPos, newFormattedValue);\n            }\n        }\n        if (newFormattedValue !== formattedValue) {\n            // trigger onValueChange synchronously, so parent is updated along with the number format. Fix for #277, #287\n            _onValueChange(getValueObject(newFormattedValue, numAsString), { event: event, source: source });\n        }\n    };\n    /**\n     * if the formatted value is not synced to parent, or if the formatted value is different from last synced value sync it\n     * if the formatting props is removed, in which case last formatted value will be different from the numeric string value\n     * in such case we need to inform the parent.\n     */\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n        var ref = lastUpdatedValue.current;\n        var lastFormattedValue = ref.formattedValue;\n        var lastNumAsString = ref.numAsString;\n        if (formattedValue !== lastFormattedValue || numAsString !== lastNumAsString) {\n            _onValueChange(getValueObject(formattedValue, numAsString), {\n                event: undefined,\n                source: SourceType.props,\n            });\n        }\n    }, [formattedValue, numAsString]);\n    // also if formatted value is changed from the props, we need to update the caret position\n    // keep the last caret position if element is focused\n    var currentCaretPosition = focusedElm.current\n        ? geInputCaretPosition(focusedElm.current)\n        : undefined;\n    // needed to prevent warning with useLayoutEffect on server\n    var useIsomorphicLayoutEffect = typeof window !== 'undefined' ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n    useIsomorphicLayoutEffect(function () {\n        var input = focusedElm.current;\n        if (formattedValue !== lastUpdatedValue.current.formattedValue && input) {\n            var caretPos = getNewCaretPosition(lastUpdatedValue.current.formattedValue, formattedValue, currentCaretPosition);\n            /**\n             * set the value imperatively, as we set the caret position as well imperatively.\n             * This is to keep value and caret position in sync\n             */\n            input.value = formattedValue;\n            setPatchedCaretPosition(input, caretPos, formattedValue);\n        }\n    }, [formattedValue]);\n    var formatInputValue = function (inputValue, event, source) {\n        var input = event.target;\n        var changeRange = caretPositionBeforeChange.current\n            ? findChangedRangeFromCaretPositions(caretPositionBeforeChange.current, input.selectionEnd)\n            : findChangeRange(formattedValue, inputValue);\n        var changeMeta = Object.assign(Object.assign({}, changeRange), { lastValue: formattedValue });\n        var _numAsString = removeFormatting(inputValue, changeMeta);\n        var _formattedValue = _format(_numAsString);\n        // formatting can remove some of the number chars, so we need to fine number string again\n        _numAsString = removeFormatting(_formattedValue, undefined);\n        if (isAllowed && !isAllowed(getValueObject(_formattedValue, _numAsString))) {\n            //reset the caret position\n            var input$1 = event.target;\n            var currentCaretPosition = geInputCaretPosition(input$1);\n            var caretPos = getNewCaretPosition(inputValue, formattedValue, currentCaretPosition);\n            input$1.value = formattedValue;\n            setPatchedCaretPosition(input$1, caretPos, formattedValue);\n            return false;\n        }\n        updateValueAndCaretPosition({\n            formattedValue: _formattedValue,\n            numAsString: _numAsString,\n            inputValue: inputValue,\n            event: event,\n            source: source,\n            input: event.target,\n        });\n        return true;\n    };\n    var setCaretPositionInfoBeforeChange = function (el, endOffset) {\n        if ( endOffset === void 0 ) endOffset = 0;\n\n        var selectionStart = el.selectionStart;\n        var selectionEnd = el.selectionEnd;\n        caretPositionBeforeChange.current = { selectionStart: selectionStart, selectionEnd: selectionEnd + endOffset };\n    };\n    var _onChange = function (e) {\n        var el = e.target;\n        var inputValue = el.value;\n        var changed = formatInputValue(inputValue, e, SourceType.event);\n        if (changed)\n            { onChange(e); }\n        // reset the position, as we have already handled the caret position\n        caretPositionBeforeChange.current = undefined;\n    };\n    var _onKeyDown = function (e) {\n        var el = e.target;\n        var key = e.key;\n        var selectionStart = el.selectionStart;\n        var selectionEnd = el.selectionEnd;\n        var value = el.value; if ( value === void 0 ) value = '';\n        var expectedCaretPosition;\n        //Handle backspace and delete against non numerical/decimal characters or arrow keys\n        if (key === 'ArrowLeft' || key === 'Backspace') {\n            expectedCaretPosition = Math.max(selectionStart - 1, 0);\n        }\n        else if (key === 'ArrowRight') {\n            expectedCaretPosition = Math.min(selectionStart + 1, value.length);\n        }\n        else if (key === 'Delete') {\n            expectedCaretPosition = selectionStart;\n        }\n        // if key is delete and text is not selected keep the end offset to 1, as it deletes one character\n        // this is required as selection is not changed on delete case, which changes the change range calculation\n        var endOffset = 0;\n        if (key === 'Delete' && selectionStart === selectionEnd) {\n            endOffset = 1;\n        }\n        var isArrowKey = key === 'ArrowLeft' || key === 'ArrowRight';\n        //if expectedCaretPosition is not set it means we don't want to Handle keyDown\n        // also if multiple characters are selected don't handle\n        if (expectedCaretPosition === undefined || (selectionStart !== selectionEnd && !isArrowKey)) {\n            onKeyDown(e);\n            // keep information of what was the caret position before keyDown\n            // set it after onKeyDown, in case parent updates the position manually\n            setCaretPositionInfoBeforeChange(el, endOffset);\n            return;\n        }\n        var newCaretPosition = expectedCaretPosition;\n        if (isArrowKey) {\n            var direction = key === 'ArrowLeft' ? 'left' : 'right';\n            newCaretPosition = correctCaretPosition(value, expectedCaretPosition, direction);\n            // arrow left or right only moves the caret, so no need to handle the event, if we are handling it manually\n            if (newCaretPosition !== expectedCaretPosition) {\n                e.preventDefault();\n            }\n        }\n        else if (key === 'Delete' && !isValidInputCharacter(value[expectedCaretPosition])) {\n            // in case of delete go to closest caret boundary on the right side\n            newCaretPosition = correctCaretPosition(value, expectedCaretPosition, 'right');\n        }\n        else if (key === 'Backspace' && !isValidInputCharacter(value[expectedCaretPosition])) {\n            // in case of backspace go to closest caret boundary on the left side\n            newCaretPosition = correctCaretPosition(value, expectedCaretPosition, 'left');\n        }\n        if (newCaretPosition !== expectedCaretPosition) {\n            setPatchedCaretPosition(el, newCaretPosition, value);\n        }\n        onKeyDown(e);\n        setCaretPositionInfoBeforeChange(el, endOffset);\n    };\n    /** required to handle the caret position when click anywhere within the input **/\n    var _onMouseUp = function (e) {\n        var el = e.target;\n        /**\n         * NOTE: we have to give default value for value as in case when custom input is provided\n         * value can come as undefined when nothing is provided on value prop.\n         */\n        var correctCaretPositionIfRequired = function () {\n            var selectionStart = el.selectionStart;\n            var selectionEnd = el.selectionEnd;\n            var value = el.value; if ( value === void 0 ) value = '';\n            if (selectionStart === selectionEnd) {\n                var caretPosition = correctCaretPosition(value, selectionStart);\n                if (caretPosition !== selectionStart) {\n                    setPatchedCaretPosition(el, caretPosition, value);\n                }\n            }\n        };\n        correctCaretPositionIfRequired();\n        // try to correct after selection has updated by browser\n        // this case is required when user clicks on some position while a text is selected on input\n        requestAnimationFrame(function () {\n            correctCaretPositionIfRequired();\n        });\n        onMouseUp(e);\n        setCaretPositionInfoBeforeChange(el);\n    };\n    var _onFocus = function (e) {\n        // Workaround Chrome and Safari bug https://bugs.chromium.org/p/chromium/issues/detail?id=779328\n        // (onFocus event target selectionStart is always 0 before setTimeout)\n        if (e.persist)\n            { e.persist(); }\n        var el = e.target;\n        var currentTarget = e.currentTarget;\n        focusedElm.current = el;\n        timeout.current.focusTimeout = setTimeout(function () {\n            var selectionStart = el.selectionStart;\n            var selectionEnd = el.selectionEnd;\n            var value = el.value; if ( value === void 0 ) value = '';\n            var caretPosition = correctCaretPosition(value, selectionStart);\n            //setPatchedCaretPosition only when everything is not selected on focus (while tabbing into the field)\n            if (caretPosition !== selectionStart &&\n                !(selectionStart === 0 && selectionEnd === value.length)) {\n                setPatchedCaretPosition(el, caretPosition, value);\n            }\n            onFocus(Object.assign(Object.assign({}, e), { currentTarget: currentTarget }));\n        }, 0);\n    };\n    var _onBlur = function (e) {\n        focusedElm.current = null;\n        clearTimeout(timeout.current.focusTimeout);\n        clearTimeout(timeout.current.setCaretTimeout);\n        onBlur(e);\n    };\n    // add input mode on element based on format prop and device once the component is mounted\n    var inputMode = mounted && addInputMode() ? 'numeric' : undefined;\n    var inputProps = Object.assign({ inputMode: inputMode }, otherProps, {\n        type: type,\n        value: formattedValue,\n        onChange: _onChange,\n        onKeyDown: _onKeyDown,\n        onMouseUp: _onMouseUp,\n        onFocus: _onFocus,\n        onBlur: _onBlur,\n    });\n    if (displayType === 'text') {\n        return renderText ? (react__WEBPACK_IMPORTED_MODULE_0___default().createElement((react__WEBPACK_IMPORTED_MODULE_0___default().Fragment), null, renderText(formattedValue, otherProps) || null)) : (react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"span\", Object.assign({}, otherProps, { ref: getInputRef }), formattedValue));\n    }\n    else if (customInput) {\n        var CustomInput = customInput;\n        /* @ts-ignore */\n        return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(CustomInput, Object.assign({}, inputProps, { ref: getInputRef }));\n    }\n    return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"input\", Object.assign({}, inputProps, { ref: getInputRef }));\n}\n\nfunction format(numStr, props) {\n    var decimalScale = props.decimalScale;\n    var fixedDecimalScale = props.fixedDecimalScale;\n    var prefix = props.prefix; if ( prefix === void 0 ) prefix = '';\n    var suffix = props.suffix; if ( suffix === void 0 ) suffix = '';\n    var allowNegative = props.allowNegative;\n    var thousandsGroupStyle = props.thousandsGroupStyle; if ( thousandsGroupStyle === void 0 ) thousandsGroupStyle = 'thousand';\n    // don't apply formatting on empty string or '-'\n    if (numStr === '' || numStr === '-') {\n        return numStr;\n    }\n    var ref = getSeparators(props);\n    var thousandSeparator = ref.thousandSeparator;\n    var decimalSeparator = ref.decimalSeparator;\n    /**\n     * Keep the decimal separator\n     * when decimalScale is not defined or non zero and the numStr has decimal in it\n     * Or if decimalScale is > 0 and fixeDecimalScale is true (even if numStr has no decimal)\n     */\n    var hasDecimalSeparator = (decimalScale !== 0 && numStr.indexOf('.') !== -1) || (decimalScale && fixedDecimalScale);\n    var ref$1 = splitDecimal(numStr, allowNegative);\n    var beforeDecimal = ref$1.beforeDecimal;\n    var afterDecimal = ref$1.afterDecimal;\n    var addNegation = ref$1.addNegation; // eslint-disable-line prefer-const\n    //apply decimal precision if its defined\n    if (decimalScale !== undefined) {\n        afterDecimal = limitToScale(afterDecimal, decimalScale, !!fixedDecimalScale);\n    }\n    if (thousandSeparator) {\n        beforeDecimal = applyThousandSeparator(beforeDecimal, thousandSeparator, thousandsGroupStyle);\n    }\n    //add prefix and suffix when there is a number present\n    if (prefix)\n        { beforeDecimal = prefix + beforeDecimal; }\n    if (suffix)\n        { afterDecimal = afterDecimal + suffix; }\n    //restore negation sign\n    if (addNegation)\n        { beforeDecimal = '-' + beforeDecimal; }\n    numStr = beforeDecimal + ((hasDecimalSeparator && decimalSeparator) || '') + afterDecimal;\n    return numStr;\n}\nfunction getSeparators(props) {\n    var decimalSeparator = props.decimalSeparator; if ( decimalSeparator === void 0 ) decimalSeparator = '.';\n    var thousandSeparator = props.thousandSeparator;\n    var allowedDecimalSeparators = props.allowedDecimalSeparators;\n    if (thousandSeparator === true) {\n        thousandSeparator = ',';\n    }\n    if (!allowedDecimalSeparators) {\n        allowedDecimalSeparators = [decimalSeparator, '.'];\n    }\n    return {\n        decimalSeparator: decimalSeparator,\n        thousandSeparator: thousandSeparator,\n        allowedDecimalSeparators: allowedDecimalSeparators,\n    };\n}\nfunction handleNegation(value, allowNegative) {\n    if ( value === void 0 ) value = '';\n\n    var negationRegex = new RegExp('(-)');\n    var doubleNegationRegex = new RegExp('(-)(.)*(-)');\n    // Check number has '-' value\n    var hasNegation = negationRegex.test(value);\n    // Check number has 2 or more '-' values\n    var removeNegation = doubleNegationRegex.test(value);\n    //remove negation\n    value = value.replace(/-/g, '');\n    if (hasNegation && !removeNegation && allowNegative) {\n        value = '-' + value;\n    }\n    return value;\n}\nfunction getNumberRegex(decimalSeparator, global) {\n    return new RegExp((\"(^-)|[0-9]|\" + (escapeRegExp(decimalSeparator))), global ? 'g' : undefined);\n}\nfunction isNumericString(val, prefix, suffix) {\n    // for empty value we can always treat it as numeric string\n    if (val === '')\n        { return true; }\n    return (!(prefix === null || prefix === void 0 ? void 0 : prefix.match(/\\d/)) && !(suffix === null || suffix === void 0 ? void 0 : suffix.match(/\\d/)) && typeof val === 'string' && !isNaN(Number(val)));\n}\nfunction removeFormatting(value, changeMeta, props) {\n    var assign;\n\n    if ( changeMeta === void 0 ) changeMeta = getDefaultChangeMeta(value);\n    var allowNegative = props.allowNegative;\n    var prefix = props.prefix; if ( prefix === void 0 ) prefix = '';\n    var suffix = props.suffix; if ( suffix === void 0 ) suffix = '';\n    var decimalScale = props.decimalScale;\n    var from = changeMeta.from;\n    var to = changeMeta.to;\n    var start = to.start;\n    var end = to.end;\n    var ref = getSeparators(props);\n    var allowedDecimalSeparators = ref.allowedDecimalSeparators;\n    var decimalSeparator = ref.decimalSeparator;\n    var isBeforeDecimalSeparator = value[end] === decimalSeparator;\n    /**\n     * If only a number is added on empty input which matches with the prefix or suffix,\n     * then don't remove it, just return the same\n     */\n    if (charIsNumber(value) &&\n        (value === prefix || value === suffix) &&\n        changeMeta.lastValue === '') {\n        return value;\n    }\n    /** Check for any allowed decimal separator is added in the numeric format and replace it with decimal separator */\n    if (end - start === 1 && allowedDecimalSeparators.indexOf(value[start]) !== -1) {\n        var separator = decimalScale === 0 ? '' : decimalSeparator;\n        value = value.substring(0, start) + separator + value.substring(start + 1, value.length);\n    }\n    var stripNegation = function (value, start, end) {\n        /**\n         * if prefix starts with - we don't allow negative number to avoid confusion\n         * if suffix starts with - and the value length is same as suffix length, then the - sign is from the suffix\n         * In other cases, if the value starts with - then it is a negation\n         */\n        var hasNegation = false;\n        var hasDoubleNegation = false;\n        if (prefix.startsWith('-')) {\n            hasNegation = false;\n        }\n        else if (value.startsWith('--')) {\n            hasNegation = false;\n            hasDoubleNegation = true;\n        }\n        else if (suffix.startsWith('-') && value.length === suffix.length) {\n            hasNegation = false;\n        }\n        else if (value[0] === '-') {\n            hasNegation = true;\n        }\n        var charsToRemove = hasNegation ? 1 : 0;\n        if (hasDoubleNegation)\n            { charsToRemove = 2; }\n        // remove negation/double negation from start to simplify prefix logic as negation comes before prefix\n        if (charsToRemove) {\n            value = value.substring(charsToRemove);\n            // account for the removal of the negation for start and end index\n            start -= charsToRemove;\n            end -= charsToRemove;\n        }\n        return { value: value, start: start, end: end, hasNegation: hasNegation };\n    };\n    var toMetadata = stripNegation(value, start, end);\n    var hasNegation = toMetadata.hasNegation;\n    ((assign = toMetadata, value = assign.value, start = assign.start, end = assign.end));\n    var ref$1 = stripNegation(changeMeta.lastValue, from.start, from.end);\n    var fromStart = ref$1.start;\n    var fromEnd = ref$1.end;\n    var lastValue = ref$1.value;\n    // if only prefix and suffix part is updated reset the value to last value\n    // if the changed range is from suffix in the updated value, and the the suffix starts with the same characters, allow the change\n    var updatedSuffixPart = value.substring(start, end);\n    if (value.length &&\n        lastValue.length &&\n        (fromStart > lastValue.length - suffix.length || fromEnd < prefix.length) &&\n        !(updatedSuffixPart && suffix.startsWith(updatedSuffixPart))) {\n        value = lastValue;\n    }\n    /**\n     * remove prefix\n     * Remove whole prefix part if its present on the value\n     * If the prefix is partially deleted (in which case change start index will be less the prefix length)\n     * Remove only partial part of prefix.\n     */\n    var startIndex = 0;\n    if (value.startsWith(prefix))\n        { startIndex += prefix.length; }\n    else if (start < prefix.length)\n        { startIndex = start; }\n    value = value.substring(startIndex);\n    // account for deleted prefix for end\n    end -= startIndex;\n    /**\n     * Remove suffix\n     * Remove whole suffix part if its present on the value\n     * If the suffix is partially deleted (in which case change end index will be greater than the suffixStartIndex)\n     * remove the partial part of suffix\n     */\n    var endIndex = value.length;\n    var suffixStartIndex = value.length - suffix.length;\n    if (value.endsWith(suffix))\n        { endIndex = suffixStartIndex; }\n    // if the suffix is removed from the end\n    else if (end > suffixStartIndex)\n        { endIndex = end; }\n    // if the suffix is removed from start\n    else if (end > value.length - suffix.length)\n        { endIndex = end; }\n    value = value.substring(0, endIndex);\n    // add the negation back and handle for double negation\n    value = handleNegation(hasNegation ? (\"-\" + value) : value, allowNegative);\n    // remove non numeric characters\n    value = (value.match(getNumberRegex(decimalSeparator, true)) || []).join('');\n    // replace the decimalSeparator with ., and only keep the first separator, ignore following ones\n    var firstIndex = value.indexOf(decimalSeparator);\n    value = value.replace(new RegExp(escapeRegExp(decimalSeparator), 'g'), function (match, index) {\n        return index === firstIndex ? '.' : '';\n    });\n    //check if beforeDecimal got deleted and there is nothing after decimal,\n    //clear all numbers in such case while keeping the - sign\n    var ref$2 = splitDecimal(value, allowNegative);\n    var beforeDecimal = ref$2.beforeDecimal;\n    var afterDecimal = ref$2.afterDecimal;\n    var addNegation = ref$2.addNegation; // eslint-disable-line prefer-const\n    //clear only if something got deleted before decimal (cursor is before decimal)\n    if (to.end - to.start < from.end - from.start &&\n        beforeDecimal === '' &&\n        isBeforeDecimalSeparator &&\n        !parseFloat(afterDecimal)) {\n        value = addNegation ? '-' : '';\n    }\n    return value;\n}\nfunction getCaretBoundary(formattedValue, props) {\n    var prefix = props.prefix; if ( prefix === void 0 ) prefix = '';\n    var suffix = props.suffix; if ( suffix === void 0 ) suffix = '';\n    var boundaryAry = Array.from({ length: formattedValue.length + 1 }).map(function () { return true; });\n    var hasNegation = formattedValue[0] === '-';\n    // fill for prefix and negation\n    boundaryAry.fill(false, 0, prefix.length + (hasNegation ? 1 : 0));\n    // fill for suffix\n    var valLn = formattedValue.length;\n    boundaryAry.fill(false, valLn - suffix.length + 1, valLn + 1);\n    return boundaryAry;\n}\nfunction validateAndUpdateProps(props) {\n    var ref = getSeparators(props);\n    var thousandSeparator = ref.thousandSeparator;\n    var decimalSeparator = ref.decimalSeparator;\n    // eslint-disable-next-line prefer-const\n    var prefix = props.prefix; if ( prefix === void 0 ) prefix = '';\n    var allowNegative = props.allowNegative; if ( allowNegative === void 0 ) allowNegative = true;\n    if (thousandSeparator === decimalSeparator) {\n        throw new Error((\"\\n        Decimal separator can't be same as thousand separator.\\n        thousandSeparator: \" + thousandSeparator + \" (thousandSeparator = {true} is same as thousandSeparator = \\\",\\\")\\n        decimalSeparator: \" + decimalSeparator + \" (default value for decimalSeparator is .)\\n     \"));\n    }\n    if (prefix.startsWith('-') && allowNegative) {\n        // TODO: throw error in next major version\n        console.error((\"\\n      Prefix can't start with '-' when allowNegative is true.\\n      prefix: \" + prefix + \"\\n      allowNegative: \" + allowNegative + \"\\n    \"));\n        allowNegative = false;\n    }\n    return Object.assign(Object.assign({}, props), { allowNegative: allowNegative });\n}\nfunction useNumericFormat(props) {\n    // validate props\n    props = validateAndUpdateProps(props);\n    var _decimalSeparator = props.decimalSeparator;\n    var _allowedDecimalSeparators = props.allowedDecimalSeparators;\n    var thousandsGroupStyle = props.thousandsGroupStyle;\n    var suffix = props.suffix;\n    var allowNegative = props.allowNegative;\n    var allowLeadingZeros = props.allowLeadingZeros;\n    var onKeyDown = props.onKeyDown; if ( onKeyDown === void 0 ) onKeyDown = noop;\n    var onBlur = props.onBlur; if ( onBlur === void 0 ) onBlur = noop;\n    var thousandSeparator = props.thousandSeparator;\n    var decimalScale = props.decimalScale;\n    var fixedDecimalScale = props.fixedDecimalScale;\n    var prefix = props.prefix; if ( prefix === void 0 ) prefix = '';\n    var defaultValue = props.defaultValue;\n    var value = props.value;\n    var valueIsNumericString = props.valueIsNumericString;\n    var onValueChange = props.onValueChange;\n    var restProps = __rest(props, [\"decimalSeparator\", \"allowedDecimalSeparators\", \"thousandsGroupStyle\", \"suffix\", \"allowNegative\", \"allowLeadingZeros\", \"onKeyDown\", \"onBlur\", \"thousandSeparator\", \"decimalScale\", \"fixedDecimalScale\", \"prefix\", \"defaultValue\", \"value\", \"valueIsNumericString\", \"onValueChange\"]);\n    // get derived decimalSeparator and allowedDecimalSeparators\n    var ref = getSeparators(props);\n    var decimalSeparator = ref.decimalSeparator;\n    var allowedDecimalSeparators = ref.allowedDecimalSeparators;\n    var _format = function (numStr) { return format(numStr, props); };\n    var _removeFormatting = function (inputValue, changeMeta) { return removeFormatting(inputValue, changeMeta, props); };\n    var _value = isNil(value) ? defaultValue : value;\n    // try to figure out isValueNumericString based on format prop and value\n    var _valueIsNumericString = valueIsNumericString !== null && valueIsNumericString !== void 0 ? valueIsNumericString : isNumericString(_value, prefix, suffix);\n    if (!isNil(value)) {\n        _valueIsNumericString = _valueIsNumericString || typeof value === 'number';\n    }\n    else if (!isNil(defaultValue)) {\n        _valueIsNumericString = _valueIsNumericString || typeof defaultValue === 'number';\n    }\n    var roundIncomingValueToPrecision = function (value) {\n        if (isNotValidValue(value))\n            { return value; }\n        if (typeof value === 'number') {\n            value = toNumericString(value);\n        }\n        /**\n         * only round numeric or float string values coming through props,\n         * we don't need to do it for onChange events, as we want to prevent typing there\n         */\n        if (_valueIsNumericString && typeof decimalScale === 'number') {\n            return roundToPrecision(value, decimalScale, Boolean(fixedDecimalScale));\n        }\n        return value;\n    };\n    var ref$1 = useInternalValues(roundIncomingValueToPrecision(value), roundIncomingValueToPrecision(defaultValue), Boolean(_valueIsNumericString), _format, _removeFormatting, onValueChange);\n    var ref$1_0 = ref$1[0];\n    var numAsString = ref$1_0.numAsString;\n    var formattedValue = ref$1_0.formattedValue;\n    var _onValueChange = ref$1[1];\n    var _onKeyDown = function (e) {\n        var el = e.target;\n        var key = e.key;\n        var selectionStart = el.selectionStart;\n        var selectionEnd = el.selectionEnd;\n        var value = el.value; if ( value === void 0 ) value = '';\n        // if user tries to delete partial prefix then ignore it\n        if ((key === 'Backspace' || key === 'Delete') && selectionEnd < prefix.length) {\n            e.preventDefault();\n            return;\n        }\n        // if multiple characters are selected and user hits backspace, no need to handle anything manually\n        if (selectionStart !== selectionEnd) {\n            onKeyDown(e);\n            return;\n        }\n        // if user hits backspace, while the cursor is before prefix, and the input has negation, remove the negation\n        if (key === 'Backspace' &&\n            value[0] === '-' &&\n            selectionStart === prefix.length + 1 &&\n            allowNegative) {\n            // bring the cursor to after negation\n            setCaretPosition(el, 1);\n        }\n        // don't allow user to delete decimal separator when decimalScale and fixedDecimalScale is set\n        if (decimalScale && fixedDecimalScale) {\n            if (key === 'Backspace' && value[selectionStart - 1] === decimalSeparator) {\n                setCaretPosition(el, selectionStart - 1);\n                e.preventDefault();\n            }\n            else if (key === 'Delete' && value[selectionStart] === decimalSeparator) {\n                e.preventDefault();\n            }\n        }\n        // if user presses the allowed decimal separator before the separator, move the cursor after the separator\n        if ((allowedDecimalSeparators === null || allowedDecimalSeparators === void 0 ? void 0 : allowedDecimalSeparators.includes(key)) && value[selectionStart] === decimalSeparator) {\n            setCaretPosition(el, selectionStart + 1);\n        }\n        var _thousandSeparator = thousandSeparator === true ? ',' : thousandSeparator;\n        // move cursor when delete or backspace is pressed before/after thousand separator\n        if (key === 'Backspace' && value[selectionStart - 1] === _thousandSeparator) {\n            setCaretPosition(el, selectionStart - 1);\n        }\n        if (key === 'Delete' && value[selectionStart] === _thousandSeparator) {\n            setCaretPosition(el, selectionStart + 1);\n        }\n        onKeyDown(e);\n    };\n    var _onBlur = function (e) {\n        var _value = numAsString;\n        // if there no no numeric value, clear the input\n        if (!_value.match(/\\d/g)) {\n            _value = '';\n        }\n        // clear leading 0s\n        if (!allowLeadingZeros) {\n            _value = fixLeadingZero(_value);\n        }\n        // apply fixedDecimalScale on blur event\n        if (fixedDecimalScale && decimalScale) {\n            _value = roundToPrecision(_value, decimalScale, fixedDecimalScale);\n        }\n        if (_value !== numAsString) {\n            var formattedValue = format(_value, props);\n            _onValueChange({\n                formattedValue: formattedValue,\n                value: _value,\n                floatValue: parseFloat(_value),\n            }, {\n                event: e,\n                source: SourceType.event,\n            });\n        }\n        onBlur(e);\n    };\n    var isValidInputCharacter = function (inputChar) {\n        if (inputChar === decimalSeparator)\n            { return true; }\n        return charIsNumber(inputChar);\n    };\n    var isCharacterSame = function (ref) {\n        var currentValue = ref.currentValue;\n        var lastValue = ref.lastValue;\n        var formattedValue = ref.formattedValue;\n        var currentValueIndex = ref.currentValueIndex;\n        var formattedValueIndex = ref.formattedValueIndex;\n\n        var curChar = currentValue[currentValueIndex];\n        var newChar = formattedValue[formattedValueIndex];\n        /**\n         * NOTE: as thousand separator and allowedDecimalSeparators can be same, we need to check on\n         * typed range if we have typed any character from allowedDecimalSeparators, in that case we\n         * consider different characters like , and . same within the range of updated value.\n         */\n        var typedRange = findChangeRange(lastValue, currentValue);\n        var to = typedRange.to;\n        // handle corner case where if we user types a decimal separator with fixedDecimalScale\n        // and pass back float value the cursor jumps. #851\n        var getDecimalSeparatorIndex = function (value) {\n            return _removeFormatting(value).indexOf('.') + prefix.length;\n        };\n        if (value === 0 &&\n            fixedDecimalScale &&\n            decimalScale &&\n            currentValue[to.start] === decimalSeparator &&\n            getDecimalSeparatorIndex(currentValue) < currentValueIndex &&\n            getDecimalSeparatorIndex(formattedValue) > formattedValueIndex) {\n            return false;\n        }\n        if (currentValueIndex >= to.start &&\n            currentValueIndex < to.end &&\n            allowedDecimalSeparators &&\n            allowedDecimalSeparators.includes(curChar) &&\n            newChar === decimalSeparator) {\n            return true;\n        }\n        return curChar === newChar;\n    };\n    return Object.assign(Object.assign({}, restProps), { value: formattedValue, valueIsNumericString: false, isValidInputCharacter: isValidInputCharacter,\n        isCharacterSame: isCharacterSame, onValueChange: _onValueChange, format: _format, removeFormatting: _removeFormatting, getCaretBoundary: function (formattedValue) { return getCaretBoundary(formattedValue, props); }, onKeyDown: _onKeyDown, onBlur: _onBlur });\n}\nfunction NumericFormat(props) {\n    var numericFormatProps = useNumericFormat(props);\n    return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(NumberFormatBase, Object.assign({}, numericFormatProps));\n}\n\nfunction format$1(numStr, props) {\n    var format = props.format;\n    var allowEmptyFormatting = props.allowEmptyFormatting;\n    var mask = props.mask;\n    var patternChar = props.patternChar; if ( patternChar === void 0 ) patternChar = '#';\n    if (numStr === '' && !allowEmptyFormatting)\n        { return ''; }\n    var hashCount = 0;\n    var formattedNumberAry = format.split('');\n    for (var i = 0, ln = format.length; i < ln; i++) {\n        if (format[i] === patternChar) {\n            formattedNumberAry[i] = numStr[hashCount] || getMaskAtIndex(mask, hashCount);\n            hashCount += 1;\n        }\n    }\n    return formattedNumberAry.join('');\n}\nfunction removeFormatting$1(value, changeMeta, props) {\n    if ( changeMeta === void 0 ) changeMeta = getDefaultChangeMeta(value);\n\n    var format = props.format;\n    var patternChar = props.patternChar; if ( patternChar === void 0 ) patternChar = '#';\n    var from = changeMeta.from;\n    var to = changeMeta.to;\n    var lastValue = changeMeta.lastValue; if ( lastValue === void 0 ) lastValue = '';\n    var isNumericSlot = function (caretPos) { return format[caretPos] === patternChar; };\n    var removeFormatChar = function (string, startIndex) {\n        var str = '';\n        for (var i = 0; i < string.length; i++) {\n            if (isNumericSlot(startIndex + i) && charIsNumber(string[i])) {\n                str += string[i];\n            }\n        }\n        return str;\n    };\n    var extractNumbers = function (str) { return str.replace(/[^0-9]/g, ''); };\n    // if format doesn't have any number, remove all the non numeric characters\n    if (!format.match(/\\d/)) {\n        return extractNumbers(value);\n    }\n    /**\n     * if user paste the whole formatted text in an empty input or doing select all and paste, check if matches to the pattern\n     * and remove the format characters, if there is a mismatch on the pattern, do plane number extract\n     */\n    if ((lastValue === '' || from.end - from.start === lastValue.length) &&\n        value.length === format.length) {\n        var str = '';\n        for (var i = 0; i < value.length; i++) {\n            if (isNumericSlot(i)) {\n                if (charIsNumber(value[i])) {\n                    str += value[i];\n                }\n            }\n            else if (value[i] !== format[i]) {\n                // if there is a mismatch on the pattern, do plane number extract\n                return extractNumbers(value);\n            }\n        }\n        return str;\n    }\n    /**\n     * For partial change,\n     * where ever there is a change on the input, we can break the number in three parts\n     * 1st: left part which is unchanged\n     * 2nd: middle part which is changed\n     * 3rd: right part which is unchanged\n     *\n     * The first and third section will be same as last value, only the middle part will change\n     * We can consider on the change part all the new characters are non format characters.\n     * And on the first and last section it can have partial format characters.\n     *\n     * We pick first and last section from the lastValue (as that has 1-1 mapping with format)\n     * and middle one from the update value.\n     */\n    var firstSection = lastValue.substring(0, from.start);\n    var middleSection = value.substring(to.start, to.end);\n    var lastSection = lastValue.substring(from.end);\n    return (\"\" + (removeFormatChar(firstSection, 0)) + (extractNumbers(middleSection)) + (removeFormatChar(lastSection, from.end)));\n}\nfunction getCaretBoundary$1(formattedValue, props) {\n    var format = props.format;\n    var mask = props.mask;\n    var patternChar = props.patternChar; if ( patternChar === void 0 ) patternChar = '#';\n    var boundaryAry = Array.from({ length: formattedValue.length + 1 }).map(function () { return true; });\n    var hashCount = 0;\n    var firstEmptySlot = -1;\n    var maskAndIndexMap = {};\n    format.split('').forEach(function (char, index) {\n        var maskAtIndex = undefined;\n        if (char === patternChar) {\n            hashCount++;\n            maskAtIndex = getMaskAtIndex(mask, hashCount - 1);\n            if (firstEmptySlot === -1 && formattedValue[index] === maskAtIndex) {\n                firstEmptySlot = index;\n            }\n        }\n        maskAndIndexMap[index] = maskAtIndex;\n    });\n    var isPosAllowed = function (pos) {\n        // the position is allowed if the position is not masked and valid number area\n        return format[pos] === patternChar && formattedValue[pos] !== maskAndIndexMap[pos];\n    };\n    for (var i = 0, ln = boundaryAry.length; i < ln; i++) {\n        // consider caret to be in boundary if it is before or after numeric value\n        // Note: on pattern based format its denoted by patternCharacter\n        // we should also allow user to put cursor on first empty slot\n        boundaryAry[i] = i === firstEmptySlot || isPosAllowed(i) || isPosAllowed(i - 1);\n    }\n    // the first patternChar position is always allowed\n    boundaryAry[format.indexOf(patternChar)] = true;\n    return boundaryAry;\n}\nfunction validateProps(props) {\n    var mask = props.mask;\n    if (mask) {\n        var maskAsStr = mask === 'string' ? mask : mask.toString();\n        if (maskAsStr.match(/\\d/g)) {\n            throw new Error((\"Mask \" + mask + \" should not contain numeric character;\"));\n        }\n    }\n}\nfunction isNumericString$1(val, format) {\n    //we can treat empty string as numeric string\n    if (val === '')\n        { return true; }\n    return !(format === null || format === void 0 ? void 0 : format.match(/\\d/)) && typeof val === 'string' && (!!val.match(/^\\d+$/) || val === '');\n}\nfunction usePatternFormat(props) {\n    var mask = props.mask;\n    var allowEmptyFormatting = props.allowEmptyFormatting;\n    var formatProp = props.format;\n    var inputMode = props.inputMode; if ( inputMode === void 0 ) inputMode = 'numeric';\n    var onKeyDown = props.onKeyDown; if ( onKeyDown === void 0 ) onKeyDown = noop;\n    var patternChar = props.patternChar; if ( patternChar === void 0 ) patternChar = '#';\n    var value = props.value;\n    var defaultValue = props.defaultValue;\n    var valueIsNumericString = props.valueIsNumericString;\n    var restProps = __rest(props, [\"mask\", \"allowEmptyFormatting\", \"format\", \"inputMode\", \"onKeyDown\", \"patternChar\", \"value\", \"defaultValue\", \"valueIsNumericString\"]);\n    // validate props\n    validateProps(props);\n    var _getCaretBoundary = function (formattedValue) {\n        return getCaretBoundary$1(formattedValue, props);\n    };\n    var _onKeyDown = function (e) {\n        var key = e.key;\n        var el = e.target;\n        var selectionStart = el.selectionStart;\n        var selectionEnd = el.selectionEnd;\n        var value = el.value;\n        // if multiple characters are selected and user hits backspace, no need to handle anything manually\n        if (selectionStart !== selectionEnd) {\n            onKeyDown(e);\n            return;\n        }\n        // bring the cursor to closest numeric section\n        var caretPos = selectionStart;\n        // if backspace is pressed after the format characters, bring it to numeric section\n        // if delete is pressed before the format characters, bring it to numeric section\n        if (key === 'Backspace' || key === 'Delete') {\n            var direction = 'right';\n            if (key === 'Backspace') {\n                while (caretPos > 0 && formatProp[caretPos - 1] !== patternChar) {\n                    caretPos--;\n                }\n                direction = 'left';\n            }\n            else {\n                var formatLn = formatProp.length;\n                while (caretPos < formatLn && formatProp[caretPos] !== patternChar) {\n                    caretPos++;\n                }\n                direction = 'right';\n            }\n            caretPos = getCaretPosInBoundary(value, caretPos, _getCaretBoundary(value), direction);\n        }\n        else if (formatProp[caretPos] !== patternChar &&\n            key !== 'ArrowLeft' &&\n            key !== 'ArrowRight') {\n            // if user is typing on format character position, bring user to next allowed caret position\n            caretPos = getCaretPosInBoundary(value, caretPos + 1, _getCaretBoundary(value), 'right');\n        }\n        // if we changing caret position, set the caret position\n        if (caretPos !== selectionStart) {\n            setCaretPosition(el, caretPos);\n        }\n        onKeyDown(e);\n    };\n    // try to figure out isValueNumericString based on format prop and value\n    var _value = isNil(value) ? defaultValue : value;\n    var isValueNumericString = valueIsNumericString !== null && valueIsNumericString !== void 0 ? valueIsNumericString : isNumericString$1(_value, formatProp);\n    var _props = Object.assign(Object.assign({}, props), { valueIsNumericString: isValueNumericString });\n    return Object.assign(Object.assign({}, restProps), { value: value,\n        defaultValue: defaultValue, valueIsNumericString: isValueNumericString, inputMode: inputMode, format: function (numStr) { return format$1(numStr, _props); }, removeFormatting: function (inputValue, changeMeta) { return removeFormatting$1(inputValue, changeMeta, _props); }, getCaretBoundary: _getCaretBoundary, onKeyDown: _onKeyDown });\n}\nfunction PatternFormat(props) {\n    var patternFormatProps = usePatternFormat(props);\n    return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(NumberFormatBase, Object.assign({}, patternFormatProps));\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-number-format/dist/react-number-format.es.js\n"));

/***/ })

}]);