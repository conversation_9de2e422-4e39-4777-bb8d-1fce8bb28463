/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./node_modules/@glideapps/glide-data-grid/dist/esm/internal/markdown-div/private/markdown-container.css ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.gdg-mnuv029{word-break:break-word;-webkit-touch-callout:default;padding-top:6px;}.gdg-mnuv029 > *{margin:0;}.gdg-mnuv029 *:last-child{margin-bottom:0;}.gdg-mnuv029 p img{width:100%;}

/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./node_modules/@glideapps/glide-data-grid/dist/esm/internal/data-grid-overlay-editor/data-grid-overlay-editor-style.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.gdg-d19meir1{position:absolute;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;overflow:hidden;box-sizing:border-box;--overlay-top:var(--d19meir1-0);left:var(--d19meir1-1);top:var(--d19meir1-0);min-width:var(--d19meir1-2);min-height:var(--d19meir1-3);width:-webkit-max-content;width:-moz-max-content;width:max-content;max-width:400px;max-height:calc(100vh - var(--d19meir1-4));font-family:var(--gdg-font-family);font-size:var(--gdg-editor-font-size);text-align:start;}@-webkit-keyframes glide_fade_in-gdg-d19meir1{from{opacity:0%;}to{opacity:100%;}}@keyframes glide_fade_in-gdg-d19meir1{from{opacity:0%;}to{opacity:100%;}}.gdg-d19meir1.gdg-style{border-radius:2px;background-color:var(--gdg-bg-cell);box-shadow: 0 0 0 1px var(--gdg-accent-color), 0px 0px 1px rgba(62,65,86,0.4), 0px 6px 12px rgba(62,65,86,0.15);-webkit-animation:glide_fade_in-gdg-d19meir1 60ms 1;animation:glide_fade_in-gdg-d19meir1 60ms 1;}.gdg-d19meir1.gdg-pad{padding:var(--d19meir1-5) 8.5px 3px;}.gdg-d19meir1 .gdg-clip-region{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;overflow-y:auto;overflow-x:hidden;border-radius:2px;-webkit-box-flex:1;-webkit-flex-grow:1;-ms-flex-positive:1;flex-grow:1;}.gdg-d19meir1 .gdg-clip-region .gdg-growing-entry{height:100%;}.gdg-d19meir1 .gdg-clip-region input.gdg-input{width:100%;border:none;border-width:0;outline:none;}.gdg-d19meir1 .gdg-clip-region textarea.gdg-input{border:none;border-width:0;outline:none;}

/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./node_modules/@glideapps/glide-data-grid/dist/esm/internal/data-grid-overlay-editor/private/bubbles-overlay-editor-style.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.gdg-b1ygi5by{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-wrap:wrap;-ms-flex-wrap:wrap;flex-wrap:wrap;margin-top:auto;margin-bottom:auto;}.gdg-b1ygi5by .boe-bubble{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;border-radius:var(--gdg-rounding-radius,10px);padding:0 8px;height:20px;background-color:var(--gdg-bg-bubble);color:var(--gdg-text-dark);margin:2px;}.gdg-b1ygi5by textarea{position:absolute;top:0px;left:0px;width:0px;height:0px;opacity:0;}

/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./node_modules/@glideapps/glide-data-grid/dist/esm/internal/data-grid-overlay-editor/private/number-overlay-editor-style.css ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.gdg-n15fjm3e{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;margin:6px 0 3px;color:var(--gdg-text-dark);}.gdg-n15fjm3e > input{font-size:var(--gdg-editor-font-size);padding:0;font-family:var(--gdg-font-family);color:var(--gdg-text-dark);background-color:var(--gdg-bg-cell);}

/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./node_modules/@glideapps/glide-data-grid/dist/esm/internal/data-grid-overlay-editor/private/drilldown-overlay-editor.css ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.gdg-d4zsq0x{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-wrap:wrap;-ms-flex-wrap:wrap;flex-wrap:wrap;}.gdg-d4zsq0x .doe-bubble{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;padding:0 8px;height:24px;background-color:var(--gdg-bg-cell);color:var(--gdg-text-dark);margin:2px;border-radius:var(--gdg-rounding-radius,6px);box-shadow: 0 0 1px rgba(62,65,86,0.4), 0 1px 3px rgba(62,65,86,0.4);}.gdg-d4zsq0x .doe-bubble img{height:16px;object-fit:contain;margin-right:4px;}.gdg-d4zsq0x textarea{position:absolute;top:0px;left:0px;width:0px;height:0px;opacity:0;}

/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./node_modules/@glideapps/glide-data-grid/dist/esm/internal/data-grid-overlay-editor/private/image-overlay-editor-style.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.gdg-i2iowwq{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;height:100%;}.gdg-i2iowwq .gdg-centering-container{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;height:100%;}.gdg-i2iowwq .gdg-centering-container img,.gdg-i2iowwq .gdg-centering-container canvas{max-height:calc(100vh - var(--overlay-top) - 20px);object-fit:contain;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;}.gdg-i2iowwq .gdg-centering-container canvas{max-width:380px;}.gdg-i2iowwq .gdg-edit-icon{position:absolute;top:12px;right:0;width:48px;height:48px;color:var(--gdg-accent-color);cursor:pointer;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;}.gdg-i2iowwq .gdg-edit-icon > *{width:24px;height:24px;}.gdg-i2iowwq textarea{position:absolute;top:0px;left:0px;width:0px;height:0px;opacity:0;}

/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./node_modules/@glideapps/glide-data-grid/dist/esm/internal/data-grid-overlay-editor/private/uri-overlay-editor-style.css ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.gdg-u1rrojo{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-flex:1;-webkit-flex-grow:1;-ms-flex-positive:1;flex-grow:1;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;min-height:21px;}.gdg-u1rrojo .gdg-link-area{-webkit-box-flex:1;-webkit-flex-grow:1;-ms-flex-positive:1;flex-grow:1;-webkit-flex-shrink:1;-ms-flex-negative:1;flex-shrink:1;cursor:pointer;margin-right:8px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;color:var(--gdg-link-color);-webkit-text-decoration:underline !important;text-decoration:underline !important;}.gdg-u1rrojo .gdg-edit-icon{-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;width:32px;color:var(--gdg-accent-color);cursor:pointer;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;}.gdg-u1rrojo .gdg-edit-icon > *{width:24px;height:24px;}.gdg-u1rrojo textarea{position:absolute;top:0px;left:0px;width:0px;height:0px;opacity:0;}

/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./node_modules/@glideapps/glide-data-grid/dist/esm/internal/data-grid-overlay-editor/private/markdown-overlay-editor-style.css ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.gdg-m1pnx84e{min-width:var(--m1pnx84e-0);width:100%;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:flex-start;-webkit-box-align:flex-start;-ms-flex-align:flex-start;align-items:flex-start;-webkit-box-pack:justify;-webkit-justify-content:space-between;-ms-flex-pack:justify;justify-content:space-between;position:relative;color:var(--gdg-text-dark);}.gdg-m1pnx84e .gdg-g1y0xocz{-webkit-flex-shrink:1;-ms-flex-negative:1;flex-shrink:1;min-width:0;}.gdg-m1pnx84e .gdg-spacer{-webkit-flex:1;-ms-flex:1;flex:1;}.gdg-m1pnx84e .gdg-edit-icon{position:relative;cursor:pointer;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;color:var(--gdg-accent-color);padding:0;height:24px;width:24px;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;-webkit-transition:all "0.125s ease";transition:all "0.125s ease";border-radius:6px;}.gdg-m1pnx84e .gdg-edit-icon > *{width:16px;height:16px;}.gdg-m1pnx84e .gdg-edit-hover:hover{background-color:var(--gdg-accent-light);-webkit-transition:background-color 150ms;transition:background-color 150ms;}.gdg-m1pnx84e .gdg-checkmark-hover:hover{color:#ffffff;background-color:var(--gdg-accent-color);}.gdg-m1pnx84e .gdg-md-edit-textarea{position:relative;top:0px;left:0px;width:0px;height:0px;margin-top:25px;opacity:0;padding:0;}.gdg-m1pnx84e .gdg-ml-6{margin-left:6px;}

/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./node_modules/@glideapps/glide-data-grid/dist/esm/internal/scrolling-data-grid/infinite-scroller.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.gdg-s1dgczr6 .dvn-scroller{overflow:var(--s1dgczr6-0);-webkit-transform:translate3d(0,0,0);-ms-transform:translate3d(0,0,0);transform:translate3d(0,0,0);}.gdg-s1dgczr6 .dvn-hidden{visibility:hidden;}.gdg-s1dgczr6 .dvn-scroll-inner{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;pointer-events:none;}.gdg-s1dgczr6 .dvn-scroll-inner > *{-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;}.gdg-s1dgczr6 .dvn-scroll-inner .dvn-spacer{-webkit-box-flex:1;-webkit-flex-grow:1;-ms-flex-positive:1;flex-grow:1;}.gdg-s1dgczr6 .dvn-scroll-inner .dvn-stack{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;}.gdg-s1dgczr6 .dvn-underlay > *{position:absolute;left:0;top:0;}.gdg-s1dgczr6 canvas{outline:none;}.gdg-s1dgczr6 canvas *{height:0;}

/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./node_modules/@glideapps/glide-data-grid/dist/esm/internal/growing-entry/growing-entry-style.css ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.gdg-izpuzkl{position:absolute;left:0;right:0;top:0;bottom:0;width:100%;height:100%;border-radius:0px;resize:none;white-space:pre-wrap;min-width:100%;overflow:hidden;border:0;background-color:transparent;font-size:var(--gdg-editor-font-size);line-height:16px;font-family:var(--gdg-font-family);-webkit-text-fill-color:var(--gdg-text-dark);color:var(--gdg-text-dark);padding:0;margin:0;}.gdg-izpuzkl::-webkit-input-placeholder{color:var(--gdg-text-light);}.gdg-izpuzkl::-moz-placeholder{color:var(--gdg-text-light);}.gdg-izpuzkl:-ms-input-placeholder{color:var(--gdg-text-light);}.gdg-izpuzkl::placeholder{color:var(--gdg-text-light);}.gdg-invalid .gdg-izpuzkl{-webkit-text-decoration:underline;text-decoration:underline;-webkit-text-decoration-color:#d60606;text-decoration-color:#d60606;}
.gdg-s69h75o{visibility:hidden;white-space:pre-wrap;word-wrap:break-word;width:-webkit-max-content;width:-moz-max-content;width:max-content;max-width:100%;min-width:100%;font-size:var(--gdg-editor-font-size);line-height:16px;font-family:var(--gdg-font-family);color:var(--gdg-text-dark);padding:0;margin:0;padding-bottom:2px;}
.gdg-g1y0xocz{position:relative;margin-top:6px;}

/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./node_modules/@glideapps/glide-data-grid/dist/esm/internal/data-grid-search/data-grid-search-style.css ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.gdg-seveqep{position:absolute;top:4px;right:20px;background-color:var(--gdg-bg-cell);color:var(--gdg-text-dark);padding:8px;border:1px solid var(--gdg-border-color);border-radius:6px;font-size:var(--gdg-editor-font-size);-webkit-animation:gdg-search-fadein-gdg-seveqep 0.15s forwards;animation:gdg-search-fadein-gdg-seveqep 0.15s forwards;}.gdg-seveqep.out{-webkit-animation:gdg-search-fadeout-gdg-seveqep 0.15s forwards;animation:gdg-search-fadeout-gdg-seveqep 0.15s forwards;}.gdg-seveqep .gdg-search-bar-inner{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;}.gdg-seveqep .gdg-search-status{padding-top:4px;font-size:11px;}.gdg-seveqep .gdg-search-progress{position:absolute;height:4px;left:0;bottom:0;background-color:var(--gdg-text-light);}.gdg-seveqep input{width:220px;color:var(--gdg-textDark);background-color:var(--gdg-bg-cell);border:none;border-width:0;outline:none;}.gdg-seveqep button{width:24px;height:24px;padding:0;border:none;outline:none;background:none;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;cursor:pointer;color:var(--gdg-text-medium);}.gdg-seveqep button:hover{color:var(--gdg-text-dark);}.gdg-seveqep button .button-icon{width:16px;height:16px;}.gdg-seveqep button:disabled{opacity:0.4;pointer-events:none;}@-webkit-keyframes gdg-search-fadeout-gdg-seveqep{from{-webkit-transform:translateX(0);-ms-transform:translateX(0);transform:translateX(0);}to{-webkit-transform:translateX(400px);-ms-transform:translateX(400px);transform:translateX(400px);}}@keyframes gdg-search-fadeout-gdg-seveqep{from{-webkit-transform:translateX(0);-ms-transform:translateX(0);transform:translateX(0);}to{-webkit-transform:translateX(400px);-ms-transform:translateX(400px);transform:translateX(400px);}}@-webkit-keyframes gdg-search-fadein-gdg-seveqep{from{-webkit-transform:translateX(400px);-ms-transform:translateX(400px);transform:translateX(400px);}to{-webkit-transform:translateX(0);-ms-transform:translateX(0);transform:translateX(0);}}@keyframes gdg-search-fadein-gdg-seveqep{from{-webkit-transform:translateX(400px);-ms-transform:translateX(400px);transform:translateX(400px);}to{-webkit-transform:translateX(0);-ms-transform:translateX(0);transform:translateX(0);}}

/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./node_modules/@glideapps/glide-data-grid/dist/esm/internal/data-editor-container/data-grid-container.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.gdg-wmyidgi{position:relative;min-width:10px;min-height:10px;max-width:100%;max-height:100%;width:var(--wmyidgi-0);height:var(--wmyidgi-1);overflow:hidden;overflow:clip;direction:ltr;}.gdg-wmyidgi > :first-child{position:absolute;left:0;top:0;width:100%;height:100%;}

/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./node_modules/@glideapps/glide-data-grid/dist/esm/data-editor/group-rename.css ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************/
.gdg-r17m35ur{-webkit-box-flex:1;-webkit-flex-grow:1;-ms-flex-positive:1;flex-grow:1;border:none;outline:none;background-color:var(--gdg-bg-header-has-focus);border-radius:9px;padding:0 8px;box-shadow:0 0 0 1px var(--gdg-border-color);color:var(--gdg-text-group-header);min-height:var(--r17m35ur-0);font:var(--gdg-header-font-style) var(--gdg-font-family);}
.gdg-c1tqibwd{padding:0 8px;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;background-color:var(--gdg-bg-header);}

/*!****************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./node_modules/@glideapps/glide-data-grid/dist/index.css ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************/
/* Auto-generated file */

/*!******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./app/fullcalendar.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************/
/* FullCalendar Custom Styles */
.fc-event-past {
  opacity: 0.6;
}

.fc-event-time {
  font-style: italic;
}

.fc-event.conversation-event .fc-event-time {
  display: none;
}

.fc-toolbar-title {
  font-size: 2rem;
}

/* Change cursor to pointer for all calendar events */
.fc-event {
  cursor: pointer !important;
}

.fc-scrollgrid {
  border-radius: 16px !important;
  overflow: hidden !important;
}

.fc-scrollgrid-section-header > td {
  border-radius: 0 !important;
}

.fc-scrollgrid-section-body > td {
  border-radius: 0 !important;
}

.fc-col-header-cell {
  border-radius: 0 !important;
}

.fc-daygrid-day {
  border-radius: 0 !important;
}

.fc .fc-toolbar-title {
  font-size: 1.25rem !important;
  margin: 0;
  padding: 20px 0 0px 20px;
}

.fc .fc-button {
  background-color: #006082 !important;
  border-color: #006082 !important;
}

.fc-day-today {
  background-color: #edf5f7 !important;
}

[data-theme="dark"] .fc-day-today,
.dark .fc-day-today {
  background-color: #1e293b !important;
}

.fc-theme-standard td,
.fc-theme-standard th {
  border: 1px solid #e5e7eb !important;
}

.dark .fc-theme-standard td,
.dark .fc-theme-standard th {
  border: 1px solid #374151 !important;
}

.fc-scrollgrid-sync-table {
  border-collapse: separate !important;
  border-spacing: 0 !important;
}

.fc-day-other {
  background: #FAFAFB !important;
}

.dark .fc-day-other {
  background: #111827 !important;
}

.fc-daygrid-day-frame {
  position: relative !important;
  overflow: hidden !important;
}

.fc-daygrid-day-bg {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
}

.fc .fc-button .fc-icon {
  font-size: 0.875rem !important;
}

a.fc-col-header-cell-cushion {
  font-size: .85em !important;
  line-height: 2.2rem !important;
}

.fc .fc-daygrid-day-top {
  flex-direction: inherit !important;
  padding: 5px !important;
  font-size: .75em !important;
  color: #6b7280 !important;
}

.dark .fc .fc-daygrid-day-top {
  color: #9ca3af !important;
}

.fc .fc-button-primary:disabled {
  background-color: #eeeeee !important;
  color: black !important;
  border-color: #eeeeee !important;
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
  text-transform: capitalize !important;
}

.dark .fc .fc-button-primary:disabled {
  background-color: #374151 !important;
  color: white !important;
  border-color: #374151 !important;
}

/* Dark mode styles for calendar */
.dark .fc {
  color: #f9fafb;
}

.dark .fc-theme-standard th {
  background-color: #374151;
  border-color: #4b5563;
}

.dark .fc-theme-standard td {
  background-color: #1f2937;
  border-color: #374151;
}

.dark .fc-col-header-cell-cushion {
  color: #f9fafb;
}

.dark .fc-daygrid-day-number {
  color: #d1d5db;
}

.dark .fc-button-primary {
  background-color: #006082 !important;
  border-color: #006082 !important;
  color: white !important;
}

.dark .fc-button-primary:hover {
  background-color: #004d66 !important;
  border-color: #004d66 !important;
}

/* RTL-specific styles */
.fc[dir="rtl"] .fc-toolbar-title {
  padding: 20px 20px 0px 0 !important;
}

.fc[dir="rtl"] .fc-button-group {
  direction: rtl !important;
}

.fc[dir="rtl"] .fc-daygrid-day-top {
  direction: rtl !important;
}

.fc[dir="rtl"] .fc-col-header-cell-cushion {
  direction: rtl !important;
}

.fc[dir="rtl"] .fc-event {
  direction: rtl !important;
}

/* Calendar cell hover styles - Different from background */
.fc-daygrid-day:hover {
  background-color: #f3f4f6 !important;
  cursor: pointer !important;
}

.dark .fc-daygrid-day:hover {
  background-color: #374151 !important;
}

.fc-timegrid-slot:hover {
  background-color: #e0f2fe !important;
  cursor: pointer !important;
  z-index: 2 !important;
  box-shadow: 0 0 0 2px #38bdf8 inset !important;
}

.dark .fc-timegrid-slot:hover {
  background-color: #374151 !important;
}

.fc-timeGridWeek-view .fc-scroller {
  height: auto !important;
  overflow-y: auto !important;
}

/* Remove empty space at top of week view */
.fc-timeGridWeek-view .fc-scrollgrid-section-header {
  height: auto !important;
}

.fc-timeGridWeek-view .fc-col-header {
  padding: 8px 0 !important;
}

/* Ensure week view has proper row heights */
.fc-timeGridWeek-view .fc-timegrid-slot {
  height: 3em !important;
  min-height: 3em !important;
}

.fc-timeGridWeek-view .fc-timegrid-axis {
  width: 4em !important;
}



/* Better spacing for event content */
.fc-event-main {
  padding: 2px 4px !important;
}

.fc-event-title {
  font-size: 0.85em !important;
  line-height: 1.2 !important;
}

.fc-event-time {
  font-size: 0.75em !important;
  line-height: 1.1 !important;
}

/* Enhanced conversation event styling (matching Python implementation) */
.fc-event.conversation-event {
  background-color: #EDAE49 !important;
  border-color: #EDAE49 !important;
  color: #1f2937 !important;
}

.dark .fc-event.conversation-event {
  color: #ffffff !important;
}

/* Hide time display for conversation events (matching Python implementation) */
.fc-event.conversation-event .fc-event-time {
  display: none !important;
}

/* Cancelled reservation styling (matching Python implementation) */
.fc-event[data-cancelled="true"] {
  background-color: #e5e1e0 !important;
  border-color: #e5e1e0 !important;
  color: #908584 !important;
}

/* Past event styling (matching Python implementation) */
.fc-event-past {
  opacity: 0.6 !important;
}

/* Performance optimizations */
.fc {
  will-change: auto !important;
  transform: translateZ(0) !important;
}

.fc-scrollgrid {
  will-change: auto !important;
}

.fc-view-harness {
  will-change: auto !important;
} 
