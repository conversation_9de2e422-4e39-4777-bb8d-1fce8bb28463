groups:
- name: assistant_alerts
  rules:
    # Alert whenever the assistant returns a 5xx HTTP error (function exception)
    - alert: AssistantFunctionException
      expr: |
        increase(http_requests_total{http_status=~"5.."}[5m]) > 0
      for: 1m
      labels:
        severity: warning
      annotations:
        summary: "Assistant returned a 5xx error"
        description: |
          The assistant returned HTTP {{ $labels.http_status }} on endpoint {{ $labels.endpoint }}.

    # Alert whenever a retry occurs in the safety decorator
    - alert: Assistant<PERSON><PERSON>ryOccurred
      expr: |
        increase(api_retry_attempts_total[5m]) > 0
      for: 0m
      labels:
        severity: info
      annotations:
        summary: "Retry occurred in assistant safety decorator"
        description: |
          A retry was attempted in the assistant's retry decorator with exception type {{ $labels.exception_type }}.

    # Alert when retry count spikes above threshold
    - alert: ExcessiveAssistantRetries
      expr: increase(api_retry_attempts_total[5m]) > 10
      for: 2m
      labels:
        severity: warning
      annotations:
        summary: "High retry rate in assistant safety decorator"
        description: "More than 10 retries in 5 minutes (exception_type={{ $labels.exception_type }})."

    # Alert on any handled reservation failure
    - alert: ReservationFailure
      expr: increase(reservations_failed_total[5m]) > 0
      for: 0m
      labels:
        severity: warning
      annotations:
        summary: "Reservation operation failed"
        description: "A reservation attempt failed in the service."

    # Alert on any handled cancellation failure
    - alert: CancellationFailure
      expr: increase(reservations_cancellation_failed_total[5m]) > 0
      for: 0m
      labels:
        severity: warning
      annotations:
        summary: "Cancellation operation failed"
        description: "A cancellation attempt failed in the service."

    # Alert on any handled modification failure
    - alert: ModificationFailure
      expr: increase(reservations_modification_failed_total[5m]) > 0
      for: 0m
      labels:
        severity: warning
      annotations:
        summary: "Modification operation failed"
        description: "A reservation modification attempt failed in the service."
        
    # Alert on any delete reservation failure
    - alert: DeleteReservationFailure
      expr: |
        increase(http_requests_total{http_status=~"5..", endpoint=~"/api/reservations.*", method="DELETE"}[5m]) > 0
      for: 0m
      labels:
        severity: warning
      annotations:
        summary: "Delete reservation operation failed"
        description: "A delete reservation attempt failed with HTTP {{ $labels.http_status }} on endpoint {{ $labels.endpoint }}."

- name: llm_errors
  rules:
    # Alert on LLM API errors by provider and type
    - alert: LLMApiError
      expr: |
        rate(llm_api_errors_total[5m]) > 0
      for: 0m
      labels:
        severity: warning
      annotations:
        summary: "LLM API Error Detected"
        description: "{{ $labels.provider }} API error ({{ $labels.error_type }}) detected"

    # Alert on LLM retry attempts
    - alert: LLMRetryAttempts
      expr: |
        rate(llm_retry_attempts_total[5m]) > 0
      for: 0m
      labels:
        severity: warning
      annotations:
        summary: "LLM API Retry Detected" 
        description: "{{ $labels.provider }} API retry ({{ $labels.error_type }}) detected"
        
    # Alert on LLM tool execution errors
    - alert: LLMToolExecutionError
      expr: |
        rate(llm_tool_execution_errors_total[5m]) > 0
      for: 0m
      labels:
        severity: warning
      annotations:
        summary: "LLM Tool Execution Error"
        description: "Error executing tool '{{ $labels.tool_name }}' called by {{ $labels.provider }}"
        
    # Alert on empty LLM responses
    - alert: LLMEmptyResponse
      expr: |
        rate(llm_empty_responses_total[5m]) > 0
      for: 0m
      labels:
        severity: info
      annotations:
        summary: "Empty or Invalid LLM Response"
        description: "{{ $labels.provider }} returned an {{ $labels.response_type }} response"
        
- name: application_errors
  rules:
    # Alert on WhatsApp message delivery failures
    - alert: WhatsAppMessageFailure
      expr: |
        rate(whatsapp_message_failures_total[5m]) > 0
      for: 0m
      labels:
        severity: warning
      annotations:
        summary: "WhatsApp Message Delivery Failed"
        description: "WhatsApp message delivery failed (likely marked as undeliverable)"
        
    # Alert on APScheduler missed jobs
    - alert: SchedulerJobMissed
      expr: |
        rate(scheduler_job_missed_total[5m]) > 0
      for: 0m
      labels:
        severity: warning
      annotations:
        summary: "Scheduler Job Execution Missed"
        description: "An APScheduler job missed its scheduled execution time"
        
    # Alert on backup script failures
    - alert: BackupScriptFailure
      expr: |
        rate(backup_script_failures_total[5m]) > 0
      for: 0m
      labels:
        severity: critical
      annotations:
        summary: "Database Backup Script Failed"
        description: "The database backup script failed to execute properly"
        
    # Alert on invalid HTTP requests
    - alert: InvalidHTTPRequest
      expr: |
        rate(invalid_http_requests_total[5m]) > 0
      for: 1m
      labels:
        severity: info
      annotations:
        summary: "Invalid HTTP Request Received"
        description: "An invalid HTTP request was received by the application"
        
    # Alert on concurrent task limits
    - alert: ConcurrentTaskLimitReached
      expr: |
        rate(concurrent_task_limit_reached_total[5m]) > 0
      for: 1m
      labels:
        severity: warning
      annotations:
        summary: "Maximum Concurrent Tasks Reached"
        description: "The application has reached its maximum concurrent task limit" 