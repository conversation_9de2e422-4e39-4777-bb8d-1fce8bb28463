"use client"

import { useRef, useLayoutEffect, useState, useEffect, useMemo } from 'react'
import FullCalendar from '@fullcalendar/react'
import multiMonthPlugin from '@fullcalendar/multimonth'
import dayGridPlugin from '@fullcalendar/daygrid'
import timeGridPlugin from '@fullcalendar/timegrid'
import listPlugin from '@fullcalendar/list'
import interactionPlugin from '@fullcalendar/interaction'
import { useLanguage } from '@/lib/language-context'
import arLocale from '@fullcalendar/core/locales/ar-sa'
import { getReservations, getConversations, modifyReservation } from '@/lib/api'
import { useToast } from '@/hooks/use-toast'
import type { CalendarEvent, Reservation } from '@/types/calendar'
import { 
  getBusinessHours, 
  getSlotTimes, 
  getButtonText, 
  getTimezone,
  getValidRange,
  parseTime,
  SLOT_DURATION_HOURS
} from '@/lib/calendar-config'
import { 
  createCalendarCallbacks, 
  type CalendarCallbackHand<PERSON>,
  type DateClick<PERSON>rg,
  type DateSelectArg 
} from '@/lib/calendar-callbacks'
import { DataTableEditor } from './data-table-editor'
import '@/app/fullcalendar.css'

interface FullCalendarComponentProps {
  freeRoam?: boolean
  initialView?: string
  initialDate?: string
}

export function FullCalendarComponent({ 
  freeRoam = false, 
  initialView = 'multiMonthYear',
  initialDate
}: FullCalendarComponentProps) {
  const calendarRef = useRef<FullCalendar>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const { isRTL } = useLanguage()
  const { toast } = useToast()
  const [events, setEvents] = useState<CalendarEvent[]>([])
  const [loading, setLoading] = useState(true)
  const [editorOpen, setEditorOpen] = useState(false)
  const [selectedDateRange, setSelectedDateRange] = useState<{ start: string; end: string } | null>(null)
  const [currentDate] = useState<Date>(initialDate ? new Date(initialDate) : new Date())
  const [currentView, setCurrentView] = useState<string>(initialView)
  const [isHydrated, setIsHydrated] = useState(false)
  const viewRef = useRef<string>(initialView)
  const displayedDateRef = useRef<Date>(initialDate ? new Date(initialDate) : new Date())
  const [slotTimesKey, setSlotTimesKey] = useState(0)
  const [lastClickedDate, setLastClickedDate] = useState<Date | null>(null)

  // Helper to format a Date object to 'YYYY-MM-DDTHH:MM:SS' (local ISO-like string)
  const formatToLocalISO = (dt: Date | null): string => {
    if (!dt) return '';
    const year = dt.getUTCFullYear();
    const month = (dt.getUTCMonth() + 1).toString().padStart(2, '0');
    const day = dt.getUTCDate().toString().padStart(2, '0');
    const hours = dt.getUTCHours().toString().padStart(2, '0');
    const minutes = dt.getUTCMinutes().toString().padStart(2, '0');
    const seconds = dt.getUTCSeconds().toString().padStart(2, '0');
    return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
  };

  // Hydration and view persistence
  useEffect(() => {
    // Handle hydration and load persisted view
    const savedView = localStorage.getItem('calendar-view')
    if (savedView && savedView !== currentView) {
      viewRef.current = savedView
      setCurrentView(savedView)
    }
    setIsHydrated(true)
  }, [])

  // Save view to localStorage when it changes
  useEffect(() => {
    if (isHydrated && currentView !== initialView) {
      localStorage.setItem('calendar-view', currentView)
    }
  }, [currentView, isHydrated, initialView])

  // Process reservations with free roam logic (matching Python calendar_view.py lines 45-68)
  const processReservations = (allReservations: Record<string, Reservation[]>) => {
    const today = new Date()
    today.setHours(0, 0, 0, 0) // Start of today for comparison
    
    const activeReservations: Record<string, Reservation[]> = {}
    const cancelledReservations: Record<string, Reservation[]> = {}
    
    for (const [waId, customerReservations] of Object.entries(allReservations)) {
      const active: Reservation[] = []
      const cancelled: Reservation[] = []
      
      for (const reservation of customerReservations) {
        const dateStr = reservation.date
        if (dateStr) {
          const reservationDate = new Date(dateStr)
          reservationDate.setHours(0, 0, 0, 0)
          
          // In free_roam mode include all dates, else only future dates (matching Python logic)
          if (freeRoam) {
            if (reservation.cancelled) {
              cancelled.push(reservation)
            } else {
              active.push(reservation)
            }
          } else if (reservationDate >= today && !reservation.cancelled) {
            active.push(reservation)
          }
        }
      }
      
      if (active.length > 0) {
        activeReservations[waId] = active
      }
      if (cancelled.length > 0) {
        cancelledReservations[waId] = cancelled
      }
    }
    
    return { activeReservations, cancelledReservations, allCustomerData: allReservations }
  }

  // Generate calendar events from reservations and conversations (matching Python logic)
  const generateCalendarEvents = (
    allReservations: Record<string, Reservation[]>, 
    conversationsData: Record<string, any[]> = {}
  ): CalendarEvent[] => {
    const calendarEvents: CalendarEvent[] = []
    
    // Validate input data
    if (!allReservations || typeof allReservations !== 'object') {
      console.warn('Invalid reservations data received')
      return calendarEvents
    }
    
    // Process reservations based on free roam mode (matching Python logic)
    const { activeReservations, cancelledReservations, allCustomerData } = processReservations(allReservations)
    
    // Constants from Python implementation
    const slotDurationHours = 2 // 2-hour slots
    const numReservationsPerSlot = 6
    const slotDurationMs = (slotDurationHours * 60 * 60 * 1000) / numReservationsPerSlot // Duration per reservation
    
    // Step 1: Group reservations by date and time slot (matching Python logic lines 324-333)
    const groupedReservations: Record<string, Array<{ id: string, reservation: Reservation }>> = {}
    
    for (const [customerId, customerReservations] of Object.entries(activeReservations)) {
      for (const reservation of customerReservations) {
        if (reservation && reservation.customer_name && reservation.date && reservation.time_slot) {
          const dateStr = reservation.date
          const timeStr = reservation.time_slot
          const key = `${dateStr}_${timeStr}`
          
          if (!groupedReservations[key]) {
            groupedReservations[key] = []
          }
          
          groupedReservations[key].push({
            id: customerId,
            reservation
          })
        }
      }
    }
    

    
    // Step 1.5: Sort each group by reservation type then customer name (matching Python logic lines 335-336)
    for (const key of Object.keys(groupedReservations)) {
      groupedReservations[key].sort((a, b) => {
        const typeA = a.reservation.type || 0
        const typeB = b.reservation.type || 0
        if (typeA !== typeB) return typeA - typeB
        return (a.reservation.customer_name || "").localeCompare(b.reservation.customer_name || "")
      })
    }
    
    // Step 2: Process each time slot group sequentially (matching Python logic lines 338-383)
    for (const [timeKey, reservations] of Object.entries(groupedReservations)) {
      let previousEndTimeMs: number | null = null // Store as UTC milliseconds

      for (const { id, reservation } of reservations) {
        const customerName = reservation.customer_name
        const dateStr = reservation.date
        const timeStr = reservation.time_slot
        const type = reservation.type || 0
        
        try {
          const parsedTime = parseTime(timeStr) // HH:MM
          if (!dateStr || !parsedTime) {
            console.warn('Invalid date or time for reservation:', { customerName, dateStr, timeStr });
            continue;
          }
          
          let currentEventStartDateTime = new Date(`${dateStr}T${parsedTime}:00Z`) // Parse as UTC

          if (isNaN(currentEventStartDateTime.getTime())) {
            console.error(`Invalid date/time for reservation (parsed as NaN): ${dateStr} ${parsedTime}`)
            continue
          }
          
          if (previousEndTimeMs && currentEventStartDateTime.getTime() <= previousEndTimeMs) {
            currentEventStartDateTime = new Date(previousEndTimeMs + 60000) // Add 1 minute
          }
          
          const currentEventEndDateTime = new Date(currentEventStartDateTime.getTime() + slotDurationMs)
          previousEndTimeMs = currentEventEndDateTime.getTime()
          
          const isPastSlot = currentEventStartDateTime < new Date() // Compare UTC dates
          
          const event: CalendarEvent = {
            id: id,
            title: customerName,
            start: formatToLocalISO(currentEventStartDateTime),
            end: formatToLocalISO(currentEventEndDateTime),
            backgroundColor: type === 0 ? "#4caf50" : "#3688d8",
            borderColor: type === 0 ? "#4caf50" : "#3688d8",
            editable: !isPastSlot || freeRoam,
            extendedProps: {
              type,
              cancelled: false
            }
          }
          calendarEvents.push(event)
        } catch (error) {
          console.error('Error processing active reservation for calendar event:', { customerName, dateStr, timeStr }, error)
          continue
        }
      }
    }
    
    // Add cancelled reservations if in free roam mode (matching Python logic lines 385-411)
    if (freeRoam) {
      for (const [customerId, customerReservations] of Object.entries(cancelledReservations)) {
        for (const reservation of customerReservations) {
          if (reservation && reservation.customer_name) {
            const customerName = reservation.customer_name
            const dateStr = reservation.date
            const timeStr = reservation.time_slot
            const type = reservation.type || 0
            
            try {
              const parsedTime = parseTime(timeStr)
              if (!dateStr || !parsedTime) {
                console.warn('Invalid date or time for cancelled reservation:', { customerName, dateStr, timeStr });
                continue;
              }
              const startDateTime = new Date(`${dateStr}T${parsedTime}:00Z`) // Parse as UTC
              if (isNaN(startDateTime.getTime())) {
                console.error(`Invalid date/time for cancelled reservation (parsed as NaN): ${dateStr} ${parsedTime}`)
                continue
              }
              const endDateTime = new Date(startDateTime.getTime() + slotDurationMs)
              
              const event: CalendarEvent = {
                 id: customerId,
                 title: customerName,
                 start: formatToLocalISO(startDateTime),
                 end: formatToLocalISO(endDateTime),
                 editable: false,
                 backgroundColor: "#e5e1e0",
                 borderColor: "#e5e1e0",
                 textColor: "#908584",
                 extendedProps: {
                   type,
                   cancelled: true
                 }
              }
              calendarEvents.push(event)
            } catch (error) {
              console.error('Error processing cancelled reservation for calendar event:', { customerName, dateStr, timeStr }, error)
              continue
            }
          }
        }
      }
    }
    
    // Add conversation events if in free roam mode (matching Python logic lines 413-452)
    if (freeRoam && conversationsData) {
      for (const [customerId, conversations] of Object.entries(conversationsData)) {
        if (Array.isArray(conversations) && conversations.length > 0) {
          // Check if there's a reservation for this ID (active or cancelled) to get customer name
          let customerName = null
          
          // First check in active reservations
          if (activeReservations[customerId]) {
            for (const reservation of activeReservations[customerId]) {
              if (reservation.customer_name) {
                customerName = reservation.customer_name
                break
              }
            }
          }
          
          // If not found, check in all customer data (matching Python logic)
          if (!customerName && allCustomerData[customerId]) {
            for (const reservation of allCustomerData[customerId]) {
              if (reservation.customer_name) {
                customerName = reservation.customer_name
                break
              }
            }
          }
          
          // Get the last conversation for timing (matching Python logic)
          const lastConversation = conversations[conversations.length - 1]
          const dateStr = lastConversation?.date
          const timeStr = lastConversation?.time
          
          if (!dateStr || !timeStr) {
            continue
          }
          
          try {
            // Parse the conversation datetime
            const parsedTime = parseTime(timeStr)
            const startDateTime = new Date(`${dateStr}T${parsedTime}:00Z`) // Parse as UTC
            
            // Validate the parsed date
            if (isNaN(startDateTime.getTime())) {
              console.error(`Invalid conversation date/time (parsed as NaN): ${dateStr} ${parsedTime}`)
              continue
            }
            
            // Calculate end time (same duration as reservations)
            const endDateTime = new Date(startDateTime.getTime() + slotDurationMs)
            
            // Create title based on customer name if available (matching Python get_message logic)
            const title = `Conversation${customerName ? ` - ${customerName}` : ` - ${customerId}`}`
            
            // Create conversation event (matching Python event structure)
            const conversationEvent: CalendarEvent = {
              id: customerId,
              title: title,
              start: formatToLocalISO(startDateTime),
              end: formatToLocalISO(endDateTime),
              backgroundColor: "#EDAE49", // Exact color from Python implementation
              borderColor: "#EDAE49",
              className: ['conversation-event'], // Matching Python classNames
              editable: false, // Conversations are not editable
              extendedProps: {
                type: 2, // Use type 2 for conversations (0 and 1 are for reservations)
                cancelled: false
              }
            }
            
            calendarEvents.push(conversationEvent)
            
          } catch (error) {
            console.error('Error processing conversation for calendar event:', { customerId, dateStr, timeStr }, error)
            continue
          }
        }
      }
    }
    
    return calendarEvents
  }



  // Fetch reservations and conversations, generate events
  useEffect(() => {
    const fetchCalendarData = async () => {
      try {
        setLoading(true)
        // Fetch reservations (both future and past, including cancelled in free_roam mode)
        const reservationsData = await getReservations(false, freeRoam)
        
        // Fetch conversations if in free roam mode (matching Python implementation)
        let conversationsData: Record<string, any> = {}
        if (freeRoam) {
          try {
            const convResponse = await getConversations()
            conversationsData = convResponse || {}
          } catch (convError) {
            console.warn('Failed to fetch conversations:', convError)
          }
        }
        
        // Generate calendar events from reservations and conversations
        const calendarEvents = generateCalendarEvents(reservationsData, conversationsData)
        
        // Final validation: filter out any events with invalid dates
        const validEvents = calendarEvents.filter(event => {
          if (!event.start || !event.end) {
            console.warn('Event missing start/end date:', event)
            return false
          }
          
          const startDate = new Date(event.start)
          const endDate = new Date(event.end)
          
          if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
            console.warn('Event has invalid dates:', event)
            return false
          }
          
          return true
        })
        
        setEvents(validEvents)
        
      } catch (error) {
        console.error('Error fetching calendar data:', error)
        setEvents([])
      } finally {
        setLoading(false)
      }
    }
    
    fetchCalendarData()
  }, [freeRoam])

  // Refetch calendar events without page refresh
  const refetchEvents = async () => {
    try {

      
      // Fetch reservations
      const reservationsData = await getReservations(false, freeRoam)
      
      // Fetch conversations if in free roam mode
      let conversationsData: Record<string, any> = {}
      if (freeRoam) {
        try {
          const convResponse = await getConversations()
          conversationsData = convResponse || {}
        } catch (convError) {
          console.warn('Failed to fetch conversations during refetch:', convError)
        }
      }
      
      // Generate and set new calendar events
      const calendarEvents = generateCalendarEvents(reservationsData, conversationsData)
      const validEvents = calendarEvents.filter(event => {
        if (!event.start || !event.end) return false
        const startDate = new Date(event.start)
        const endDate = new Date(event.end)
        return !isNaN(startDate.getTime()) && !isNaN(endDate.getTime())
      })
      
      setEvents(validEvents)
      
    } catch (error) {
      console.error('Error refetching calendar data:', error)
    }
  }

  // Handle event drag and drop changes (matching Python eventChange logic)
  const handleEventChange = async (info: any) => {
    const event = info.event
    const newStartDate = new Date(event.start)
    const newDate = newStartDate.toISOString().split('T')[0]
    const newTime = newStartDate.toLocaleTimeString('en-US', { 
      hour12: true, 
      hour: '2-digit', 
      minute: '2-digit' 
    })
    
    const eventType = event.extendedProps.type || 0
    const customerName = event.title
    
    try {

      
      const result = await modifyReservation({
        id: event.id,
        date: newDate,
        time: newTime,
        title: customerName,
        type: eventType,
        approximate: true
      })
      
      if (result.success) {
        toast({
          title: isRTL ? "تم تحديث الحجز" : "Reservation Updated",
          description: isRTL ? "تم تحديث الحجز بنجاح" : "Reservation has been updated successfully",
          variant: "success",
          duration: 3000,
        })
        
        // Refetch calendar events after successful change
        await refetchEvents()
      } else {
        // Revert the change if backend update failed
        info.revert()
        toast({
          title: isRTL ? "فشل في التحديث" : "Update Failed",
          description: result.message || (isRTL ? "حدث خطأ أثناء تحديث الحجز" : "Error updating reservation"),
          variant: "destructive",
          duration: 5000,
        })
      }
    } catch (error) {
      // Revert the change on error
      info.revert()
      toast({
        title: isRTL ? "خطأ في الشبكة" : "Network Error",
        description: isRTL ? "تعذر الاتصال بالخادم" : "Could not connect to server",
        variant: "destructive",
        duration: 5000,
      })
      console.error('Error modifying reservation:', error)
    }
  }

  // Calendar callback handlers
  const callbackHandlers: CalendarCallbackHandlers = {
    onOpenDataEditor: (info) => {
      let dateToSet: Date | null = null;
      if ('start' in info && info.start) {
        dateToSet = new Date(info.start);
      } else if ('date' in info && info.date) {
        dateToSet = new Date(info.date);
      } else if ('dateStr' in info && info.dateStr) {
        dateToSet = new Date(info.dateStr);
      }
      if (dateToSet) setLastClickedDate(dateToSet);
      // Convert click info to date/time range for the table editor
      if ('start' in info && 'end' in info) {
        // Date range selection
        setSelectedDateRange({
          start: info.start.toISOString(),
          end: info.end.toISOString()
        })
      } else {
        // Single date click: include time if present
        const ds = info.dateStr || info.date.toISOString();
        if (ds.includes('T')) {
          // Time-based view click
          setSelectedDateRange({ start: ds, end: ds });
        } else {
          // Date-only click
          setSelectedDateRange({ start: ds, end: ds });
        }
      }
      setEditorOpen(true)
    },
    onDateClick: (info) => {
      if (info.date) setLastClickedDate(new Date(info.date));
      // Date click handled
    },
    onSelect: (info) => {
      if (info.start) setLastClickedDate(new Date(info.start));
      // Date range selection handled
    },
    onEventClick: (info) => {
      // TODO: Open conversation view
    },
    onEventChange: handleEventChange,
    onEventAdd: (info) => {
      // Event add is already handled in the callback
    },
    onEventRemove: (eventId) => {
      // Refresh events after removal
    }
  }

  // Create calendar callbacks
  const callbacks = createCalendarCallbacks(callbackHandlers)

  // Get slot times based on initial date (matching Python implementation)
  const slotTimes = useMemo(() => getSlotTimes(lastClickedDate || currentDate, freeRoam), [lastClickedDate, currentDate, freeRoam])

  // Resize calendar when container size or language changes
  useLayoutEffect(() => {
    if (!calendarRef.current || !containerRef.current) return
    const calendarApi = calendarRef.current.getApi()
    // Initial sizing
    requestAnimationFrame(() => calendarApi.updateSize())
    // Observe container resize
    const resizeObserver = new ResizeObserver(() => {
      calendarApi.updateSize()
    })
    resizeObserver.observe(containerRef.current)
    // Update on window resize
    const handleWindowResize = () => calendarApi.updateSize()
    window.addEventListener('resize', handleWindowResize)
    return () => {
      resizeObserver.disconnect()
      window.removeEventListener('resize', handleWindowResize)
    }
  }, [isRTL])

  if (loading || !isHydrated) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <div className="text-gray-500">Loading calendar from Python backend...</div>
      </div>
    )
  }

  return (
    <>
      <div ref={containerRef} className="w-full h-full min-h-[600px]">
        <FullCalendar
          ref={calendarRef}
          plugins={[multiMonthPlugin, dayGridPlugin, timeGridPlugin, listPlugin, interactionPlugin]}
          initialView={isHydrated ? currentView : initialView}
          initialDate={initialDate}
          height="auto"
          contentHeight="auto"
          events={events}
          
          // Header configuration (matching Python implementation)
          headerToolbar={{
            left: 'today prev,next',
            center: 'title',
            right: 'multiMonthYear,dayGridMonth,timeGridWeek,listMonth'
          }}
          
          // Enhanced calendar options (matching Python implementation)
          editable={true}
          selectable={true}
          eventStartEditable={true}
          eventDurationEditable={false}
          expandRows={true}
          navLinks={true}
          weekNumbers={false}
          buttonIcons={{
            prev: 'chevron-left',
            next: 'chevron-right'
          }}
          nowIndicator={true}
          allDaySlot={false}
          slotDuration={{ hours: SLOT_DURATION_HOURS }}
          
          // Business hours and constraints (matching Python implementation)
          businessHours={getBusinessHours(freeRoam)}
          eventConstraint={freeRoam ? undefined : "businessHours"}
          selectConstraint={freeRoam ? undefined : "businessHours"}
          hiddenDays={freeRoam ? [] : [5]} // Hide Friday unless in free roam
          
          // Valid range for navigation (matching Python validRange logic)
          // Only apply validRange when NOT in free roam AND NOT in multiMonthYear view
          {...(!freeRoam && viewRef.current !== 'multiMonthYear' && currentView !== 'multiMonthYear' && initialView !== 'multiMonthYear' ? { validRange: getValidRange(freeRoam, currentView) } : {})}
          
          // Dynamic slot times based on date/conditions
          slotMinTime={slotTimes.slotMinTime}
          slotMaxTime={slotTimes.slotMaxTime}
          
          // Localization (matching Python implementation)
          locale={isRTL ? arLocale : "en"}
          direction={isRTL ? "rtl" : "ltr"}
          buttonText={getButtonText(isRTL)}
          firstDay={6} // Saturday as first day
          aspectRatio={1.4}
          timeZone={getTimezone()}
          
          // Multi-month specific options
          multiMonthMaxColumns={3}
          multiMonthMinWidth={350}
          fixedWeekCount={false}
          showNonCurrentDates={false}
          dayMaxEvents={true}
          moreLinkClick="popover"
          eventDisplay="block"
          displayEventTime={true}
          
          // Styling
          eventClassNames="rounded px-1 text-xs"
          dayCellClassNames="hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer"
          viewClassNames="bg-white dark:bg-gray-900 rounded-lg shadow-sm"
          
          // Event callbacks (matching Python implementation)
          dateClick={callbacks.dateClick}
          select={callbacks.select}
          eventClick={callbacks.eventClick}
          eventChange={callbacks.eventChange}
          viewDidMount={(info) => {
            // Save view changes to localStorage and update ref immediately
            if (isHydrated && info.view.type !== currentView) {
              viewRef.current = info.view.type
              setCurrentView(info.view.type)
            }
            // Update size after view change
            calendarRef.current?.getApi().updateSize()
          }}
          datesSet={(info) => {
            // Update current view immediately when view changes (before rendering)
            if (isHydrated && info.view.type !== currentView) {
              viewRef.current = info.view.type
              setCurrentView(info.view.type)
            }
            // Update size after dates change
            calendarRef.current?.getApi().updateSize()
          }}
        />
      </div>
      
      {/* Data Table Editor */}
      <DataTableEditor
        open={editorOpen}
        onOpenChange={setEditorOpen}
        slotDurationHours={SLOT_DURATION_HOURS}
        events={events.filter(event => {
          if (freeRoam) {
            return event.extendedProps?.type === 0 || (event.extendedProps?.type === 0 && event.extendedProps?.cancelled);
          } else {
            return event.extendedProps?.type === 0 && !event.extendedProps?.cancelled;
          }
        }).map(event => ({
          ...event,
          type: 'reservation',
          extendedProps: {
            ...event.extendedProps,
            status: event.extendedProps?.cancelled ? 'cancelled' : 'active',
          }
        }))}
        selectedDateRange={selectedDateRange}
        isRTL={isRTL}
        freeRoam={freeRoam}
        onSave={async (updatedEvents) => {
          // Only process changed reservations (compare by id, start, end, customerName)
          const changed = updatedEvents.filter(ue => {
            const orig = events.find(e => e.id === ue.id);
            if (!orig) return true;
            return (
              orig.start !== ue.start ||
              orig.end !== ue.end ||
              (orig.extendedProps?.customerName !== ue.extendedProps?.customerName)
            );
          });
          let allSuccess = true;
          for (const event of changed) {
            if (event.extendedProps?.status === 'cancelled') continue; // skip cancelled
            try {
              const newDate = event.start.split('T')[0];
              const newTime = new Date(event.start).toLocaleTimeString('en-US', { hour12: true, hour: '2-digit', minute: '2-digit' });
              const result = await modifyReservation({
                id: event.id,
                date: newDate,
                time: newTime,
                title: event.extendedProps?.customerName || event.title,
                type: 0,
                approximate: true
              });
              if (!result.success) {
                allSuccess = false;
                toast({
                  title: isRTL ? "فشل في التحديث" : "Update Failed",
                  description: result.message || (isRTL ? "حدث خطأ أثناء تحديث الحجز" : "Error updating reservation"),
                  variant: "destructive",
                  duration: 5000,
                });
              }
            } catch (error) {
              allSuccess = false;
              toast({
                title: isRTL ? "خطأ في الشبكة" : "Network Error",
                description: isRTL ? "تعذر الاتصال بالخادم" : "Could not connect to server",
                variant: "destructive",
                duration: 5000,
              });
            }
          }
          if (allSuccess && changed.length > 0) {
            toast({
              title: isRTL ? "تم تحديث الحجز" : "Reservation Updated",
              description: isRTL ? "تم تحديث الحجز بنجاح" : "Reservation has been updated successfully",
              variant: "success",
              duration: 3000,
            });
          }
          await refetchEvents();
        }}
        onEventClick={(event: any) => {
          // Event click in table handled
        }}
      />
    </>
  )
} 