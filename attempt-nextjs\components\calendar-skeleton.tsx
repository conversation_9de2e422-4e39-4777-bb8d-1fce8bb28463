export function CalendarSkeleton() {
  return (
    <div className="w-full h-full bg-white dark:bg-gray-900 rounded-lg shadow-sm border animate-pulse">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex gap-2">
          <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
          <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
          <div className="w-16 h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
        <div className="w-48 h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
        <div className="flex gap-2">
          <div className="w-24 h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
          <div className="w-20 h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
          <div className="w-20 h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
          <div className="w-20 h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
      </div>
      
      {/* Calendar Grid */}
      <div className="p-4">
        <div className="grid grid-cols-7 gap-2 mb-4">
          {Array.from({ length: 7 }).map((_, i) => (
            <div key={i} className="h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
          ))}
        </div>
        <div className="grid grid-cols-7 gap-2">
          {Array.from({ length: 35 }).map((_, i) => (
            <div key={i} className="h-24 bg-gray-100 dark:bg-gray-800 rounded border"></div>
          ))}
        </div>
      </div>
    </div>
  )
} 