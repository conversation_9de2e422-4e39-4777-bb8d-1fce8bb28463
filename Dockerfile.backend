FROM python:3.10-slim

RUN apt-get update && apt-get install -y build-essential python3-dev libuv1-dev sqlite3 zip rclone && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Create rclone config directory
RUN mkdir -p /root/.config/rclone

# Install uv for better package management
RUN pip install --no-cache-dir uv

# Copy backend dependencies file
COPY requirements-backend.in ./
RUN uv pip install --system --no-cache-dir -r requirements-backend.in

# Copy only the backend code (not frontend)
COPY run.py .
COPY app/config.py app/config.py
COPY app/__init__.py app/__init__.py
COPY app/db.py app/db.py
COPY app/i18n.py app/i18n.py
COPY app/metrics.py app/metrics.py
COPY app/scheduler.py app/scheduler.py
COPY app/views.py app/views.py
COPY app/utils/ app/utils/
COPY app/services/ app/services/
COPY app/decorators/ app/decorators/
COPY scripts/ /app/scripts/
COPY tests/ tests/

# Create data directory for persistent storage
RUN mkdir -p /app/data
RUN mkdir -p /app/backups

# Note: We'll handle database copy in the docker-compose command instead

# Set environment variable to point to persisted database location
ENV DB_PATH=/app/data/threads_db.sqlite

# Expose FastAPI port
EXPOSE 8000

# Default command runs the FastAPI app; scheduler starts automatically
CMD ["python", "run.py"] 