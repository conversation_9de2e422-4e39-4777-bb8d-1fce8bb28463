import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { callPythonBackend } from '@/lib/backend'

export async function POST(req: NextRequest) {
  try {
    const payload = await req.json()
    const { id, title, date, time, type, max_reservations = 6 } = payload
    
    // Validate required fields
    if (!id || !title || !date || !time || type === undefined) {
      return NextResponse.json({
        success: false,
        message: 'Missing required fields: id, title, date, time, and type'
      }, { status: 400 })
    }
    
    console.log('Creating reservation via Python backend:', { id, title, date, time, type, max_reservations })
    
    // Map Next.js payload to Python backend format
    const backendPayload = {
      wa_id: id,
      customer_name: title,
      date_str: date,
      time_slot: time,
      reservation_type: type,
      hijri: false,
      ar: false,
      max_reservations
    }
    
    // Make request to Python backend
    const backendResponse = await callPythonBackend('/reservations', {
      method: 'POST',
      body: JSON.stringify(backendPayload)
    })
    
    console.log('Python backend reserve response:', backendResponse)
    
    return NextResponse.json(backendResponse)
    
  } catch (error) {
    console.error('Error creating reservation via Python backend:', error)
    return NextResponse.json({
      success: false,
      message: `Failed to create reservation: ${error instanceof Error ? error.message : 'Unknown error'}`
    }, { status: 500 })
  }
} 