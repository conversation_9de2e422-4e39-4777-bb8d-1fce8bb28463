/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/reservations/route";
exports.ids = ["app/api/reservations/route"];
exports.modules = {

/***/ "(rsc)/./app/api/reservations/route.ts":
/*!***************************************!*\
  !*** ./app/api/reservations/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_backend__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/backend */ \"(rsc)/./lib/backend.ts\");\n\n\nasync function GET(req) {\n    try {\n        const url = new URL(req.url);\n        const future = url.searchParams.get('future') === 'true';\n        const includeCancelled = url.searchParams.get('include_cancelled') === 'true';\n        // Make request to Python backend with same parameters\n        const params = new URLSearchParams({\n            future: future.toString(),\n            include_cancelled: includeCancelled.toString()\n        });\n        const backendResponse = await (0,_lib_backend__WEBPACK_IMPORTED_MODULE_1__.callPythonBackend)(`/reservations?${params}`);\n        // The Python backend should return the data in the expected format\n        // { success: true, data: Record<string, Reservation[]> }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(backendResponse);\n    } catch (error) {\n        console.error('Error fetching reservations from Python backend:', error);\n        // Return empty data structure on error to prevent breaking the frontend\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: `Failed to fetch reservations: ${error instanceof Error ? error.message : 'Unknown error'}`,\n            data: {}\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvYXBpL3Jlc2VydmF0aW9ucy9yb3V0ZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBMEM7QUFHTztBQUUxQyxlQUFlRSxJQUFJQyxHQUFnQjtJQUN4QyxJQUFJO1FBQ0YsTUFBTUMsTUFBTSxJQUFJQyxJQUFJRixJQUFJQyxHQUFHO1FBQzNCLE1BQU1FLFNBQVNGLElBQUlHLFlBQVksQ0FBQ0MsR0FBRyxDQUFDLGNBQWM7UUFDbEQsTUFBTUMsbUJBQW1CTCxJQUFJRyxZQUFZLENBQUNDLEdBQUcsQ0FBQyx5QkFBeUI7UUFJdkUsc0RBQXNEO1FBQ3RELE1BQU1FLFNBQVMsSUFBSUMsZ0JBQWdCO1lBQ2pDTCxRQUFRQSxPQUFPTSxRQUFRO1lBQ3ZCQyxtQkFBbUJKLGlCQUFpQkcsUUFBUTtRQUM5QztRQUVBLE1BQU1FLGtCQUFrQixNQUFNYiwrREFBaUJBLENBQUMsQ0FBQyxjQUFjLEVBQUVTLFFBQVE7UUFJekUsbUVBQW1FO1FBQ25FLHlEQUF5RDtRQUN6RCxPQUFPVixxREFBWUEsQ0FBQ2UsSUFBSSxDQUFDRDtJQUUzQixFQUFFLE9BQU9FLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLG9EQUFvREE7UUFFbEUsd0VBQXdFO1FBQ3hFLE9BQU9oQixxREFBWUEsQ0FBQ2UsSUFBSSxDQUN0QjtZQUNFRyxTQUFTO1lBQ1RDLFNBQVMsQ0FBQyw4QkFBOEIsRUFBRUgsaUJBQWlCSSxRQUFRSixNQUFNRyxPQUFPLEdBQUcsaUJBQWlCO1lBQ3BHRSxNQUFNLENBQUM7UUFDVCxHQUNBO1lBQUVDLFFBQVE7UUFBSTtJQUVsQjtBQUNGIiwic291cmNlcyI6WyJFOlxcRExcXFByb2plY3RzXFxweXRob24td2hhdHNhcHAtYm90XFxhdHRlbXB0LW5leHRqc1xcYXBwXFxhcGlcXHJlc2VydmF0aW9uc1xccm91dGUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInXG5pbXBvcnQgdHlwZSB7IE5leHRSZXF1ZXN0IH0gZnJvbSAnbmV4dC9zZXJ2ZXInXG5pbXBvcnQgdHlwZSB7IFJlc2VydmF0aW9uIH0gZnJvbSAnQC90eXBlcy9jYWxlbmRhcidcbmltcG9ydCB7IGNhbGxQeXRob25CYWNrZW5kIH0gZnJvbSAnQC9saWIvYmFja2VuZCdcblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIEdFVChyZXE6IE5leHRSZXF1ZXN0KSB7XG4gIHRyeSB7XG4gICAgY29uc3QgdXJsID0gbmV3IFVSTChyZXEudXJsKVxuICAgIGNvbnN0IGZ1dHVyZSA9IHVybC5zZWFyY2hQYXJhbXMuZ2V0KCdmdXR1cmUnKSA9PT0gJ3RydWUnXG4gICAgY29uc3QgaW5jbHVkZUNhbmNlbGxlZCA9IHVybC5zZWFyY2hQYXJhbXMuZ2V0KCdpbmNsdWRlX2NhbmNlbGxlZCcpID09PSAndHJ1ZSdcbiAgICBcbiAgXG4gICAgXG4gICAgLy8gTWFrZSByZXF1ZXN0IHRvIFB5dGhvbiBiYWNrZW5kIHdpdGggc2FtZSBwYXJhbWV0ZXJzXG4gICAgY29uc3QgcGFyYW1zID0gbmV3IFVSTFNlYXJjaFBhcmFtcyh7XG4gICAgICBmdXR1cmU6IGZ1dHVyZS50b1N0cmluZygpLFxuICAgICAgaW5jbHVkZV9jYW5jZWxsZWQ6IGluY2x1ZGVDYW5jZWxsZWQudG9TdHJpbmcoKVxuICAgIH0pXG4gICAgXG4gICAgY29uc3QgYmFja2VuZFJlc3BvbnNlID0gYXdhaXQgY2FsbFB5dGhvbkJhY2tlbmQoYC9yZXNlcnZhdGlvbnM/JHtwYXJhbXN9YClcbiAgICBcbiAgICBcbiAgICBcbiAgICAvLyBUaGUgUHl0aG9uIGJhY2tlbmQgc2hvdWxkIHJldHVybiB0aGUgZGF0YSBpbiB0aGUgZXhwZWN0ZWQgZm9ybWF0XG4gICAgLy8geyBzdWNjZXNzOiB0cnVlLCBkYXRhOiBSZWNvcmQ8c3RyaW5nLCBSZXNlcnZhdGlvbltdPiB9XG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKGJhY2tlbmRSZXNwb25zZSlcbiAgICBcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyByZXNlcnZhdGlvbnMgZnJvbSBQeXRob24gYmFja2VuZDonLCBlcnJvcilcbiAgICBcbiAgICAvLyBSZXR1cm4gZW1wdHkgZGF0YSBzdHJ1Y3R1cmUgb24gZXJyb3IgdG8gcHJldmVudCBicmVha2luZyB0aGUgZnJvbnRlbmRcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICB7IFxuICAgICAgICBzdWNjZXNzOiBmYWxzZSwgXG4gICAgICAgIG1lc3NhZ2U6IGBGYWlsZWQgdG8gZmV0Y2ggcmVzZXJ2YXRpb25zOiAke2Vycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ1Vua25vd24gZXJyb3InfWAsXG4gICAgICAgIGRhdGE6IHt9XG4gICAgICB9LFxuICAgICAgeyBzdGF0dXM6IDUwMCB9XG4gICAgKVxuICB9XG59ICJdLCJuYW1lcyI6WyJOZXh0UmVzcG9uc2UiLCJjYWxsUHl0aG9uQmFja2VuZCIsIkdFVCIsInJlcSIsInVybCIsIlVSTCIsImZ1dHVyZSIsInNlYXJjaFBhcmFtcyIsImdldCIsImluY2x1ZGVDYW5jZWxsZWQiLCJwYXJhbXMiLCJVUkxTZWFyY2hQYXJhbXMiLCJ0b1N0cmluZyIsImluY2x1ZGVfY2FuY2VsbGVkIiwiYmFja2VuZFJlc3BvbnNlIiwianNvbiIsImVycm9yIiwiY29uc29sZSIsInN1Y2Nlc3MiLCJtZXNzYWdlIiwiRXJyb3IiLCJkYXRhIiwic3RhdHVzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/api/reservations/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/backend.ts":
/*!************************!*\
  !*** ./lib/backend.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PYTHON_BACKEND_URL: () => (/* binding */ PYTHON_BACKEND_URL),\n/* harmony export */   callPythonBackend: () => (/* binding */ callPythonBackend)\n/* harmony export */ });\n// Backend configuration for Python API\nconst PYTHON_BACKEND_URL = \"http://localhost:8000\" || 0 || 0;\n// Helper function to make requests to Python backend\nasync function callPythonBackend(endpoint, options = {}) {\n    const url = `${PYTHON_BACKEND_URL}${endpoint}`;\n    const defaultOptions = {\n        headers: {\n            'Content-Type': 'application/json',\n            ...options.headers\n        }\n    };\n    const response = await fetch(url, {\n        ...defaultOptions,\n        ...options\n    });\n    if (!response.ok) {\n        throw new Error(`Backend request failed: ${response.status} ${response.statusText}`);\n    }\n    return response.json();\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/backend.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Freservations%2Froute&page=%2Fapi%2Freservations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Freservations%2Froute.ts&appDir=E%3A%5CDL%5CProjects%5Cpython-whatsapp-bot%5Cattempt-nextjs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CDL%5CProjects%5Cpython-whatsapp-bot%5Cattempt-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Freservations%2Froute&page=%2Fapi%2Freservations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Freservations%2Froute.ts&appDir=E%3A%5CDL%5CProjects%5Cpython-whatsapp-bot%5Cattempt-nextjs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CDL%5CProjects%5Cpython-whatsapp-bot%5Cattempt-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var E_DL_Projects_python_whatsapp_bot_attempt_nextjs_app_api_reservations_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/reservations/route.ts */ \"(rsc)/./app/api/reservations/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/reservations/route\",\n        pathname: \"/api/reservations\",\n        filename: \"route\",\n        bundlePath: \"app/api/reservations/route\"\n    },\n    resolvedPagePath: \"E:\\\\DL\\\\Projects\\\\python-whatsapp-bot\\\\attempt-nextjs\\\\app\\\\api\\\\reservations\\\\route.ts\",\n    nextConfigOutput,\n    userland: E_DL_Projects_python_whatsapp_bot_attempt_nextjs_app_api_reservations_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZyZXNlcnZhdGlvbnMlMkZyb3V0ZSZwYWdlPSUyRmFwaSUyRnJlc2VydmF0aW9ucyUyRnJvdXRlJmFwcFBhdGhzPSZwYWdlUGF0aD1wcml2YXRlLW5leHQtYXBwLWRpciUyRmFwaSUyRnJlc2VydmF0aW9ucyUyRnJvdXRlLnRzJmFwcERpcj1FJTNBJTVDREwlNUNQcm9qZWN0cyU1Q3B5dGhvbi13aGF0c2FwcC1ib3QlNUNhdHRlbXB0LW5leHRqcyU1Q2FwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9RSUzQSU1Q0RMJTVDUHJvamVjdHMlNUNweXRob24td2hhdHNhcHAtYm90JTVDYXR0ZW1wdC1uZXh0anMmaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQStGO0FBQ3ZDO0FBQ3FCO0FBQ3VDO0FBQ3BIO0FBQ0E7QUFDQTtBQUNBLHdCQUF3Qix5R0FBbUI7QUFDM0M7QUFDQSxjQUFjLGtFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxZQUFZO0FBQ1osQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLFFBQVEsc0RBQXNEO0FBQzlEO0FBQ0EsV0FBVyw0RUFBVztBQUN0QjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQzBGOztBQUUxRiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEFwcFJvdXRlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1tb2R1bGVzL2FwcC1yb3V0ZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IHBhdGNoRmV0Y2ggYXMgX3BhdGNoRmV0Y2ggfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9saWIvcGF0Y2gtZmV0Y2hcIjtcbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCJFOlxcXFxETFxcXFxQcm9qZWN0c1xcXFxweXRob24td2hhdHNhcHAtYm90XFxcXGF0dGVtcHQtbmV4dGpzXFxcXGFwcFxcXFxhcGlcXFxccmVzZXJ2YXRpb25zXFxcXHJvdXRlLnRzXCI7XG4vLyBXZSBpbmplY3QgdGhlIG5leHRDb25maWdPdXRwdXQgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IG5leHRDb25maWdPdXRwdXQgPSBcIlwiXG5jb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBSb3V0ZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUk9VVEUsXG4gICAgICAgIHBhZ2U6IFwiL2FwaS9yZXNlcnZhdGlvbnMvcm91dGVcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9yZXNlcnZhdGlvbnNcIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL3Jlc2VydmF0aW9ucy9yb3V0ZVwiXG4gICAgfSxcbiAgICByZXNvbHZlZFBhZ2VQYXRoOiBcIkU6XFxcXERMXFxcXFByb2plY3RzXFxcXHB5dGhvbi13aGF0c2FwcC1ib3RcXFxcYXR0ZW1wdC1uZXh0anNcXFxcYXBwXFxcXGFwaVxcXFxyZXNlcnZhdGlvbnNcXFxccm91dGUudHNcIixcbiAgICBuZXh0Q29uZmlnT3V0cHV0LFxuICAgIHVzZXJsYW5kXG59KTtcbi8vIFB1bGwgb3V0IHRoZSBleHBvcnRzIHRoYXQgd2UgbmVlZCB0byBleHBvc2UgZnJvbSB0aGUgbW9kdWxlLiBUaGlzIHNob3VsZFxuLy8gYmUgZWxpbWluYXRlZCB3aGVuIHdlJ3ZlIG1vdmVkIHRoZSBvdGhlciByb3V0ZXMgdG8gdGhlIG5ldyBmb3JtYXQuIFRoZXNlXG4vLyBhcmUgdXNlZCB0byBob29rIGludG8gdGhlIHJvdXRlLlxuY29uc3QgeyB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MgfSA9IHJvdXRlTW9kdWxlO1xuZnVuY3Rpb24gcGF0Y2hGZXRjaCgpIHtcbiAgICByZXR1cm4gX3BhdGNoRmV0Y2goe1xuICAgICAgICB3b3JrQXN5bmNTdG9yYWdlLFxuICAgICAgICB3b3JrVW5pdEFzeW5jU3RvcmFnZVxuICAgIH0pO1xufVxuZXhwb3J0IHsgcm91dGVNb2R1bGUsIHdvcmtBc3luY1N0b3JhZ2UsIHdvcmtVbml0QXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcywgcGF0Y2hGZXRjaCwgIH07XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1yb3V0ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Freservations%2Froute&page=%2Fapi%2Freservations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Freservations%2Froute.ts&appDir=E%3A%5CDL%5CProjects%5Cpython-whatsapp-bot%5Cattempt-nextjs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CDL%5CProjects%5Cpython-whatsapp-bot%5Cattempt-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Freservations%2Froute&page=%2Fapi%2Freservations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Freservations%2Froute.ts&appDir=E%3A%5CDL%5CProjects%5Cpython-whatsapp-bot%5Cattempt-nextjs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CDL%5CProjects%5Cpython-whatsapp-bot%5Cattempt-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();