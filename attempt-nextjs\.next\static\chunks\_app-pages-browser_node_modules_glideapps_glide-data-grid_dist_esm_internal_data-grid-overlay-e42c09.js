"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_glideapps_glide-data-grid_dist_esm_internal_data-grid-overlay-e42c09"],{

/***/ "(app-pages-browser)/./node_modules/@glideapps/glide-data-grid/dist/esm/internal/data-grid-overlay-editor/data-grid-overlay-editor-style.js":
/*!******************************************************************************************************************************!*\
  !*** ./node_modules/@glideapps/glide-data-grid/dist/esm/internal/data-grid-overlay-editor/data-grid-overlay-editor-style.js ***!
  \******************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DataGridOverlayEditorStyle: () => (/* binding */ DataGridOverlayEditorStyle)\n/* harmony export */ });\n/* harmony import */ var _linaria_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @linaria/react */ \"(app-pages-browser)/./node_modules/@linaria/react/dist/index.mjs\");\n\nconst _exp2 = /*#__PURE__*/ ()=>(p)=>p.targetX;\nconst _exp3 = /*#__PURE__*/ ()=>(p)=>p.targetY;\nconst _exp4 = /*#__PURE__*/ ()=>(p)=>p.targetWidth;\nconst _exp5 = /*#__PURE__*/ ()=>(p)=>p.targetHeight;\nconst _exp6 = /*#__PURE__*/ ()=>(p)=>p.targetY + 10;\nconst _exp7 = /*#__PURE__*/ ()=>(p)=>Math.max(0, (p.targetHeight - 28) / 2);\nconst DataGridOverlayEditorStyle = /*#__PURE__*/ (0,_linaria_react__WEBPACK_IMPORTED_MODULE_0__.styled)('div')({\n    name: \"DataGridOverlayEditorStyle\",\n    class: \"gdg-d19meir1\",\n    propsAsIs: false,\n    vars: {\n        \"d19meir1-0\": [\n            _exp3(),\n            \"px\"\n        ],\n        \"d19meir1-1\": [\n            _exp2(),\n            \"px\"\n        ],\n        \"d19meir1-2\": [\n            _exp4(),\n            \"px\"\n        ],\n        \"d19meir1-3\": [\n            _exp5(),\n            \"px\"\n        ],\n        \"d19meir1-4\": [\n            _exp6(),\n            \"px\"\n        ],\n        \"d19meir1-5\": [\n            _exp7(),\n            \"px\"\n        ]\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AZ2xpZGVhcHBzL2dsaWRlLWRhdGEtZ3JpZC9kaXN0L2VzbS9pbnRlcm5hbC9kYXRhLWdyaWQtb3ZlcmxheS1lZGl0b3IvZGF0YS1ncmlkLW92ZXJsYXktZWRpdG9yLXN0eWxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXdDO0FBUXhDLE1BQU0sQ0FBQyxNQUFNLHlCQUEwQixHQUFHLE1BQU07Ozs7Ozs7O3VCQVEzQixDQUFDLENBQUM7OztlQUdaLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDO1lBQUEsTUFBTztZQUFBO1NBQUE7cUJBQ1IsQ0FBQztZQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7WUFBQTtTQUFBO3NCQUNOO1lBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDO1lBQUE7U0FBQTs7Ozs7Ozs7Ozs7ZUFHTSxDQUFDO1NBQUMiLCJzb3VyY2VzIjpbIkU6XFxzcmNcXGludGVybmFsXFxkYXRhLWdyaWQtb3ZlcmxheS1lZGl0b3JcXGRhdGEtZ3JpZC1vdmVybGF5LWVkaXRvci1zdHlsZS50c3giXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@glideapps/glide-data-grid/dist/esm/internal/data-grid-overlay-editor/data-grid-overlay-editor-style.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@glideapps/glide-data-grid/dist/esm/internal/data-grid-overlay-editor/data-grid-overlay-editor.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/@glideapps/glide-data-grid/dist/esm/internal/data-grid-overlay-editor/data-grid-overlay-editor.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\");\n/* harmony import */ var _click_outside_container_click_outside_container_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../click-outside-container/click-outside-container.js */ \"(app-pages-browser)/./node_modules/@glideapps/glide-data-grid/dist/esm/internal/click-outside-container/click-outside-container.js\");\n/* harmony import */ var _common_styles_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../common/styles.js */ \"(app-pages-browser)/./node_modules/@glideapps/glide-data-grid/dist/esm/common/styles.js\");\n/* harmony import */ var _data_grid_data_grid_types_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../data-grid/data-grid-types.js */ \"(app-pages-browser)/./node_modules/@glideapps/glide-data-grid/dist/esm/internal/data-grid/data-grid-types.js\");\n/* harmony import */ var _data_grid_overlay_editor_style_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./data-grid-overlay-editor-style.js */ \"(app-pages-browser)/./node_modules/@glideapps/glide-data-grid/dist/esm/internal/data-grid-overlay-editor/data-grid-overlay-editor-style.js\");\n/* harmony import */ var _use_stay_on_screen_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./use-stay-on-screen.js */ \"(app-pages-browser)/./node_modules/@glideapps/glide-data-grid/dist/esm/internal/data-grid-overlay-editor/use-stay-on-screen.js\");\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst DataGridOverlayEditor = (p)=>{\n    _s();\n    const { target, content, onFinishEditing: onFinishEditingIn, forceEditMode, initialValue, imageEditorOverride, markdownDivCreateNode, highlight, className, theme, id, cell, bloom, validateCell, getCellRenderer, provideEditor, isOutsideClick } = p;\n    const [tempValue, setTempValueRaw] = react__WEBPACK_IMPORTED_MODULE_0__.useState(forceEditMode ? content : undefined);\n    const lastValueRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(tempValue !== null && tempValue !== void 0 ? tempValue : content);\n    lastValueRef.current = tempValue !== null && tempValue !== void 0 ? tempValue : content;\n    const [isValid, setIsValid] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        \"DataGridOverlayEditor.useState\": ()=>{\n            if (validateCell === undefined) return true;\n            return !((0,_data_grid_data_grid_types_js__WEBPACK_IMPORTED_MODULE_2__.isEditableGridCell)(content) && (validateCell === null || validateCell === void 0 ? void 0 : validateCell(cell, content, lastValueRef.current)) === false);\n        }\n    }[\"DataGridOverlayEditor.useState\"]);\n    const onFinishEditing = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"DataGridOverlayEditor.useCallback[onFinishEditing]\": (newCell, movement)=>{\n            onFinishEditingIn(isValid ? newCell : undefined, movement);\n        }\n    }[\"DataGridOverlayEditor.useCallback[onFinishEditing]\"], [\n        isValid,\n        onFinishEditingIn\n    ]);\n    const setTempValue = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"DataGridOverlayEditor.useCallback[setTempValue]\": (newVal)=>{\n            if (validateCell !== undefined && newVal !== undefined && (0,_data_grid_data_grid_types_js__WEBPACK_IMPORTED_MODULE_2__.isEditableGridCell)(newVal)) {\n                const validResult = validateCell(cell, newVal, lastValueRef.current);\n                if (validResult === false) {\n                    setIsValid(false);\n                } else if (typeof validResult === \"object\") {\n                    newVal = validResult;\n                    setIsValid(true);\n                } else {\n                    setIsValid(true);\n                }\n            }\n            setTempValueRaw(newVal);\n        }\n    }[\"DataGridOverlayEditor.useCallback[setTempValue]\"], [\n        cell,\n        validateCell\n    ]);\n    const finished = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const customMotion = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n    const onClickOutside = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"DataGridOverlayEditor.useCallback[onClickOutside]\": ()=>{\n            onFinishEditing(tempValue, [\n                0,\n                0\n            ]);\n            finished.current = true;\n        }\n    }[\"DataGridOverlayEditor.useCallback[onClickOutside]\"], [\n        tempValue,\n        onFinishEditing\n    ]);\n    const onEditorFinished = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"DataGridOverlayEditor.useCallback[onEditorFinished]\": (newValue, movement)=>{\n            var _ref;\n            onFinishEditing(newValue, (_ref = movement !== null && movement !== void 0 ? movement : customMotion.current) !== null && _ref !== void 0 ? _ref : [\n                0,\n                0\n            ]);\n            finished.current = true;\n        }\n    }[\"DataGridOverlayEditor.useCallback[onEditorFinished]\"], [\n        onFinishEditing\n    ]);\n    const onKeyDown = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"DataGridOverlayEditor.useCallback[onKeyDown]\": async (event)=>{\n            let save = false;\n            if (event.key === \"Escape\") {\n                event.stopPropagation();\n                event.preventDefault();\n                customMotion.current = [\n                    0,\n                    0\n                ];\n            } else if (event.key === \"Enter\" && !event.shiftKey) {\n                event.stopPropagation();\n                event.preventDefault();\n                customMotion.current = [\n                    0,\n                    1\n                ];\n                save = true;\n            } else if (event.key === \"Tab\") {\n                event.stopPropagation();\n                event.preventDefault();\n                customMotion.current = [\n                    event.shiftKey ? -1 : 1,\n                    0\n                ];\n                save = true;\n            }\n            window.setTimeout({\n                \"DataGridOverlayEditor.useCallback[onKeyDown]\": ()=>{\n                    if (!finished.current && customMotion.current !== undefined) {\n                        onFinishEditing(save ? tempValue : undefined, customMotion.current);\n                        finished.current = true;\n                    }\n                }\n            }[\"DataGridOverlayEditor.useCallback[onKeyDown]\"], 0);\n        }\n    }[\"DataGridOverlayEditor.useCallback[onKeyDown]\"], [\n        onFinishEditing,\n        tempValue\n    ]);\n    const targetValue = tempValue !== null && tempValue !== void 0 ? tempValue : content;\n    const [editorProvider, useLabel] = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"DataGridOverlayEditor.useMemo\": ()=>{\n            var _getCellRenderer_provideEditor, _getCellRenderer;\n            if ((0,_data_grid_data_grid_types_js__WEBPACK_IMPORTED_MODULE_2__.isInnerOnlyCell)(content)) return [];\n            const external = provideEditor === null || provideEditor === void 0 ? void 0 : provideEditor(content);\n            if (external !== undefined) return [\n                external,\n                false\n            ];\n            return [\n                (_getCellRenderer = getCellRenderer(content)) === null || _getCellRenderer === void 0 ? void 0 : (_getCellRenderer_provideEditor = _getCellRenderer.provideEditor) === null || _getCellRenderer_provideEditor === void 0 ? void 0 : _getCellRenderer_provideEditor.call(_getCellRenderer, content),\n                false\n            ];\n        }\n    }[\"DataGridOverlayEditor.useMemo\"], [\n        content,\n        getCellRenderer,\n        provideEditor\n    ]);\n    const { ref, style: stayOnScreenStyle } = (0,_use_stay_on_screen_js__WEBPACK_IMPORTED_MODULE_3__.useStayOnScreen)();\n    let pad = true;\n    let editor;\n    let style = true;\n    let styleOverride;\n    if (editorProvider !== undefined) {\n        pad = editorProvider.disablePadding !== true;\n        style = editorProvider.disableStyling !== true;\n        const isObjectEditor = (0,_data_grid_data_grid_types_js__WEBPACK_IMPORTED_MODULE_2__.isObjectEditorCallbackResult)(editorProvider);\n        if (isObjectEditor) {\n            styleOverride = editorProvider.styleOverride;\n        }\n        const CustomEditor = isObjectEditor ? editorProvider.editor : editorProvider;\n        editor = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(CustomEditor, {\n            isHighlighted: highlight,\n            onChange: setTempValue,\n            value: targetValue,\n            initialValue: initialValue,\n            onFinishedEditing: onEditorFinished,\n            validatedSelection: (0,_data_grid_data_grid_types_js__WEBPACK_IMPORTED_MODULE_2__.isEditableGridCell)(targetValue) ? targetValue.selectionRange : undefined,\n            forceEditMode: forceEditMode,\n            target: target,\n            imageEditorOverride: imageEditorOverride,\n            markdownDivCreateNode: markdownDivCreateNode,\n            isValid: isValid,\n            theme: theme\n        });\n    }\n    styleOverride = {\n        ...styleOverride,\n        ...stayOnScreenStyle\n    };\n    // Consider imperatively creating and adding the element to the dom?\n    const portalElement = document.getElementById(\"portal\");\n    if (portalElement === null) {\n        // eslint-disable-next-line no-console\n        console.error('Cannot open Data Grid overlay editor, because portal not found.  Please add `<div id=\"portal\" />` as the last child of your `<body>`.');\n        return null;\n    }\n    let classWrap = style ? \"gdg-style\" : \"gdg-unstyle\";\n    if (!isValid) {\n        classWrap += \" gdg-invalid\";\n    }\n    if (pad) {\n        classWrap += \" gdg-pad\";\n    }\n    var _bloom_;\n    const bloomX = (_bloom_ = bloom === null || bloom === void 0 ? void 0 : bloom[0]) !== null && _bloom_ !== void 0 ? _bloom_ : 1;\n    var _bloom_1;\n    const bloomY = (_bloom_1 = bloom === null || bloom === void 0 ? void 0 : bloom[1]) !== null && _bloom_1 !== void 0 ? _bloom_1 : 1;\n    return /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal)(/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(_common_styles_js__WEBPACK_IMPORTED_MODULE_4__.ThemeContext.Provider, {\n        value: theme\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(_click_outside_container_click_outside_container_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        style: (0,_common_styles_js__WEBPACK_IMPORTED_MODULE_4__.makeCSSStyle)(theme),\n        className: className,\n        onClickOutside: onClickOutside,\n        isOutsideClick: isOutsideClick\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(_data_grid_overlay_editor_style_js__WEBPACK_IMPORTED_MODULE_6__.DataGridOverlayEditorStyle, {\n        ref: ref,\n        id: id,\n        className: classWrap,\n        style: styleOverride,\n        as: useLabel === true ? \"label\" : undefined,\n        targetX: target.x - bloomX,\n        targetY: target.y - bloomY,\n        targetWidth: target.width + bloomX * 2,\n        targetHeight: target.height + bloomY * 2\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: \"gdg-clip-region\",\n        onKeyDown: onKeyDown\n    }, editor)))), portalElement);\n};\n_s(DataGridOverlayEditor, \"0jiczSLhM8yYDlO8PcA5scsGajQ=\", false, function() {\n    return [\n        _use_stay_on_screen_js__WEBPACK_IMPORTED_MODULE_3__.useStayOnScreen\n    ];\n});\n_c = DataGridOverlayEditor;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DataGridOverlayEditor); //# sourceMappingURL=data-grid-overlay-editor.js.map\nvar _c;\n$RefreshReg$(_c, \"DataGridOverlayEditor\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@glideapps/glide-data-grid/dist/esm/internal/data-grid-overlay-editor/data-grid-overlay-editor.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@glideapps/glide-data-grid/dist/esm/internal/data-grid-overlay-editor/use-stay-on-screen.js":
/*!******************************************************************************************************************!*\
  !*** ./node_modules/@glideapps/glide-data-grid/dist/esm/internal/data-grid-overlay-editor/use-stay-on-screen.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useStayOnScreen: () => (/* binding */ useStayOnScreen)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nfunction useRefState() {\n    _s();\n    const [refState, setRefState] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    return [\n        refState !== null && refState !== void 0 ? refState : undefined,\n        setRefState\n    ];\n}\n_s(useRefState, \"XsRCxVPJDIYww+Nhw2PPn+Uz8wY=\");\nfunction useStayOnScreen() {\n    _s1();\n    const [ref, setRef] = useRefState();\n    const [xOffset, setXOffset] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [isIntersecting, setIsIntersecting] = react__WEBPACK_IMPORTED_MODULE_0__.useState(true);\n    react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect({\n        \"useStayOnScreen.useLayoutEffect\": ()=>{\n            if (ref === undefined) return;\n            if (!(\"IntersectionObserver\" in window)) return;\n            const observer = new IntersectionObserver({\n                \"useStayOnScreen.useLayoutEffect\": (ents)=>{\n                    if (ents.length === 0) return;\n                    setIsIntersecting(ents[0].isIntersecting);\n                }\n            }[\"useStayOnScreen.useLayoutEffect\"], {\n                threshold: 1\n            });\n            observer.observe(ref);\n            return ({\n                \"useStayOnScreen.useLayoutEffect\": ()=>observer.disconnect()\n            })[\"useStayOnScreen.useLayoutEffect\"];\n        }\n    }[\"useStayOnScreen.useLayoutEffect\"], [\n        ref\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useStayOnScreen.useEffect\": ()=>{\n            if (isIntersecting || ref === undefined) return;\n            let rafHandle;\n            const fn = {\n                \"useStayOnScreen.useEffect.fn\": ()=>{\n                    const { right: refRight } = ref.getBoundingClientRect();\n                    setXOffset({\n                        \"useStayOnScreen.useEffect.fn\": (cv)=>Math.min(cv + window.innerWidth - refRight - 10, 0)\n                    }[\"useStayOnScreen.useEffect.fn\"]);\n                    rafHandle = requestAnimationFrame(fn);\n                }\n            }[\"useStayOnScreen.useEffect.fn\"];\n            rafHandle = requestAnimationFrame(fn);\n            return ({\n                \"useStayOnScreen.useEffect\": ()=>{\n                    if (rafHandle !== undefined) {\n                        cancelAnimationFrame(rafHandle);\n                    }\n                }\n            })[\"useStayOnScreen.useEffect\"];\n        }\n    }[\"useStayOnScreen.useEffect\"], [\n        ref,\n        isIntersecting\n    ]);\n    const style = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"useStayOnScreen.useMemo[style]\": ()=>{\n            return {\n                transform: \"translateX(\".concat(xOffset, \"px)\")\n            };\n        }\n    }[\"useStayOnScreen.useMemo[style]\"], [\n        xOffset\n    ]);\n    return {\n        ref: setRef,\n        style\n    };\n} //# sourceMappingURL=use-stay-on-screen.js.map\n_s1(useStayOnScreen, \"biKOaWATPBHfza3ZwRTr3chtE+c=\", false, function() {\n    return [\n        useRefState\n    ];\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AZ2xpZGVhcHBzL2dsaWRlLWRhdGEtZ3JpZC9kaXN0L2VzbS9pbnRlcm5hbC9kYXRhLWdyaWQtb3ZlcmxheS1lZGl0b3IvdXNlLXN0YXktb24tc2NyZWVuLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUErQjtBQUUvQjs7SUFDSSxNQUFNLENBQUMsUUFBUSxFQUFFLFdBQVcsQ0FBQyxHQUFHLDJDQUFjLEVBQXNCLENBQUM7SUFDckUsT0FBTzttREFBQyxRQUFRLEdBQUksU0FBUztRQUFFLFdBQVc7S0FBQyxDQUFDO0FBQ2hELENBQUM7R0FIUSxXQUFXO0FBVWQ7O0lBQ0YsTUFBTSxDQUFDLEdBQUcsRUFBRSxNQUFNLENBQUM7SUFDbkIsTUFBTSxDQUFDLE9BQU8sRUFBRSxVQUFVLENBQUMsR0FBRywyQ0FBYyxDQUFDLENBQUMsQ0FBQyxDQUFDO0lBQ2hELE1BQU0sQ0FBQyxjQUFjLEVBQUUsaUJBQWlCLENBQUMsR0FBRywyQ0FBYyxDQUFDLElBQUksQ0FBQyxDQUFDO0lBRWpFLGtEQUFxQjsyQ0FBQyxHQUFHLEVBQUU7WUFDdkIsSUFBSSxHQUFHLEtBQUssU0FBUyxFQUFFLE9BQU87WUFDOUIsSUFBSSxDQUFDLENBQUMsc0JBQXNCLElBQUksT0FBTSxDQUFDLENBQUUsT0FBTztZQUVoRCxNQUFNLFFBQVEsR0FBRyxJQUFJLG9CQUFvQjtvREFDckMsSUFBSSxDQUFDLEVBQUU7b0JBQ0gsSUFBSSxJQUFJLENBQUMsTUFBTSxLQUFLLENBQUMsRUFBRSxPQUFPO29CQUM5QixpQkFBaUIsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUMsY0FBYyxDQUFDLENBQUM7Z0JBQzlDLENBQUM7a0RBQ0Q7Z0JBQUUsU0FBUyxFQUFFLENBQUM7WUFBQSxDQUFFLENBQ25CLENBQUM7WUFDRixRQUFRLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxDQUFDO1lBRXRCO21EQUFPLEdBQUcsQ0FBRyxDQUFELE9BQVMsQ0FBQyxVQUFVLEVBQUU7O1FBQ3RDLENBQUM7MENBQUU7UUFBQyxHQUFHO0tBQUMsQ0FBQyxDQUFDO0lBRVYsNENBQWU7cUNBQUMsR0FBRyxFQUFFO1lBQ2pCLElBQUksY0FBYyxJQUFJLEdBQUcsS0FBSyxTQUFTLEVBQUUsT0FBTztZQUVoRCxJQUFJLFNBQTZCLENBQUM7WUFDbEMsTUFBTSxFQUFFO2dEQUFHLEdBQUcsRUFBRTtvQkFDWixNQUFNLEVBQUUsS0FBSyxFQUFFLFFBQVEsRUFBRSxHQUFHLEdBQUcsQ0FBQyxxQkFBcUIsRUFBRSxDQUFDO29CQUV4RCxVQUFVO3dEQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUFFLEdBQUcsTUFBTSxDQUFDLFVBQVUsR0FBRyxRQUFRLEdBQUcsRUFBRSxFQUFFLENBQUMsQ0FBQzs7b0JBQ3BFLFNBQVMsR0FBRyxxQkFBcUIsQ0FBQyxFQUFFLENBQUMsQ0FBQztnQkFDMUMsQ0FBQzs7WUFFRCxTQUFTLEdBQUcscUJBQXFCLENBQUMsRUFBRSxDQUFDLENBQUM7WUFDdEM7NkNBQU8sR0FBRyxFQUFFO29CQUNSLElBQUksU0FBUyxLQUFLLFNBQVMsRUFBRTt3QkFDekIsb0JBQW9CLENBQUMsU0FBUyxDQUFDLENBQUM7cUJBQ25DO2dCQUNMLENBQUM7O1FBQ0wsQ0FBQztvQ0FBRTtRQUFDLEdBQUc7UUFBRSxjQUFjO0tBQUMsQ0FBQyxDQUFDO0lBRTFCLE1BQU0sS0FBSyxHQUFHLDBDQUFhOzBDQUFDLEdBQUcsRUFBRTtZQUM3QixPQUFPO2dCQUFFLFNBQVMsRUFBRSxjQUFxQixJQUFLLEdBQVosT0FBTztZQUFLLENBQUUsQ0FBQztRQUNyRCxDQUFDO3lDQUFFO1FBQUMsT0FBTztLQUFDLENBQUMsQ0FBQztJQUVkLE9BQU87UUFDSCxHQUFHLEVBQUUsTUFBTTtRQUNYLEtBQUs7S0FDUixDQUFDO0FBQ04sQ0FBQztJQWhEZSxlQUFlOztRQUNMLFdBQVciLCJzb3VyY2VzIjpbIkU6XFxzcmNcXGludGVybmFsXFxkYXRhLWdyaWQtb3ZlcmxheS1lZGl0b3JcXHVzZS1zdGF5LW9uLXNjcmVlbi50cyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@glideapps/glide-data-grid/dist/esm/internal/data-grid-overlay-editor/use-stay-on-screen.js\n"));

/***/ })

}]);