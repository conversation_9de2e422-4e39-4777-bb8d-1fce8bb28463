(function(n,t){typeof exports=="object"&&typeof module!="undefined"?module.exports=t():typeof define=="function"&&define.amd?define(t):n.moment=t()})(this,function(){"use strict";function r(){return to.apply(null,arguments)}function tl(n){to=n}function oi(n){return Object.prototype.toString.call(n)==="[object Array]"}function uu(n){return n instanceof Date||Object.prototype.toString.call(n)==="[object Date]"}function io(n,t){for(var r=[],i=0;i<n.length;++i)r.push(t(n[i],i));return r}function pt(n,t){return Object.prototype.hasOwnProperty.call(n,t)}function fu(n,t){for(var i in t)pt(t,i)&&(n[i]=t[i]);return pt(t,"toString")&&(n.toString=t.toString),pt(t,"valueOf")&&(n.valueOf=t.valueOf),n}function ki(n,t,i,r){return hs(n,t,i,r,!0).utc()}function il(){return{empty:!1,unusedTokens:[],unusedInput:[],overflow:-2,charsLeftOver:0,nullInput:!1,invalidMonth:null,invalidFormat:!1,userInvalidated:!1,iso:!1}}function o(n){return n._pf==null&&(n._pf=il()),n._pf}function lf(n){if(n._isValid==null){var t=o(n);n._isValid=!isNaN(n._d.getTime())&&t.overflow<0&&!t.empty&&!t.invalidMonth&&!t.invalidWeekday&&!t.nullInput&&!t.invalidFormat&&!t.userInvalidated;n._strict&&(n._isValid=n._isValid&&t.charsLeftOver===0&&t.unusedTokens.length===0&&t.bigHour===undefined)}return n._isValid}function eu(n){var t=ki(NaN);return n!=null?fu(o(t),n):o(t).userInvalidated=!0,t}function y(n){return n===void 0}function vf(n,t){var u,i,r;if(y(t._isAMomentObject)||(n._isAMomentObject=t._isAMomentObject),y(t._i)||(n._i=t._i),y(t._f)||(n._f=t._f),y(t._l)||(n._l=t._l),y(t._strict)||(n._strict=t._strict),y(t._tzm)||(n._tzm=t._tzm),y(t._isUTC)||(n._isUTC=t._isUTC),y(t._offset)||(n._offset=t._offset),y(t._pf)||(n._pf=o(t)),y(t._locale)||(n._locale=t._locale),af.length>0)for(u in af)i=af[u],r=t[i],y(r)||(n[i]=r);return n}function or(n){vf(this,n);this._d=new Date(n._d!=null?n._d.getTime():NaN);yf===!1&&(yf=!0,r.updateOffset(this),yf=!1)}function ni(n){return n instanceof or||n!=null&&n._isAMomentObject!=null}function p(n){return n<0?Math.ceil(n):Math.floor(n)}function e(n){var t=+n,i=0;return t!==0&&isFinite(t)&&(i=p(t)),i}function ro(n,t,i){for(var f=Math.min(n.length,t.length),o=Math.abs(n.length-t.length),u=0,r=0;r<f;r++)(i&&n[r]!==t[r]||!i&&e(n[r])!==e(t[r]))&&u++;return u+o}function uo(){}function fo(n){return n?n.toLowerCase().replace("_","-"):n}function rl(n){for(var r=0,i,t,f,u;r<n.length;){for(u=fo(n[r]).split("-"),i=u.length,t=fo(n[r+1]),t=t?t.split("-"):null;i>0;){if(f=eo(u.slice(0,i).join("-")),f)return f;if(t&&t.length>=i&&ro(u,t,!0)>=i-1)break;i--}r++}return null}function eo(n){var t=null;if(!si[n]&&typeof module!="undefined"&&module&&module.exports)try{t=ou._abbr;require("./locale/"+n);sr(t)}catch(i){}return si[n]}function sr(n,t){var i;return n&&(i=y(t)?hi(n):oo(n,t),i&&(ou=i)),ou._abbr}function oo(n,t){return t!==null?(t.abbr=n,si[n]=si[n]||new uo,si[n].set(t),sr(n),si[n]):(delete si[n],null)}function hi(n){var t;if(n&&n._locale&&n._locale._abbr&&(n=n._locale._abbr),!n)return ou;if(!oi(n)){if(t=eo(n),t)return t;n=[n]}return rl(n)}function v(n,t){var i=n.toLowerCase();hr[i]=hr[i+"s"]=hr[t]=n}function b(n){return typeof n=="string"?hr[n]||hr[n.toLowerCase()]:undefined}function so(n){var r={},t;for(var i in n)pt(n,i)&&(t=b(i),t&&(r[t]=n[i]));return r}function wt(n){return n instanceof Function||Object.prototype.toString.call(n)==="[object Function]"}function di(n,t){return function(i){return i!=null?(ho(this,n,i),r.updateOffset(this,t),this):su(this,n)}}function su(n,t){return n.isValid()?n._d["get"+(n._isUTC?"UTC":"")+t]():NaN}function ho(n,t,i){n.isValid()&&n._d["set"+(n._isUTC?"UTC":"")+t](i)}function co(n,t){var i;if(typeof n=="object")for(i in n)this.set(i,n[i]);else if(n=b(n),wt(this[n]))return this[n](t);return this}function bt(n,t,i){var r=""+Math.abs(n),u=t-r.length,f=n>=0;return(f?i?"+":"":"-")+Math.pow(10,Math.max(0,u)).toString().substr(1)+r}function u(n,t,i,r){var u=r;typeof r=="string"&&(u=function(){return this[r]()});n&&(gi[n]=u);t&&(gi[t[0]]=function(){return bt(u.apply(this,arguments),t[1],t[2])});i&&(gi[i]=function(){return this.localeData().ordinal(u.apply(this,arguments),n)})}function ul(n){return n.match(/\[[\s\S]/)?n.replace(/^\[|\]$/g,""):n.replace(/\\/g,"")}function fl(n){for(var i=n.match(lo),t=0,r=i.length;t<r;t++)i[t]=gi[i[t]]?gi[i[t]]:ul(i[t]);return function(u){var f="";for(t=0;t<r;t++)f+=i[t]instanceof Function?i[t].call(u,n):i[t];return f}}function wf(n,t){return n.isValid()?(t=ao(t,n.localeData()),pf[t]=pf[t]||fl(t),pf[t](n)):n.localeData().invalidDate()}function ao(n,t){function r(n){return t.longDateFormat(n)||n}var i=5;for(hu.lastIndex=0;i>=0&&hu.test(n);)n=n.replace(hu,r),hu.lastIndex=0,i-=1;return n}function i(n,t,i){df[n]=wt(t)?t:function(n){return n&&i?i:t}}function ol(n,t){return pt(df,n)?df[n](t._strict,t._locale):new RegExp(sl(n))}function sl(n){return pu(n.replace("\\","").replace(/\\(\[)|\\(\])|\[([^\]\[]*)\]|\\(.)/g,function(n,t,i,r,u){return t||i||r||u}))}function pu(n){return n.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")}function h(n,t){var i,r=t;for(typeof n=="string"&&(n=[n]),typeof t=="number"&&(r=function(n,i){i[t]=e(n)}),i=0;i<n.length;i++)gf[n[i]]=r}function lr(n,t){h(n,function(n,i,r,u){r._w=r._w||{};t(n,r._w,r,u)})}function hl(n,t,i){t!=null&&pt(gf,n)&&gf[n](t,i._a,i,n)}function ne(n,t){return new Date(Date.UTC(n,t+1,0)).getUTCDate()}function al(n,t){return oi(this._months)?this._months[n.month()]:this._months[te.test(t)?"format":"standalone"][n.month()]}function vl(n,t){return oi(this._monthsShort)?this._monthsShort[n.month()]:this._monthsShort[te.test(t)?"format":"standalone"][n.month()]}function yl(n,t,i){var r,u,f;for(this._monthsParse||(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[]),r=0;r<12;r++)if((u=ki([2e3,r]),i&&!this._longMonthsParse[r]&&(this._longMonthsParse[r]=new RegExp("^"+this.months(u,"").replace(".","")+"$","i"),this._shortMonthsParse[r]=new RegExp("^"+this.monthsShort(u,"").replace(".","")+"$","i")),i||this._monthsParse[r]||(f="^"+this.months(u,"")+"|^"+this.monthsShort(u,""),this._monthsParse[r]=new RegExp(f.replace(".",""),"i")),i&&t==="MMMM"&&this._longMonthsParse[r].test(n))||i&&t==="MMM"&&this._shortMonthsParse[r].test(n)||!i&&this._monthsParse[r].test(n))return r}function go(n,t){var i;return n.isValid()?typeof t=="string"&&(t=n.localeData().monthsParse(t),typeof t!="number")?n:(i=Math.min(n.date(),ne(n.year(),t)),n._d["set"+(n._isUTC?"UTC":"")+"Month"](t,i),n):n}function ns(n){return n!=null?(go(this,n),r.updateOffset(this,!0),this):su(this,"Month")}function pl(){return ne(this.year(),this.month())}function wl(n){return this._monthsParseExact?(pt(this,"_monthsRegex")||rs.call(this),n?this._monthsShortStrictRegex:this._monthsShortRegex):this._monthsShortStrictRegex&&n?this._monthsShortStrictRegex:this._monthsShortRegex}function bl(n){return this._monthsParseExact?(pt(this,"_monthsRegex")||rs.call(this),n?this._monthsStrictRegex:this._monthsRegex):this._monthsStrictRegex&&n?this._monthsStrictRegex:this._monthsRegex}function rs(){function f(n,t){return t.length-n.length}for(var i=[],r=[],t=[],u,n=0;n<12;n++)u=ki([2e3,n]),i.push(this.monthsShort(u,"")),r.push(this.months(u,"")),t.push(this.months(u,"")),t.push(this.monthsShort(u,""));for(i.sort(f),r.sort(f),t.sort(f),n=0;n<12;n++)i[n]=pu(i[n]),r[n]=pu(r[n]),t[n]=pu(t[n]);this._monthsRegex=new RegExp("^("+t.join("|")+")","i");this._monthsShortRegex=this._monthsRegex;this._monthsStrictRegex=new RegExp("^("+r.join("|")+")$","i");this._monthsShortStrictRegex=new RegExp("^("+i.join("|")+")$","i")}function ie(n){var i,t=n._a;return t&&o(n).overflow===-2&&(i=t[kt]<0||t[kt]>11?kt:t[st]<1||t[st]>ne(t[k],t[kt])?st:t[a]<0||t[a]>24||t[a]===24&&(t[d]!==0||t[dt]!==0||t[ci]!==0)?a:t[d]<0||t[d]>59?d:t[dt]<0||t[dt]>59?dt:t[ci]<0||t[ci]>999?ci:-1,o(n)._overflowDayOfYear&&(i<k||i>st)&&(i=st),o(n)._overflowWeeks&&i===-1&&(i=cl),o(n)._overflowWeekday&&i===-1&&(i=ll),o(n).overflow=i),n}function us(n){r.suppressDeprecationWarnings===!1&&typeof console!="undefined"&&console.warn&&console.warn("Deprecation warning: "+n)}function g(n,t){var i=!0;return fu(function(){return i&&(us(n+"\nArguments: "+Array.prototype.slice.call(arguments).join(", ")+"\n"+(new Error).stack),i=!1),t.apply(this,arguments)},t)}function kl(n,t){re[n]||(us(t),re[n]=!0)}function fs(n){var t,r,e=n._i,i=dl.exec(e)||gl.exec(e),s,f,u,h;if(i){for(o(n).iso=!0,t=0,r=wu.length;t<r;t++)if(wu[t][1].exec(i[1])){f=wu[t][0];s=wu[t][2]!==!1;break}if(f==null){n._isValid=!1;return}if(i[3]){for(t=0,r=ue.length;t<r;t++)if(ue[t][1].exec(i[3])){u=(i[2]||" ")+ue[t][0];break}if(u==null){n._isValid=!1;return}}if(!s&&u!=null){n._isValid=!1;return}if(i[4])if(na.exec(i[4]))h="Z";else{n._isValid=!1;return}n._f=f+(u||"")+(h||"");oe(n)}else n._isValid=!1}function ia(n){var t=ta.exec(n._i);if(t!==null){n._d=new Date(+t[1]);return}fs(n);n._isValid===!1&&(delete n._isValid,r.createFromInputFallback(n))}function ra(n,t,i,r,u,f,e){var o=new Date(n,t,i,r,u,f,e);return n<100&&n>=0&&isFinite(o.getFullYear())&&o.setFullYear(n),o}function bu(n){var t=new Date(Date.UTC.apply(null,arguments));return n<100&&n>=0&&isFinite(t.getUTCFullYear())&&t.setUTCFullYear(n),t}function ar(n){return es(n)?366:365}function es(n){return n%4==0&&n%100!=0||n%400==0}function ua(){return es(this.year())}function ku(n,t,i){var r=7+t-i,u=(7+bu(n,0,r).getUTCDay()-t)%7;return-u+r-1}function os(n,t,i,r,u){var s=(7+i-r)%7,h=ku(n,r,u),f=1+7*(t-1)+s+h,e,o;return f<=0?(e=n-1,o=ar(e)+f):f>ar(n)?(e=n+1,o=f-ar(n)):(e=n,o=f),{year:e,dayOfYear:o}}function vr(n,t,i){var e=ku(n.year(),t,i),r=Math.floor((n.dayOfYear()-e-1)/7)+1,f,u;return r<1?(u=n.year()-1,f=r+li(u,t,i)):r>li(n.year(),t,i)?(f=r-li(n.year(),t,i),u=n.year()+1):(u=n.year(),f=r),{week:f,year:u}}function li(n,t,i){var r=ku(n,t,i),u=ku(n+1,t,i);return(ar(n)-r+u)/7}function nr(n,t,i){return n!=null?n:t!=null?t:i}function fa(n){var t=new Date(r.now());return n._useUTC?[t.getUTCFullYear(),t.getUTCMonth(),t.getUTCDate()]:[t.getFullYear(),t.getMonth(),t.getDate()]}function ee(n){var t,i,r=[],u,f;if(!n._d){for(u=fa(n),n._w&&n._a[st]==null&&n._a[kt]==null&&ea(n),n._dayOfYear&&(f=nr(n._a[k],u[k]),n._dayOfYear>ar(f)&&(o(n)._overflowDayOfYear=!0),i=bu(f,0,n._dayOfYear),n._a[kt]=i.getUTCMonth(),n._a[st]=i.getUTCDate()),t=0;t<3&&n._a[t]==null;++t)n._a[t]=r[t]=u[t];for(;t<7;t++)n._a[t]=r[t]=n._a[t]==null?t===2?1:0:n._a[t];n._a[a]===24&&n._a[d]===0&&n._a[dt]===0&&n._a[ci]===0&&(n._nextDay=!0,n._a[a]=0);n._d=(n._useUTC?bu:ra).apply(null,r);n._tzm!=null&&n._d.setUTCMinutes(n._d.getUTCMinutes()-n._tzm);n._nextDay&&(n._a[a]=24)}}function ea(n){var t,e,u,i,r,f,h,s;t=n._w;t.GG!=null||t.W!=null||t.E!=null?(r=1,f=4,e=nr(t.GG,n._a[k],vr(c(),1,4).year),u=nr(t.W,1),i=nr(t.E,1),(i<1||i>7)&&(s=!0)):(r=n._locale._week.dow,f=n._locale._week.doy,e=nr(t.gg,n._a[k],vr(c(),r,f).year),u=nr(t.w,1),t.d!=null?(i=t.d,(i<0||i>6)&&(s=!0)):t.e!=null?(i=t.e+r,(t.e<0||t.e>6)&&(s=!0)):i=r);u<1||u>li(e,r,f)?o(n)._overflowWeeks=!0:s!=null?o(n)._overflowWeekday=!0:(h=os(e,u,i,r,f),n._a[k]=h.year,n._dayOfYear=h.dayOfYear)}function oe(n){if(n._f===r.ISO_8601){fs(n);return}n._a=[];o(n).empty=!0;for(var t=""+n._i,i,u,s,c=t.length,h=0,e=ao(n._f,n._locale).match(lo)||[],f=0;f<e.length;f++)u=e[f],i=(t.match(ol(u,n))||[])[0],i&&(s=t.substr(0,t.indexOf(i)),s.length>0&&o(n).unusedInput.push(s),t=t.slice(t.indexOf(i)+i.length),h+=i.length),gi[u]?(i?o(n).empty=!1:o(n).unusedTokens.push(u),hl(u,i,n)):n._strict&&!i&&o(n).unusedTokens.push(u);o(n).charsLeftOver=c-h;t.length>0&&o(n).unusedInput.push(t);o(n).bigHour===!0&&n._a[a]<=12&&n._a[a]>0&&(o(n).bigHour=undefined);n._a[a]=oa(n._locale,n._a[a],n._meridiem);ee(n);ie(n)}function oa(n,t,i){var r;return i==null?t:n.meridiemHour!=null?n.meridiemHour(t,i):n.isPM!=null?(r=n.isPM(i),r&&t<12&&(t+=12),r||t!==12||(t=0),t):t}function sa(n){var t,f,u,r,i;if(n._f.length===0){o(n).invalidFormat=!0;n._d=new Date(NaN);return}for(r=0;r<n._f.length;r++)(i=0,t=vf({},n),n._useUTC!=null&&(t._useUTC=n._useUTC),t._f=n._f[r],oe(t),lf(t))&&(i+=o(t).charsLeftOver,i+=o(t).unusedTokens.length*10,o(t).score=i,(u==null||i<u)&&(u=i,f=t));fu(n,f||t)}function ha(n){if(!n._d){var t=so(n._i);n._a=io([t.year,t.month,t.day||t.date,t.hour,t.minute,t.second,t.millisecond],function(n){return n&&parseInt(n,10)});ee(n)}}function ca(n){var t=new or(ie(ss(n)));return t._nextDay&&(t.add(1,"d"),t._nextDay=undefined),t}function ss(n){var t=n._i,i=n._f;return(n._locale=n._locale||hi(n._l),t===null||i===undefined&&t==="")?eu({nullInput:!0}):(typeof t=="string"&&(n._i=t=n._locale.preparse(t)),ni(t))?new or(ie(t)):(oi(i)?sa(n):i?oe(n):uu(t)?n._d=t:la(n),lf(n)||(n._d=null),n)}function la(n){var t=n._i;t===undefined?n._d=new Date(r.now()):uu(t)?n._d=new Date(+t):typeof t=="string"?ia(n):oi(t)?(n._a=io(t.slice(0),function(n){return parseInt(n,10)}),ee(n)):typeof t=="object"?ha(n):typeof t=="number"?n._d=new Date(t):r.createFromInputFallback(n)}function hs(n,t,i,r,u){var f={};return typeof i=="boolean"&&(r=i,i=undefined),f._isAMomentObject=!0,f._useUTC=f._isUTC=u,f._l=i,f._i=n,f._f=t,f._strict=r,ca(f)}function c(n,t,i,r){return hs(n,t,i,r,!1)}function as(n,t){var r,i;if(t.length===1&&oi(t[0])&&(t=t[0]),!t.length)return c();for(r=t[0],i=1;i<t.length;++i)(!t[i].isValid()||t[i][n](r))&&(r=t[i]);return r}function aa(){var n=[].slice.call(arguments,0);return as("isBefore",n)}function va(){var n=[].slice.call(arguments,0);return as("isAfter",n)}function du(n){var t=so(n),i=t.year||0,r=t.quarter||0,u=t.month||0,f=t.week||0,e=t.day||0,o=t.hour||0,s=t.minute||0,h=t.second||0,c=t.millisecond||0;this._milliseconds=+c+h*1e3+s*6e4+o*36e5;this._days=+e+f*7;this._months=+u+r*3+i*12;this._data={};this._locale=hi();this._bubble()}function se(n){return n instanceof du}function ys(n,t){u(n,0,0,function(){var n=this.utcOffset(),i="+";return n<0&&(n=-n,i="-"),i+bt(~~(n/60),2)+t+bt(~~n%60,2)})}function he(n,t){var r=(t||"").match(n)||[],f=r[r.length-1]||[],i=(f+"").match(ps)||["-",0,0],u=+(i[1]*60)+e(i[2]);return i[0]==="+"?u:-u}function ce(n,t){var i,u;return t._isUTC?(i=t.clone(),u=(ni(n)||uu(n)?+n:+c(n))-+i,i._d.setTime(+i._d+u),r.updateOffset(i,!1),i):c(n).local()}function le(n){return-Math.round(n._d.getTimezoneOffset()/15)*15}function ya(n,t){var i=this._offset||0,u;return this.isValid()?n!=null?(typeof n=="string"?n=he(yu,n):Math.abs(n)<16&&(n=n*60),!this._isUTC&&t&&(u=le(this)),this._offset=n,this._isUTC=!0,u!=null&&this.add(u,"m"),i!==n&&(!t||this._changeInProgress?nh(this,ti(n-i,"m"),1,!1):this._changeInProgress||(this._changeInProgress=!0,r.updateOffset(this,!0),this._changeInProgress=null)),this):this._isUTC?i:le(this):n!=null?this:NaN}function pa(n,t){return n!=null?(typeof n!="string"&&(n=-n),this.utcOffset(n,t),this):-this.utcOffset()}function wa(n){return this.utcOffset(0,n)}function ba(n){return this._isUTC&&(this.utcOffset(0,n),this._isUTC=!1,n&&this.subtract(le(this),"m")),this}function ka(){return this._tzm?this.utcOffset(this._tzm):typeof this._i=="string"&&this.utcOffset(he(el,this._i)),this}function da(n){return this.isValid()?(n=n?c(n).utcOffset():0,(this.utcOffset()-n)%60==0):!1}function ga(){return this.utcOffset()>this.clone().month(0).utcOffset()||this.utcOffset()>this.clone().month(5).utcOffset()}function nv(){var n,t;return y(this._isDSTShifted)?(n={},vf(n,this),n=ss(n),n._a?(t=n._isUTC?ki(n._a):c(n._a),this._isDSTShifted=this.isValid()&&ro(n._a,t.toArray())>0):this._isDSTShifted=!1,this._isDSTShifted):this._isDSTShifted}function tv(){return this.isValid()?!this._isUTC:!1}function iv(){return this.isValid()?this._isUTC:!1}function ws(){return this.isValid()?this._isUTC&&this._offset===0:!1}function ti(n,t){var i=n,r=null,u,f,o;return se(n)?i={ms:n._milliseconds,d:n._days,M:n._months}:typeof n=="number"?(i={},t?i[t]=n:i.milliseconds=n):(r=bs.exec(n))?(u=r[1]==="-"?-1:1,i={y:0,d:e(r[st])*u,h:e(r[a])*u,m:e(r[d])*u,s:e(r[dt])*u,ms:e(r[ci])*u}):(r=ks.exec(n))?(u=r[1]==="-"?-1:1,i={y:ai(r[2],u),M:ai(r[3],u),d:ai(r[4],u),h:ai(r[5],u),m:ai(r[6],u),s:ai(r[7],u),w:ai(r[8],u)}):i==null?i={}:typeof i=="object"&&("from"in i||"to"in i)&&(o=rv(c(i.from),c(i.to)),i={},i.ms=o.milliseconds,i.M=o.months),f=new du(i),se(n)&&pt(n,"_locale")&&(f._locale=n._locale),f}function ai(n,t){var i=n&&parseFloat(n.replace(",","."));return(isNaN(i)?0:i)*t}function ds(n,t){var i={milliseconds:0,months:0};return i.months=t.month()-n.month()+(t.year()-n.year())*12,n.clone().add(i.months,"M").isAfter(t)&&--i.months,i.milliseconds=+t-+n.clone().add(i.months,"M"),i}function rv(n,t){var i;return(n.isValid()&&t.isValid())?(t=ce(t,n),n.isBefore(t)?i=ds(n,t):(i=ds(t,n),i.milliseconds=-i.milliseconds,i.months=-i.months),i):{milliseconds:0,months:0}}function gs(n,t){return function(i,r){var u,f;return r===null||isNaN(+r)||(kl(t,"moment()."+t+"(period, number) is deprecated. Please use moment()."+t+"(number, period)."),f=i,i=r,r=f),i=typeof i=="string"?+i:i,u=ti(i,r),nh(this,u,n),this}}function nh(n,t,i,u){var o=t._milliseconds,f=t._days,e=t._months;n.isValid()&&(u=u==null?!0:u,o&&n._d.setTime(+n._d+o*i),f&&ho(n,"Date",su(n,"Date")+f*i),e&&go(n,su(n,"Month")+e*i),u&&r.updateOffset(n,f||e))}function uv(n,t){var u=n||c(),f=ce(u,this).startOf("day"),i=this.diff(f,"days",!0),r=i<-6?"sameElse":i<-1?"lastWeek":i<0?"lastDay":i<1?"sameDay":i<2?"nextDay":i<7?"nextWeek":"sameElse",e=t&&(wt(t[r])?t[r]():t[r]);return this.format(e||this.localeData().calendar(r,this,c(u)))}function fv(){return new or(this)}function ev(n,t){var i=ni(n)?n:c(n);return(this.isValid()&&i.isValid())?(t=b(y(t)?"millisecond":t),t==="millisecond"?+this>+i:+i<+this.clone().startOf(t)):!1}function ov(n,t){var i=ni(n)?n:c(n);return(this.isValid()&&i.isValid())?(t=b(y(t)?"millisecond":t),t==="millisecond"?+this<+i:+this.clone().endOf(t)<+i):!1}function sv(n,t,i){return this.isAfter(n,i)&&this.isBefore(t,i)}function hv(n,t){var i=ni(n)?n:c(n),r;return(this.isValid()&&i.isValid())?(t=b(t||"millisecond"),t==="millisecond"?+this==+i:(r=+i,+this.clone().startOf(t)<=r&&r<=+this.clone().endOf(t))):!1}function cv(n,t){return this.isSame(n,t)||this.isAfter(n,t)}function lv(n,t){return this.isSame(n,t)||this.isBefore(n,t)}function av(n,t,i){var f,e,u,r;return this.isValid()?(f=ce(n,this),!f.isValid())?NaN:(e=(f.utcOffset()-this.utcOffset())*6e4,t=b(t),t==="year"||t==="month"||t==="quarter"?(r=vv(this,f),t==="quarter"?r=r/3:t==="year"&&(r=r/12)):(u=this-f,r=t==="second"?u/1e3:t==="minute"?u/6e4:t==="hour"?u/36e5:t==="day"?(u-e)/864e5:t==="week"?(u-e)/6048e5:u),i?r:p(r)):NaN}function vv(n,t){var r=(t.year()-n.year())*12+(t.month()-n.month()),i=n.clone().add(r,"months"),u,f;return t-i<0?(u=n.clone().add(r-1,"months"),f=(t-i)/(i-u)):(u=n.clone().add(r+1,"months"),f=(t-i)/(u-i)),-(r+f)}function yv(){return this.clone().locale("en").format("ddd MMM DD YYYY HH:mm:ss [GMT]ZZ")}function pv(){var n=this.clone().utc();return 0<n.year()&&n.year()<=9999?wt(Date.prototype.toISOString)?this.toDate().toISOString():wf(n,"YYYY-MM-DD[T]HH:mm:ss.SSS[Z]"):wf(n,"YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]")}function wv(n){var t=wf(this,n||r.defaultFormat);return this.localeData().postformat(t)}function bv(n,t){return this.isValid()&&(ni(n)&&n.isValid()||c(n).isValid())?ti({to:this,from:n}).locale(this.locale()).humanize(!t):this.localeData().invalidDate()}function kv(n){return this.from(c(),n)}function dv(n,t){return this.isValid()&&(ni(n)&&n.isValid()||c(n).isValid())?ti({from:this,to:n}).locale(this.locale()).humanize(!t):this.localeData().invalidDate()}function gv(n){return this.to(c(),n)}function rh(n){var t;return n===undefined?this._locale._abbr:(t=hi(n),t!=null&&(this._locale=t),this)}function uh(){return this._locale}function ny(n){n=b(n);switch(n){case"year":this.month(0);case"quarter":case"month":this.date(1);case"week":case"isoWeek":case"day":this.hours(0);case"hour":this.minutes(0);case"minute":this.seconds(0);case"second":this.milliseconds(0)}return n==="week"&&this.weekday(0),n==="isoWeek"&&this.isoWeekday(1),n==="quarter"&&this.month(Math.floor(this.month()/3)*3),this}function ty(n){return(n=b(n),n===undefined||n==="millisecond")?this:this.startOf(n).add(1,n==="isoWeek"?"week":n).subtract(1,"ms")}function iy(){return+this._d-(this._offset||0)*6e4}function ry(){return Math.floor(+this/1e3)}function uy(){return this._offset?new Date(+this):this._d}function fy(){var n=this;return[n.year(),n.month(),n.date(),n.hour(),n.minute(),n.second(),n.millisecond()]}function ey(){var n=this;return{years:n.year(),months:n.month(),date:n.date(),hours:n.hours(),minutes:n.minutes(),seconds:n.seconds(),milliseconds:n.milliseconds()}}function oy(){return this.isValid()?this.toISOString():"null"}function sy(){return lf(this)}function hy(){return fu({},o(this))}function cy(){return o(this).overflow}function ly(){return{input:this._i,format:this._f,locale:this._locale,isUTC:this._isUTC,strict:this._strict}}function gu(n,t){u(0,[n,n.length],0,t)}function ay(n){return fh.call(this,n,this.week(),this.weekday(),this.localeData()._week.dow,this.localeData()._week.doy)}function vy(n){return fh.call(this,n,this.isoWeek(),this.isoWeekday(),1,4)}function yy(){return li(this.year(),1,4)}function py(){var n=this.localeData()._week;return li(this.year(),n.dow,n.doy)}function fh(n,t,i,r,u){var f;return n==null?vr(this,r,u).year:(f=li(n,r,u),t>f&&(t=f),wy.call(this,n,t,i,r,u))}function wy(n,t,i,r,u){var e=os(n,t,i,r,u),f=bu(e.year,0,e.dayOfYear);return this.year(f.getUTCFullYear()),this.month(f.getUTCMonth()),this.date(f.getUTCDate()),this}function by(n){return n==null?Math.ceil((this.month()+1)/3):this.month((n-1)*3+this.month()%3)}function ky(n){return vr(n,this._week.dow,this._week.doy).week}function dy(){return this._week.dow}function gy(){return this._week.doy}function np(n){var t=this.localeData().week(this);return n==null?t:this.add((n-t)*7,"d")}function tp(n){var t=vr(this,1,4).week;return n==null?t:this.add((n-t)*7,"d")}function ip(n,t){return typeof n!="string"?n:isNaN(n)?(n=t.weekdaysParse(n),typeof n=="number")?n:null:parseInt(n,10)}function rp(n,t){return oi(this._weekdays)?this._weekdays[n.day()]:this._weekdays[this._weekdays.isFormat.test(t)?"format":"standalone"][n.day()]}function up(n){return this._weekdaysShort[n.day()]}function fp(n){return this._weekdaysMin[n.day()]}function ep(n,t,i){var r,u,f;for(this._weekdaysParse||(this._weekdaysParse=[],this._minWeekdaysParse=[],this._shortWeekdaysParse=[],this._fullWeekdaysParse=[]),r=0;r<7;r++)if((u=c([2e3,1]).day(r),i&&!this._fullWeekdaysParse[r]&&(this._fullWeekdaysParse[r]=new RegExp("^"+this.weekdays(u,"").replace(".",".?")+"$","i"),this._shortWeekdaysParse[r]=new RegExp("^"+this.weekdaysShort(u,"").replace(".",".?")+"$","i"),this._minWeekdaysParse[r]=new RegExp("^"+this.weekdaysMin(u,"").replace(".",".?")+"$","i")),this._weekdaysParse[r]||(f="^"+this.weekdays(u,"")+"|^"+this.weekdaysShort(u,"")+"|^"+this.weekdaysMin(u,""),this._weekdaysParse[r]=new RegExp(f.replace(".",""),"i")),i&&t==="dddd"&&this._fullWeekdaysParse[r].test(n))||i&&t==="ddd"&&this._shortWeekdaysParse[r].test(n)||i&&t==="dd"&&this._minWeekdaysParse[r].test(n)||!i&&this._weekdaysParse[r].test(n))return r}function op(n){if(!this.isValid())return n!=null?this:NaN;var t=this._isUTC?this._d.getUTCDay():this._d.getDay();return n!=null?(n=ip(n,this.localeData()),this.add(n-t,"d")):t}function sp(n){if(!this.isValid())return n!=null?this:NaN;var t=(this.day()+7-this.localeData()._week.dow)%7;return n==null?t:this.add(n-t,"d")}function hp(n){return this.isValid()?n==null?this.day()||7:this.day(this.day()%7?n:n-7):n!=null?this:NaN}function cp(n){var t=Math.round((this.clone().startOf("day")-this.clone().startOf("year"))/864e5)+1;return n==null?t:this.add(n-t,"d")}function ye(){return this.hours()%12||12}function ch(n,t){u(n,0,0,function(){return this.localeData().meridiem(this.hours(),this.minutes(),t)})}function lh(n,t){return t._meridiemParse}function lp(n){return(n+"").toLowerCase().charAt(0)==="p"}function ap(n,t,i){return n>11?i?"pm":"PM":i?"am":"AM"}function vp(n,t){t[ci]=e(("0."+n)*1e3)}function yp(){return this._isUTC?"UTC":""}function pp(){return this._isUTC?"Coordinated Universal Time":""}function wp(n){return c(n*1e3)}function bp(){return c.apply(null,arguments).parseZone()}function kp(n,t,i){var r=this._calendar[n];return wt(r)?r.call(t,i):r}function dp(n){var t=this._longDateFormat[n],i=this._longDateFormat[n.toUpperCase()];return t||!i?t:(this._longDateFormat[n]=i.replace(/MMMM|MM|DD|dddd/g,function(n){return n.slice(1)}),this._longDateFormat[n])}function gp(){return this._invalidDate}function nw(n){return this._ordinal.replace("%d",n)}function tc(n){return n}function tw(n,t,i,r){var u=this._relativeTime[i];return wt(u)?u(n,t,i,r):u.replace(/%d/i,n)}function iw(n,t){var i=this._relativeTime[n>0?"future":"past"];return wt(i)?i(t):i.replace(/%s/i,t)}function rw(n){var t;for(var i in n)t=n[i],wt(t)?this[i]=t:this["_"+i]=t;this._ordinalParseLenient=new RegExp(this._ordinalParse.source+"|"+/\d{1,2}/.source)}function rc(n,t,i,r){var u=hi(),f=ki().set(r,t);return u[i](f,n)}function yr(n,t,i,r,u){if(typeof n=="number"&&(t=n,n=undefined),n=n||"",t!=null)return rc(n,t,i,u);for(var e=[],f=0;f<r;f++)e[f]=rc(n,f,i,u);return e}function uw(n,t){return yr(n,t,"months",12,"month")}function fw(n,t){return yr(n,t,"monthsShort",12,"month")}function ew(n,t){return yr(n,t,"weekdays",7,"day")}function ow(n,t){return yr(n,t,"weekdaysShort",7,"day")}function sw(n,t){return yr(n,t,"weekdaysMin",7,"day")}function hw(){var n=this._data;return this._milliseconds=ht(this._milliseconds),this._days=ht(this._days),this._months=ht(this._months),n.milliseconds=ht(n.milliseconds),n.seconds=ht(n.seconds),n.minutes=ht(n.minutes),n.hours=ht(n.hours),n.months=ht(n.months),n.years=ht(n.years),this}function uc(n,t,i,r){var u=ti(t,i);return n._milliseconds+=r*u._milliseconds,n._days+=r*u._days,n._months+=r*u._months,n._bubble()}function cw(n,t){return uc(this,n,t,1)}function lw(n,t){return uc(this,n,t,-1)}function fc(n){return n<0?Math.floor(n):Math.ceil(n)}function aw(){var r=this._milliseconds,n=this._days,t=this._months,i=this._data,u,f,e,s,o;return r>=0&&n>=0&&t>=0||r<=0&&n<=0&&t<=0||(r+=fc(we(t)+n)*864e5,n=0,t=0),i.milliseconds=r%1e3,u=p(r/1e3),i.seconds=u%60,f=p(u/60),i.minutes=f%60,e=p(f/60),i.hours=e%24,n+=p(e/24),o=p(ec(n)),t+=o,n-=fc(we(o)),s=p(t/12),t%=12,i.days=n,i.months=t,i.years=s,this}function ec(n){return n*4800/146097}function we(n){return n*146097/4800}function vw(n){var t,r,i=this._milliseconds;if(n=b(n),n==="month"||n==="year")return t=this._days+i/864e5,r=this._months+ec(t),n==="month"?r:r/12;t=this._days+Math.round(we(this._months));switch(n){case"week":return t/7+i/6048e5;case"day":return t+i/864e5;case"hour":return t*24+i/36e5;case"minute":return t*1440+i/6e4;case"second":return t*86400+i/1e3;case"millisecond":return Math.floor(t*864e5)+i;default:throw new Error("Unknown unit "+n);}}function yw(){return this._milliseconds+this._days*864e5+this._months%12*2592e6+e(this._months/12)*31536e6}function ri(n){return function(){return this.as(n)}}function ib(n){return n=b(n),this[n+"s"]()}function vi(n){return function(){return this._data[n]}}function cb(){return p(this.days()/7)}function lb(n,t,i,r,u){return u.relativeTime(t||1,!!i,n,r)}function ab(n,t,i){var r=ti(n).abs(),h=yi(r.as("s")),f=yi(r.as("m")),e=yi(r.as("h")),o=yi(r.as("d")),s=yi(r.as("M")),c=yi(r.as("y")),u=h<gt.s&&["s",h]||f<=1&&["m"]||f<gt.m&&["mm",f]||e<=1&&["h"]||e<gt.h&&["hh",e]||o<=1&&["d"]||o<gt.d&&["dd",o]||s<=1&&["M"]||s<gt.M&&["MM",s]||c<=1&&["y"]||["yy",c];return u[2]=t,u[3]=+n>0,u[4]=i,lb.apply(null,u)}function vb(n,t){return gt[n]===undefined?!1:t===undefined?gt[n]:(gt[n]=t,!0)}function yb(n){var t=this.localeData(),i=ab(this,!n,t);return n&&(i=t.pastFuture(+this,i)),t.postformat(i)}function tf(){var t=nf(this._milliseconds)/1e3,a=nf(this._days),i=nf(this._months),n,e,o;n=p(t/60);e=p(n/60);t%=60;n%=60;o=p(i/12);i%=12;var s=o,h=i,c=a,r=e,u=n,f=t,l=this.asSeconds();return l?(l<0?"-":"")+"P"+(s?s+"Y":"")+(h?h+"M":"")+(c?c+"D":"")+(r||u||f?"T":"")+(r?r+"H":"")+(u?u+"M":"")+(f?f+"S":""):"P0D"}function rk(n,t){var i=n.split("_");return t%10==1&&t%100!=11?i[0]:t%10>=2&&t%10<=4&&(t%100<10||t%100>=20)?i[1]:i[2]}function pi(n,t,i){var r={mm:t?"хвіліна_хвіліны_хвілін":"хвіліну_хвіліны_хвілін",hh:t?"гадзіна_гадзіны_гадзін":"гадзіну_гадзіны_гадзін",dd:"дзень_дні_дзён",MM:"месяц_месяцы_месяцаў",yy:"год_гады_гадоў"};return i==="m"?t?"хвіліна":"хвіліну":i==="h"?t?"гадзіна":"гадзіну":n+" "+rk(r[i],+n)}function be(n,t,i){return n+" "+lk({mm:"munutenn",MM:"miz",dd:"devezh"}[i],n)}function ck(n){switch(hc(n)){case 1:case 3:case 4:case 5:case 9:return n+" bloaz";default:return n+" vloaz"}}function hc(n){return n>9?hc(n%10):n}function lk(n,t){return t===2?ak(n):n}function ak(n){var t={m:"v",b:"v",d:"z"};return t[n.charAt(0)]===undefined?n:t[n.charAt(0)]+n.substring(1)}function wi(n,t,i){var r=n+" ";switch(i){case"m":return t?"jedna minuta":"jedne minute";case"mm":return r+(n===1?"minuta":n===2||n===3||n===4?"minute":"minuta");case"h":return t?"jedan sat":"jednog sata";case"hh":return r+(n===1?"sat":n===2||n===3||n===4?"sata":"sati");case"dd":return r+(n===1?"dan":"dana");case"MM":return r+(n===1?"mjesec":n===2||n===3||n===4?"mjeseca":"mjeseci");case"yy":return r+(n===1?"godina":n===2||n===3||n===4?"godine":"godina")}}function pr(n){return n>1&&n<5&&~~(n/10)!=1}function tt(n,t,i,r){var u=n+" ";switch(i){case"s":return t||r?"pár sekund":"pár sekundami";case"m":return t?"minuta":r?"minutu":"minutou";case"mm":return t||r?u+(pr(n)?"minuty":"minut"):u+"minutami";case"h":return t?"hodina":r?"hodinu":"hodinou";case"hh":return t||r?u+(pr(n)?"hodiny":"hodin"):u+"hodinami";case"d":return t||r?"den":"dnem";case"dd":return t||r?u+(pr(n)?"dny":"dní"):u+"dny";case"M":return t||r?"měsíc":"měsícem";case"MM":return t||r?u+(pr(n)?"měsíce":"měsíců"):u+"měsíci";case"y":return t||r?"rok":"rokem";case"yy":return t||r?u+(pr(n)?"roky":"let"):u+"lety"}}function ui(n,t,i){var r={m:["eine Minute","einer Minute"],h:["eine Stunde","einer Stunde"],d:["ein Tag","einem Tag"],dd:[n+" Tage",n+" Tagen"],M:["ein Monat","einem Monat"],MM:[n+" Monate",n+" Monaten"],y:["ein Jahr","einem Jahr"],yy:[n+" Jahre",n+" Jahren"]};return t?r[i][0]:r[i][1]}function fi(n,t,i){var r={m:["eine Minute","einer Minute"],h:["eine Stunde","einer Stunde"],d:["ein Tag","einem Tag"],dd:[n+" Tage",n+" Tagen"],M:["ein Monat","einem Monat"],MM:[n+" Monate",n+" Monaten"],y:["ein Jahr","einem Jahr"],yy:[n+" Jahre",n+" Jahren"]};return t?r[i][0]:r[i][1]}function ct(n,t,i,r){var u={s:["mõne sekundi","mõni sekund","paar sekundit"],m:["ühe minuti","üks minut"],mm:[n+" minuti",n+" minutit"],h:["ühe tunni","tund aega","üks tund"],hh:[n+" tunni",n+" tundi"],d:["ühe päeva","üks päev"],M:["kuu aja","kuu aega","üks kuu"],MM:[n+" kuu",n+" kuud"],y:["ühe aasta","aasta","üks aasta"],yy:[n+" aasta",n+" aastat"]};return t?u[i][2]?u[i][2]:u[i][1]:r?u[i][0]:u[i][1]}function it(n,t,i,r){var u="";switch(i){case"s":return r?"muutaman sekunnin":"muutama sekunti";case"m":return r?"minuutin":"minuutti";case"mm":u=r?"minuutin":"minuuttia";break;case"h":return r?"tunnin":"tunti";case"hh":u=r?"tunnin":"tuntia";break;case"d":return r?"päivän":"päivä";case"dd":u=r?"päivän":"päivää";break;case"M":return r?"kuukauden":"kuukausi";case"MM":u=r?"kuukauden":"kuukautta";break;case"y":return r?"vuoden":"vuosi";case"yy":u=r?"vuoden":"vuotta"}return yd(n,r)+" "+u}function yd(n,t){return n<10?t?ac[n]:wr[n]:n}function bi(n,t,i){var r=n+" ";switch(i){case"m":return t?"jedna minuta":"jedne minute";case"mm":return r+(n===1?"minuta":n===2||n===3||n===4?"minute":"minuta");case"h":return t?"jedan sat":"jednog sata";case"hh":return r+(n===1?"sat":n===2||n===3||n===4?"sata":"sati");case"dd":return r+(n===1?"dan":"dana");case"MM":return r+(n===1?"mjesec":n===2||n===3||n===4?"mjeseca":"mjeseci");case"yy":return r+(n===1?"godina":n===2||n===3||n===4?"godine":"godina")}}function rt(n,t,i,r){var u=n;switch(i){case"s":return r||t?"néhány másodperc":"néhány másodperce";case"m":return"egy"+(r||t?" perc":" perce");case"mm":return u+(r||t?" perc":" perce");case"h":return"egy"+(r||t?" óra":" órája");case"hh":return u+(r||t?" óra":" órája");case"d":return"egy"+(r||t?" nap":" napja");case"dd":return u+(r||t?" nap":" napja");case"M":return"egy"+(r||t?" hónap":" hónapja");case"MM":return u+(r||t?" hónap":" hónapja");case"y":return"egy"+(r||t?" év":" éve");case"yy":return u+(r||t?" év":" éve")}return""}function yc(n){return(n?"":"[múlt] ")+"["+vc[this.day()]+"] LT[-kor]"}function br(n){return n%100==11?!0:n%10==1?!1:!0}function lt(n,t,i,r){var u=n+" ";switch(i){case"s":return t||r?"nokkrar sekúndur":"nokkrum sekúndum";case"m":return t?"mínúta":"mínútu";case"mm":return br(n)?u+(t||r?"mínútur":"mínútum"):t?u+"mínúta":u+"mínútu";case"hh":return br(n)?u+(t||r?"klukkustundir":"klukkustundum"):u+"klukkustund";case"d":return t?"dagur":r?"dag":"degi";case"dd":return br(n)?t?u+"dagar":u+(r?"daga":"dögum"):t?u+"dagur":u+(r?"dag":"degi");case"M":return t?"mánuður":r?"mánuð":"mánuði";case"MM":return br(n)?t?u+"mánuðir":u+(r?"mánuði":"mánuðum"):t?u+"mánuður":u+(r?"mánuð":"mánuði");case"y":return t||r?"ár":"ári";case"yy":return br(n)?u+(t||r?"ár":"árum"):u+(t||r?"ár":"ári")}}function kr(n,t,i){var r={m:["eng Minutt","enger Minutt"],h:["eng Stonn","enger Stonn"],d:["een Dag","engem Dag"],M:["ee Mount","engem Mount"],y:["ee Joer","engem Joer"]};return t?r[i][0]:r[i][1]}function bg(n){var t=n.substr(0,n.indexOf(" "));return tr(t)?"a "+n:"an "+n}function kg(n){var t=n.substr(0,n.indexOf(" "));return tr(t)?"viru "+n:"virun "+n}function tr(n){if(n=parseInt(n,10),isNaN(n))return!1;if(n<0)return!0;if(n<10)return 4<=n&&n<=7?!0:!1;if(n<100){var t=n%10,i=n/10;return t===0?tr(i):tr(t)}if(n<1e4){while(n>=10)n=n/10;return tr(n)}return n=n/1e3,tr(n)}function nn(n,t,i,r){return t?"kelios sekundės":r?"kelių sekundžių":"kelias sekundes"}function ir(n,t,i,r){return t?ei(i)[0]:r?ei(i)[1]:ei(i)[2]}function wc(n){return n%10==0||n>10&&n<20}function ei(n){return pc[n].split("_")}function dr(n,t,i,r){var u=n+" ";return n===1?u+ir(n,t,i[0],r):t?u+(wc(n)?ei(i)[1]:ei(i)[0]):r?u+ei(i)[1]:u+(wc(n)?ei(i)[1]:ei(i)[2])}function bc(n,t,i){return i?t%10==1&&t!==11?n[2]:n[3]:t%10==1&&t!==11?n[0]:n[1]}function gr(n,t,i){return n+" "+bc(ke[i],n,t)}function nu(n,t,i){return bc(ke[i],n,t)}function rn(n,t){return t?"dažas sekundes":"dažām sekundēm"}function ut(n,t,i){var r="";if(t)switch(i){case"s":r="काही सेकंद";break;case"m":r="एक मिनिट";break;case"mm":r="%d मिनिटे";break;case"h":r="एक तास";break;case"hh":r="%d तास";break;case"d":r="एक दिवस";break;case"dd":r="%d दिवस";break;case"M":r="एक महिना";break;case"MM":r="%d महिने";break;case"y":r="एक वर्ष";break;case"yy":r="%d वर्षे"}else switch(i){case"s":r="काही सेकंदां";break;case"m":r="एका मिनिटा";break;case"mm":r="%d मिनिटां";break;case"h":r="एका तासा";break;case"hh":r="%d तासां";break;case"d":r="एका दिवसा";break;case"dd":r="%d दिवसां";break;case"M":r="एका महिन्या";break;case"MM":r="%d महिन्यां";break;case"y":r="एका वर्षा";break;case"yy":r="%d वर्षां"}return r.replace(/%d/i,n)}function of(n){return n%10<5&&n%10>1&&~~(n/10)%10!=1}function rr(n,t,i){var r=n+" ";switch(i){case"m":return t?"minuta":"minutę";case"mm":return r+(of(n)?"minuty":"minut");case"h":return t?"godzina":"godzinę";case"hh":return r+(of(n)?"godziny":"godzin");case"MM":return r+(of(n)?"miesiące":"miesięcy");case"yy":return r+(of(n)?"lata":"lat")}}function tu(n,t,i){var r=" ";return(n%100>=20||n>=100&&n%100==0)&&(r=" de "),n+r+{mm:"minute",hh:"ore",dd:"zile",MM:"luni",yy:"ani"}[i]}function itt(n,t){var i=n.split("_");return t%10==1&&t%100!=11?i[0]:t%10>=2&&t%10<=4&&(t%100<10||t%100>=20)?i[1]:i[2]}function ur(n,t,i){var r={mm:t?"минута_минуты_минут":"минуту_минуты_минут",hh:"час_часа_часов",dd:"день_дня_дней",MM:"месяц_месяца_месяцев",yy:"год_года_лет"};return i==="m"?t?"минута":"минуту":n+" "+itt(r[i],+n)}function iu(n){return n>1&&n<5}function ft(n,t,i,r){var u=n+" ";switch(i){case"s":return t||r?"pár sekúnd":"pár sekundami";case"m":return t?"minúta":r?"minútu":"minútou";case"mm":return t||r?u+(iu(n)?"minúty":"minút"):u+"minútami";case"h":return t?"hodina":r?"hodinu":"hodinou";case"hh":return t||r?u+(iu(n)?"hodiny":"hodín"):u+"hodinami";case"d":return t||r?"deň":"dňom";case"dd":return t||r?u+(iu(n)?"dni":"dní"):u+"dňami";case"M":return t||r?"mesiac":"mesiacom";case"MM":return t||r?u+(iu(n)?"mesiace":"mesiacov"):u+"mesiacmi";case"y":return t||r?"rok":"rokom";case"yy":return t||r?u+(iu(n)?"roky":"rokov"):u+"rokmi"}}function et(n,t,i,r){var u=n+" ";switch(i){case"s":return t||r?"nekaj sekund":"nekaj sekundami";case"m":return t?"ena minuta":"eno minuto";case"mm":return u+(n===1?t?"minuta":"minuto":n===2?t||r?"minuti":"minutama":n<5?t||r?"minute":"minutami":t||r?"minut":"minutami");case"h":return t?"ena ura":"eno uro";case"hh":return u+(n===1?t?"ura":"uro":n===2?t||r?"uri":"urama":n<5?t||r?"ure":"urami":t||r?"ur":"urami");case"d":return t||r?"en dan":"enim dnem";case"dd":return u+(n===1?t||r?"dan":"dnem":n===2?t||r?"dni":"dnevoma":t||r?"dni":"dnevi");case"M":return t||r?"en mesec":"enim mesecem";case"MM":return u+(n===1?t||r?"mesec":"mesecem":n===2?t||r?"meseca":"mesecema":n<5?t||r?"mesece":"meseci":t||r?"mesecev":"meseci");case"y":return t||r?"eno leto":"enim letom";case"yy":return u+(n===1?t||r?"leto":"letom":n===2?t||r?"leti":"letoma":n<5?t||r?"leta":"leti":t||r?"let":"leti")}}function ktt(n){var t=n;return n.indexOf("jaj")!==-1?t.slice(0,-3)+"leS":n.indexOf("jar")!==-1?t.slice(0,-3)+"waQ":n.indexOf("DIS")!==-1?t.slice(0,-3)+"nem":t+" pIq"}function dtt(n){var t=n;return n.indexOf("jaj")!==-1?t.slice(0,-3)+"Hu’":n.indexOf("jar")!==-1?t.slice(0,-3)+"wen":n.indexOf("DIS")!==-1?t.slice(0,-3)+"ben":t+" ret"}function ru(n,t,i){var r=gtt(n);switch(i){case"mm":return r+" tup";case"hh":return r+" rep";case"dd":return r+" jaj";case"MM":return r+" jar";case"yy":return r+" DIS"}}function gtt(n){var i=Math.floor(n%1e3/100),r=Math.floor(n%100/10),u=n%10,t="";return i>0&&(t+=hf[i]+"vatlh"),r>0&&(t+=(t!==""?" ":"")+hf[r]+"maH"),u>0&&(t+=(t!==""?" ":"")+hf[u]),t===""?"pagh":t}function ot(n,t,i,r){var u={s:["viensas secunds","'iensas secunds"],m:["'n míut","'iens míut"],mm:[n+" míuts",""+n+" míuts"],h:["'n þora","'iensa þora"],hh:[n+" þoras",""+n+" þoras"],d:["'n ziua","'iensa ziua"],dd:[n+" ziuas",""+n+" ziuas"],M:["'n mes","'iens mes"],MM:[n+" mesen",""+n+" mesen"],y:["'n ar","'iens ar"],yy:[n+" ars",""+n+" ars"]};return r?u[i][0]:t?u[i][0]:u[i][1]}function fit(n,t){var i=n.split("_");return t%10==1&&t%100!=11?i[0]:t%10>=2&&t%10<=4&&(t%100<10||t%100>=20)?i[1]:i[2]}function fr(n,t,i){var r={mm:t?"хвилина_хвилини_хвилин":"хвилину_хвилини_хвилин",hh:t?"година_години_годин":"годину_години_годин",dd:"день_дні_днів",MM:"місяць_місяці_місяців",yy:"рік_роки_років"};return i==="m"?t?"хвилина":"хвилину":i==="h"?t?"година":"годину":n+" "+fit(r[i],+n)}function eit(n,t){var i={nominative:"неділя_понеділок_вівторок_середа_четвер_п’ятниця_субота".split("_"),accusative:"неділю_понеділок_вівторок_середу_четвер_п’ятницю_суботу".split("_"),genitive:"неділі_понеділка_вівторка_середи_четверга_п’ятниці_суботи".split("_")},r=/(\[[ВвУу]\]) ?dddd/.test(t)?"accusative":/\[?(?:минулої|наступної)? ?\] ?dddd/.test(t)?"genitive":"nominative";return i[r][n.day()]}function er(n){return function(){return n+"о"+(this.hours()===11?"б":"")+"] LT"}}var to,af=r.momentProperties=[],yf=!1,si={},ou,hr={},lo=/(\[[^\[]*\])|(\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|YYYYYY|YYYYY|YYYY|YY|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g,hu=/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g,pf={},gi={},vo=/\d/,w=/\d\d/,yo=/\d{3}/,bf=/\d{4}/,cu=/[+-]?\d{6}/,l=/\d\d?/,po=/\d\d\d\d?/,wo=/\d\d\d\d\d\d?/,lu=/\d{1,3}/,kf=/\d{1,4}/,au=/[+-]?\d{1,6}/,vu=/[+-]?\d+/,el=/Z|[+-]\d\d:?\d\d/gi,yu=/Z|[+-]\d\d(?::?\d\d)?/gi,cr=/[0-9]*['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+|[\u0600-\u06FF\/]+(\s*?[\u0600-\u06FF]+){1,2}/i,df={},gf={},k=0,kt=1,st=2,a=3,d=4,dt=5,ci=6,cl=7,ll=8,te,bo,ko,ts,is,re,fe,cs,ls,vs,ps,bs,ks,th,ih,ae,eh,ve,oh,sh,hh,ah,vh,yh,ph,ii,wh,t,pe,bh,kh,dh,gh,nc,ic,f,ht,yi,gt,nf,s,n,pb,wb,db,rf,ik,uk,fk,vk,yk,pk,uf,ff,wk,bk,kk,dk,gk,nd,td,id,rd,ud,fd,ed,od,cd,ld,wr,ac,pd,wd,bd,kd,dd,tg,ig,fg,vc,eg,og,sg,hg,cg,lg,ag,vg,ef,yg,pg,wg,dg,gg,pc,tn,ke,un,at,fn,en,on,kc,dc,sn,hn,cn,vn,kn,de,ge,dn,gn,ntt,ttt,sf,rtt,utt,ftt,gc,nl,ett,ott,stt,vt,htt,yt,ctt,ltt,att,ptt,wtt,btt,hf,nit,cf,tit,iit,rit,uit,oit,sit,hit,cit,lit,no;u("M",["MM",2],"Mo",function(){return this.month()+1});u("MMM",0,0,function(n){return this.localeData().monthsShort(this,n)});u("MMMM",0,0,function(n){return this.localeData().months(this,n)});v("month","M");i("M",l);i("MM",l,w);i("MMM",function(n,t){return t.monthsShortRegex(n)});i("MMMM",function(n,t){return t.monthsRegex(n)});h(["M","MM"],function(n,t){t[kt]=e(n)-1});h(["MMM","MMMM"],function(n,t,i,r){var u=i._locale.monthsParse(n,r,i._strict);u!=null?t[kt]=u:o(i).invalidMonth=n});te=/D[oD]?(\[[^\[\]]*\]|\s+)+MMMM?/;bo="January_February_March_April_May_June_July_August_September_October_November_December".split("_");ko="Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_");ts=cr;is=cr;re={};r.suppressDeprecationWarnings=!1;var dl=/^\s*((?:[+-]\d{6}|\d{4})-(?:\d\d-\d\d|W\d\d-\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?::\d\d(?::\d\d(?:[.,]\d+)?)?)?)([\+\-]\d\d(?::?\d\d)?|\s*Z)?)?/,gl=/^\s*((?:[+-]\d{6}|\d{4})(?:\d\d\d\d|W\d\d\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?:\d\d(?:\d\d(?:[.,]\d+)?)?)?)([\+\-]\d\d(?::?\d\d)?|\s*Z)?)?/,na=/Z|[+-]\d\d(?::?\d\d)?/,wu=[["YYYYYY-MM-DD",/[+-]\d{6}-\d\d-\d\d/],["YYYY-MM-DD",/\d{4}-\d\d-\d\d/],["GGGG-[W]WW-E",/\d{4}-W\d\d-\d/],["GGGG-[W]WW",/\d{4}-W\d\d/,!1],["YYYY-DDD",/\d{4}-\d{3}/],["YYYY-MM",/\d{4}-\d\d/,!1],["YYYYYYMMDD",/[+-]\d{10}/],["YYYYMMDD",/\d{8}/],["GGGG[W]WWE",/\d{4}W\d{3}/],["GGGG[W]WW",/\d{4}W\d{2}/,!1],["YYYYDDD",/\d{7}/]],ue=[["HH:mm:ss.SSSS",/\d\d:\d\d:\d\d\.\d+/],["HH:mm:ss,SSSS",/\d\d:\d\d:\d\d,\d+/],["HH:mm:ss",/\d\d:\d\d:\d\d/],["HH:mm",/\d\d:\d\d/],["HHmmss.SSSS",/\d\d\d\d\d\d\.\d+/],["HHmmss,SSSS",/\d\d\d\d\d\d,\d+/],["HHmmss",/\d\d\d\d\d\d/],["HHmm",/\d\d\d\d/],["HH",/\d\d/]],ta=/^\/?Date\((\-?\d+)/i;for(r.createFromInputFallback=g("moment construction falls back to js Date. This is discouraged and will be removed in upcoming major release. Please refer to https://github.com/moment/moment/issues/1407 for more info.",function(n){n._d=new Date(n._i+(n._useUTC?" UTC":""))}),u("Y",0,0,function(){var n=this.year();return n<=9999?""+n:"+"+n}),u(0,["YY",2],0,function(){return this.year()%100}),u(0,["YYYY",4],0,"year"),u(0,["YYYYY",5],0,"year"),u(0,["YYYYYY",6,!0],0,"year"),v("year","y"),i("Y",vu),i("YY",l,w),i("YYYY",kf,bf),i("YYYYY",au,cu),i("YYYYYY",au,cu),h(["YYYYY","YYYYYY"],k),h("YYYY",function(n,t){t[k]=n.length===2?r.parseTwoDigitYear(n):e(n)}),h("YY",function(n,t){t[k]=r.parseTwoDigitYear(n)}),h("Y",function(n,t){t[k]=parseInt(n,10)}),r.parseTwoDigitYear=function(n){return e(n)+(e(n)>68?1900:2e3)},fe=di("FullYear",!1),r.ISO_8601=function(){},cs=g("moment().min is deprecated, use moment.min instead. https://github.com/moment/moment/issues/1548",function(){var n=c.apply(null,arguments);return this.isValid()&&n.isValid()?n<this?this:n:eu()}),ls=g("moment().max is deprecated, use moment.max instead. https://github.com/moment/moment/issues/1548",function(){var n=c.apply(null,arguments);return this.isValid()&&n.isValid()?n>this?this:n:eu()}),vs=function(){return Date.now?Date.now():+new Date},ys("Z",":"),ys("ZZ",""),i("Z",yu),i("ZZ",yu),h(["Z","ZZ"],function(n,t,i){i._useUTC=!0;i._tzm=he(yu,n)}),ps=/([\+\-]|\d\d)/gi,r.updateOffset=function(){},bs=/^(\-)?(?:(\d*)[. ])?(\d+)\:(\d+)(?:\:(\d+)\.?(\d{3})?\d*)?$/,ks=/^(-)?P(?:(?:([0-9,.]*)Y)?(?:([0-9,.]*)M)?(?:([0-9,.]*)D)?(?:T(?:([0-9,.]*)H)?(?:([0-9,.]*)M)?(?:([0-9,.]*)S)?)?|([0-9,.]*)W)$/,ti.fn=du.prototype,th=gs(1,"add"),ih=gs(-1,"subtract"),r.defaultFormat="YYYY-MM-DDTHH:mm:ssZ",ae=g("moment().lang() is deprecated. Instead, use moment().localeData() to get the language configuration. Use moment().locale() to change languages.",function(n){return n===undefined?this.localeData():this.locale(n)}),u(0,["gg",2],0,function(){return this.weekYear()%100}),u(0,["GG",2],0,function(){return this.isoWeekYear()%100}),gu("gggg","weekYear"),gu("ggggg","weekYear"),gu("GGGG","isoWeekYear"),gu("GGGGG","isoWeekYear"),v("weekYear","gg"),v("isoWeekYear","GG"),i("G",vu),i("g",vu),i("GG",l,w),i("gg",l,w),i("GGGG",kf,bf),i("gggg",kf,bf),i("GGGGG",au,cu),i("ggggg",au,cu),lr(["gggg","ggggg","GGGG","GGGGG"],function(n,t,i,r){t[r.substr(0,2)]=e(n)}),lr(["gg","GG"],function(n,t,i,u){t[u]=r.parseTwoDigitYear(n)}),u("Q",0,"Qo","quarter"),v("quarter","Q"),i("Q",vo),h("Q",function(n,t){t[kt]=(e(n)-1)*3}),u("w",["ww",2],"wo","week"),u("W",["WW",2],"Wo","isoWeek"),v("week","w"),v("isoWeek","W"),i("w",l),i("ww",l,w),i("W",l),i("WW",l,w),lr(["w","ww","W","WW"],function(n,t,i,r){t[r.substr(0,1)]=e(n)}),eh={dow:0,doy:6},u("D",["DD",2],"Do","date"),v("date","D"),i("D",l),i("DD",l,w),i("Do",function(n,t){return n?t._ordinalParse:t._ordinalParseLenient}),h(["D","DD"],st),h("Do",function(n,t){t[st]=e(n.match(l)[0],10)}),ve=di("Date",!0),u("d",0,"do","day"),u("dd",0,0,function(n){return this.localeData().weekdaysMin(this,n)}),u("ddd",0,0,function(n){return this.localeData().weekdaysShort(this,n)}),u("dddd",0,0,function(n){return this.localeData().weekdays(this,n)}),u("e",0,0,"weekday"),u("E",0,0,"isoWeekday"),v("day","d"),v("weekday","e"),v("isoWeekday","E"),i("d",l),i("e",l),i("E",l),i("dd",cr),i("ddd",cr),i("dddd",cr),lr(["dd","ddd","dddd"],function(n,t,i,r){var u=i._locale.weekdaysParse(n,r,i._strict);u!=null?t.d=u:o(i).invalidWeekday=n}),lr(["d","e","E"],function(n,t,i,r){t[r]=e(n)}),oh="Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),sh="Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),hh="Su_Mo_Tu_We_Th_Fr_Sa".split("_"),u("DDD",["DDDD",3],"DDDo","dayOfYear"),v("dayOfYear","DDD"),i("DDD",lu),i("DDDD",yo),h(["DDD","DDDD"],function(n,t,i){i._dayOfYear=e(n)}),u("H",["HH",2],0,"hour"),u("h",["hh",2],0,ye),u("hmm",0,0,function(){return""+ye.apply(this)+bt(this.minutes(),2)}),u("hmmss",0,0,function(){return""+ye.apply(this)+bt(this.minutes(),2)+bt(this.seconds(),2)}),u("Hmm",0,0,function(){return""+this.hours()+bt(this.minutes(),2)}),u("Hmmss",0,0,function(){return""+this.hours()+bt(this.minutes(),2)+bt(this.seconds(),2)}),ch("a",!0),ch("A",!1),v("hour","h"),i("a",lh),i("A",lh),i("H",l),i("h",l),i("HH",l,w),i("hh",l,w),i("hmm",po),i("hmmss",wo),i("Hmm",po),i("Hmmss",wo),h(["H","HH"],a),h(["a","A"],function(n,t,i){i._isPm=i._locale.isPM(n);i._meridiem=n}),h(["h","hh"],function(n,t,i){t[a]=e(n);o(i).bigHour=!0}),h("hmm",function(n,t,i){var r=n.length-2;t[a]=e(n.substr(0,r));t[d]=e(n.substr(r));o(i).bigHour=!0}),h("hmmss",function(n,t,i){var r=n.length-4,u=n.length-2;t[a]=e(n.substr(0,r));t[d]=e(n.substr(r,2));t[dt]=e(n.substr(u));o(i).bigHour=!0}),h("Hmm",function(n,t){var i=n.length-2;t[a]=e(n.substr(0,i));t[d]=e(n.substr(i))}),h("Hmmss",function(n,t){var i=n.length-4,r=n.length-2;t[a]=e(n.substr(0,i));t[d]=e(n.substr(i,2));t[dt]=e(n.substr(r))}),ah=/[ap]\.?m?\.?/i,vh=di("Hours",!0),u("m",["mm",2],0,"minute"),v("minute","m"),i("m",l),i("mm",l,w),h(["m","mm"],d),yh=di("Minutes",!1),u("s",["ss",2],0,"second"),v("second","s"),i("s",l),i("ss",l,w),h(["s","ss"],dt),ph=di("Seconds",!1),u("S",0,0,function(){return~~(this.millisecond()/100)}),u(0,["SS",2],0,function(){return~~(this.millisecond()/10)}),u(0,["SSS",3],0,"millisecond"),u(0,["SSSS",4],0,function(){return this.millisecond()*10}),u(0,["SSSSS",5],0,function(){return this.millisecond()*100}),u(0,["SSSSSS",6],0,function(){return this.millisecond()*1e3}),u(0,["SSSSSSS",7],0,function(){return this.millisecond()*1e4}),u(0,["SSSSSSSS",8],0,function(){return this.millisecond()*1e5}),u(0,["SSSSSSSSS",9],0,function(){return this.millisecond()*1e6}),v("millisecond","ms"),i("S",lu,vo),i("SS",lu,w),i("SSS",lu,yo),ii="SSSS";ii.length<=9;ii+="S")i(ii,/\d+/);for(ii="S";ii.length<=9;ii+="S")h(ii,vp);wh=di("Milliseconds",!1);u("z",0,0,"zoneAbbr");u("zz",0,0,"zoneName");t=or.prototype;t.add=th;t.calendar=uv;t.clone=fv;t.diff=av;t.endOf=ty;t.format=wv;t.from=bv;t.fromNow=kv;t.to=dv;t.toNow=gv;t.get=co;t.invalidAt=cy;t.isAfter=ev;t.isBefore=ov;t.isBetween=sv;t.isSame=hv;t.isSameOrAfter=cv;t.isSameOrBefore=lv;t.isValid=sy;t.lang=ae;t.locale=rh;t.localeData=uh;t.max=ls;t.min=cs;t.parsingFlags=hy;t.set=co;t.startOf=ny;t.subtract=ih;t.toArray=fy;t.toObject=ey;t.toDate=uy;t.toISOString=pv;t.toJSON=oy;t.toString=yv;t.unix=ry;t.valueOf=iy;t.creationData=ly;t.year=fe;t.isLeapYear=ua;t.weekYear=ay;t.isoWeekYear=vy;t.quarter=t.quarters=by;t.month=ns;t.daysInMonth=pl;t.week=t.weeks=np;t.isoWeek=t.isoWeeks=tp;t.weeksInYear=py;t.isoWeeksInYear=yy;t.date=ve;t.day=t.days=op;t.weekday=sp;t.isoWeekday=hp;t.dayOfYear=cp;t.hour=t.hours=vh;t.minute=t.minutes=yh;t.second=t.seconds=ph;t.millisecond=t.milliseconds=wh;t.utcOffset=ya;t.utc=wa;t.local=ba;t.parseZone=ka;t.hasAlignedHourOffset=da;t.isDST=ga;t.isDSTShifted=nv;t.isLocal=tv;t.isUtcOffset=iv;t.isUtc=ws;t.isUTC=ws;t.zoneAbbr=yp;t.zoneName=pp;t.dates=g("dates accessor is deprecated. Use date instead.",ve);t.months=g("months accessor is deprecated. Use month instead",ns);t.years=g("years accessor is deprecated. Use year instead",fe);t.zone=g("moment().zone is deprecated, use moment().utcOffset instead. https://github.com/moment/moment/issues/1779",pa);pe=t;bh={sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"};kh={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"};dh="Invalid date";gh="%d";nc=/\d{1,2}/;ic={future:"in %s",past:"%s ago",s:"a few seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"};f=uo.prototype;f._calendar=bh;f.calendar=kp;f._longDateFormat=kh;f.longDateFormat=dp;f._invalidDate=dh;f.invalidDate=gp;f._ordinal=gh;f.ordinal=nw;f._ordinalParse=nc;f.preparse=tc;f.postformat=tc;f._relativeTime=ic;f.relativeTime=tw;f.pastFuture=iw;f.set=rw;f.months=al;f._months=bo;f.monthsShort=vl;f._monthsShort=ko;f.monthsParse=yl;f._monthsRegex=is;f.monthsRegex=bl;f._monthsShortRegex=ts;f.monthsShortRegex=wl;f.week=ky;f._week=eh;f.firstDayOfYear=gy;f.firstDayOfWeek=dy;f.weekdays=rp;f._weekdays=oh;f.weekdaysMin=fp;f._weekdaysMin=hh;f.weekdaysShort=up;f._weekdaysShort=sh;f.weekdaysParse=ep;f.isPM=lp;f._meridiemParse=ah;f.meridiem=ap;sr("en",{ordinalParse:/\d{1,2}(th|st|nd|rd)/,ordinal:function(n){var t=n%10,i=e(n%100/10)===1?"th":t===1?"st":t===2?"nd":t===3?"rd":"th";return n+i}});r.lang=g("moment.lang is deprecated. Use moment.locale instead.",sr);r.langData=g("moment.langData is deprecated. Use moment.localeData instead.",hi);ht=Math.abs;var pw=ri("ms"),ww=ri("s"),bw=ri("m"),kw=ri("h"),dw=ri("d"),gw=ri("w"),nb=ri("M"),tb=ri("y");var rb=vi("milliseconds"),ub=vi("seconds"),fb=vi("minutes"),eb=vi("hours"),ob=vi("days"),sb=vi("months"),hb=vi("years");yi=Math.round;gt={s:45,m:45,h:22,d:26,M:11};nf=Math.abs;s=du.prototype;s.abs=hw;s.add=cw;s.subtract=lw;s.as=vw;s.asMilliseconds=pw;s.asSeconds=ww;s.asMinutes=bw;s.asHours=kw;s.asDays=dw;s.asWeeks=gw;s.asMonths=nb;s.asYears=tb;s.valueOf=yw;s._bubble=aw;s.get=ib;s.milliseconds=rb;s.seconds=ub;s.minutes=fb;s.hours=eb;s.days=ob;s.weeks=cb;s.months=sb;s.years=hb;s.humanize=yb;s.toISOString=tf;s.toString=tf;s.toJSON=tf;s.locale=rh;s.localeData=uh;s.toIsoString=g("toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)",tf);s.lang=ae;u("X",0,0,"unix");u("x",0,0,"valueOf");i("x",vu);i("X",/[+-]?\d+(\.\d{1,3})?/);h("X",function(n,t,i){i._d=new Date(parseFloat(n,10)*1e3)});h("x",function(n,t,i){i._d=new Date(e(n))});
//! moment.js
//! version : 2.11.2
//! authors : Tim Wood, Iskren Chernev, Moment.js contributors
//! license : MIT
//! momentjs.com
r.version="2.11.2";tl(c);r.fn=pe;r.min=aa;r.max=va;r.now=vs;r.utc=ki;r.unix=wp;r.months=uw;r.isDate=uu;r.locale=sr;r.invalid=eu;r.duration=ti;r.isMoment=ni;r.weekdays=ew;r.parseZone=bp;r.localeData=hi;r.isDuration=se;r.monthsShort=fw;r.weekdaysMin=sw;r.defineLocale=oo;r.weekdaysShort=ow;r.normalizeUnits=b;r.relativeTimeThreshold=vb;r.prototype=pe;n=r;
//! moment.js locale configuration
//! locale : afrikaans (af)
//! author : Werner Mollentze : https://github.com/wernerm
pb=n.defineLocale("af",{months:"Januarie_Februarie_Maart_April_Mei_Junie_Julie_Augustus_September_Oktober_November_Desember".split("_"),monthsShort:"Jan_Feb_Mar_Apr_Mei_Jun_Jul_Aug_Sep_Okt_Nov_Des".split("_"),weekdays:"Sondag_Maandag_Dinsdag_Woensdag_Donderdag_Vrydag_Saterdag".split("_"),weekdaysShort:"Son_Maa_Din_Woe_Don_Vry_Sat".split("_"),weekdaysMin:"So_Ma_Di_Wo_Do_Vr_Sa".split("_"),meridiemParse:/vm|nm/i,isPM:function(n){return/^nm$/i.test(n)},meridiem:function(n,t,i){return n<12?i?"vm":"VM":i?"nm":"NM"},longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Vandag om] LT",nextDay:"[Môre om] LT",nextWeek:"dddd [om] LT",lastDay:"[Gister om] LT",lastWeek:"[Laas] dddd [om] LT",sameElse:"L"},relativeTime:{future:"oor %s",past:"%s gelede",s:"'n paar sekondes",m:"'n minuut",mm:"%d minute",h:"'n uur",hh:"%d ure",d:"'n dag",dd:"%d dae",M:"'n maand",MM:"%d maande",y:"'n jaar",yy:"%d jaar"},ordinalParse:/\d{1,2}(ste|de)/,ordinal:function(n){return n+(n===1||n===8||n>=20?"ste":"de")},week:{dow:1,doy:4}});
//! moment.js locale configuration
//! locale : Moroccan Arabic (ar-ma)
//! author : ElFadili Yassine : https://github.com/ElFadiliY
//! author : Abdel Said : https://github.com/abdelsaid
wb=n.defineLocale("ar-ma",{months:"يناير_فبراير_مارس_أبريل_ماي_يونيو_يوليوز_غشت_شتنبر_أكتوبر_نونبر_دجنبر".split("_"),monthsShort:"يناير_فبراير_مارس_أبريل_ماي_يونيو_يوليوز_غشت_شتنبر_أكتوبر_نونبر_دجنبر".split("_"),weekdays:"الأحد_الإتنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت".split("_"),weekdaysShort:"احد_اتنين_ثلاثاء_اربعاء_خميس_جمعة_سبت".split("_"),weekdaysMin:"ح_ن_ث_ر_خ_ج_س".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[اليوم على الساعة] LT",nextDay:"[غدا على الساعة] LT",nextWeek:"dddd [على الساعة] LT",lastDay:"[أمس على الساعة] LT",lastWeek:"dddd [على الساعة] LT",sameElse:"L"},relativeTime:{future:"في %s",past:"منذ %s",s:"ثوان",m:"دقيقة",mm:"%d دقائق",h:"ساعة",hh:"%d ساعات",d:"يوم",dd:"%d أيام",M:"شهر",MM:"%d أشهر",y:"سنة",yy:"%d سنوات"},week:{dow:0,doy:12}});
//! moment.js locale configuration
//! locale : Arabic Saudi Arabia (ar-sa)
//! author : Suhail Alkowaileet : https://github.com/xsoh
var bb={"1":"1","2":"2","3":"3","4":"4","5":"5","6":"6","7":"7","8":"8","9":"9","0":"0"},kb={"1":"1","2":"2","3":"3","4":"4","5":"5","6":"6","7":"7","8":"8","9":"9","0":"0"},ait=n.defineLocale("ar-sa",{months:"يناير_فبراير_مارس_أبريل_مايو_يونيو_يوليو_أغسطس_سبتمبر_أكتوبر_نوفمبر_ديسمبر".split("_"),monthsShort:"يناير_فبراير_مارس_أبريل_مايو_يونيو_يوليو_أغسطس_سبتمبر_أكتوبر_نوفمبر_ديسمبر".split("_"),weekdays:"الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت".split("_"),weekdaysShort:"أحد_إثنين_ثلاثاء_أربعاء_خميس_جمعة_سبت".split("_"),weekdaysMin:"ح_ن_ث_ر_خ_ج_س".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},meridiemParse:/ص|م/,isPM:function(n){return"م"===n},meridiem:function(n){return n<12?"ص":"م"},calendar:{sameDay:"[اليوم على الساعة] LT",nextDay:"[غدا على الساعة] LT",nextWeek:"dddd [على الساعة] LT",lastDay:"[أمس على الساعة] LT",lastWeek:"dddd [على الساعة] LT",sameElse:"L"},relativeTime:{future:"في %s",past:"منذ %s",s:"ثوان",m:"دقيقة",mm:"%d دقائق",h:"ساعة",hh:"%d ساعات",d:"يوم",dd:"%d أيام",M:"شهر",MM:"%d أشهر",y:"سنة",yy:"%d سنوات"},preparse:function(n){return n.replace(/[١٢٣٤٥٦٧٨٩٠]/g,function(n){return kb[n]}).replace(/،/g,",")},postformat:function(n){return n.replace(/\d/g,function(n){return bb[n]}).replace(/,/g,"،")},week:{dow:0,doy:12}});
//! moment.js locale configuration
//! locale  : Tunisian Arabic (ar-tn)
db=n.defineLocale("ar-tn",{months:"جانفي_فيفري_مارس_أفريل_ماي_جوان_جويلية_أوت_سبتمبر_أكتوبر_نوفمبر_ديسمبر".split("_"),monthsShort:"جانفي_فيفري_مارس_أفريل_ماي_جوان_جويلية_أوت_سبتمبر_أكتوبر_نوفمبر_ديسمبر".split("_"),weekdays:"الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت".split("_"),weekdaysShort:"أحد_إثنين_ثلاثاء_أربعاء_خميس_جمعة_سبت".split("_"),weekdaysMin:"ح_ن_ث_ر_خ_ج_س".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[اليوم على الساعة] LT",nextDay:"[غدا على الساعة] LT",nextWeek:"dddd [على الساعة] LT",lastDay:"[أمس على الساعة] LT",lastWeek:"dddd [على الساعة] LT",sameElse:"L"},relativeTime:{future:"في %s",past:"منذ %s",s:"ثوان",m:"دقيقة",mm:"%d دقائق",h:"ساعة",hh:"%d ساعات",d:"يوم",dd:"%d أيام",M:"شهر",MM:"%d أشهر",y:"سنة",yy:"%d سنوات"},week:{dow:1,doy:4}});
//! moment.js locale configuration
//! Locale: Arabic (ar)
//! Author: Abdel Said: https://github.com/abdelsaid
//! Changes in months, weekdays: Ahmed Elkhatib
//! Native plural forms: forabi https://github.com/forabi
var gb={"1":"١","2":"٢","3":"٣","4":"٤","5":"٥","6":"٦","7":"٧","8":"٨","9":"٩","0":"٠"},nk={"١":"1","٢":"2","٣":"3","٤":"4","٥":"5","٦":"6","٧":"7","٨":"8","٩":"9","٠":"0"},oc=function(n){return n===0?0:n===1?1:n===2?2:n%100>=3&&n%100<=10?3:n%100>=11?4:5},tk={s:["أقل من ثانية","ثانية واحدة",["ثانيتان","ثانيتين"],"%d ثوان","%d ثانية","%d ثانية"],m:["أقل من دقيقة","دقيقة واحدة",["دقيقتان","دقيقتين"],"%d دقائق","%d دقيقة","%d دقيقة"],h:["أقل من ساعة","ساعة واحدة",["ساعتان","ساعتين"],"%d ساعات","%d ساعة","%d ساعة"],d:["أقل من يوم","يوم واحد",["يومان","يومين"],"%d أيام","%d يومًا","%d يوم"],M:["أقل من شهر","شهر واحد",["شهران","شهرين"],"%d أشهر","%d شهرا","%d شهر"],y:["أقل من عام","عام واحد",["عامان","عامين"],"%d أعوام","%d عامًا","%d عام"]},nt=function(n){return function(t,i){var u=oc(t),r=tk[n][oc(t)];return u===2&&(r=r[i?0:1]),r.replace(/%d/i,t)}},sc=["كانون الثاني يناير","شباط فبراير","آذار مارس","نيسان أبريل","أيار مايو","حزيران يونيو","تموز يوليو","آب أغسطس","أيلول سبتمبر","تشرين الأول أكتوبر","تشرين الثاني نوفمبر","كانون الأول ديسمبر"],vit=n.defineLocale("ar",{months:sc,monthsShort:sc,weekdays:"الأحد_الإثنين_الثلاثاء_الأربعاء_الخميس_الجمعة_السبت".split("_"),weekdaysShort:"أحد_إثنين_ثلاثاء_أربعاء_خميس_جمعة_سبت".split("_"),weekdaysMin:"ح_ن_ث_ر_خ_ج_س".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"D/‏M/‏YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},meridiemParse:/ص|م/,isPM:function(n){return"م"===n},meridiem:function(n){return n<12?"ص":"م"},calendar:{sameDay:"[اليوم عند الساعة] LT",nextDay:"[غدًا عند الساعة] LT",nextWeek:"dddd [عند الساعة] LT",lastDay:"[أمس عند الساعة] LT",lastWeek:"dddd [عند الساعة] LT",sameElse:"L"},relativeTime:{future:"بعد %s",past:"منذ %s",s:nt("s"),m:nt("m"),mm:nt("m"),h:nt("h"),hh:nt("h"),d:nt("d"),dd:nt("d"),M:nt("M"),MM:nt("M"),y:nt("y"),yy:nt("y")},preparse:function(n){return n.replace(/\u200f/g,"").replace(/[١٢٣٤٥٦٧٨٩٠]/g,function(n){return nk[n]}).replace(/،/g,",")},postformat:function(n){return n.replace(/\d/g,function(n){return gb[n]}).replace(/,/g,"،")},week:{dow:0,doy:12}});
//! moment.js locale configuration
//! locale : azerbaijani (az)
//! author : topchiyev : https://github.com/topchiyev
rf={1:"-inci",5:"-inci",8:"-inci",70:"-inci",80:"-inci",2:"-nci",7:"-nci",20:"-nci",50:"-nci",3:"-üncü",4:"-üncü",100:"-üncü",6:"-ncı",9:"-uncu",10:"-uncu",30:"-uncu",60:"-ıncı",90:"-ıncı"};ik=n.defineLocale("az",{months:"yanvar_fevral_mart_aprel_may_iyun_iyul_avqust_sentyabr_oktyabr_noyabr_dekabr".split("_"),monthsShort:"yan_fev_mar_apr_may_iyn_iyl_avq_sen_okt_noy_dek".split("_"),weekdays:"Bazar_Bazar ertəsi_Çərşənbə axşamı_Çərşənbə_Cümə axşamı_Cümə_Şənbə".split("_"),weekdaysShort:"Baz_BzE_ÇAx_Çər_CAx_Cüm_Şən".split("_"),weekdaysMin:"Bz_BE_ÇA_Çə_CA_Cü_Şə".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[bugün saat] LT",nextDay:"[sabah saat] LT",nextWeek:"[gələn həftə] dddd [saat] LT",lastDay:"[dünən] LT",lastWeek:"[keçən həftə] dddd [saat] LT",sameElse:"L"},relativeTime:{future:"%s sonra",past:"%s əvvəl",s:"birneçə saniyyə",m:"bir dəqiqə",mm:"%d dəqiqə",h:"bir saat",hh:"%d saat",d:"bir gün",dd:"%d gün",M:"bir ay",MM:"%d ay",y:"bir il",yy:"%d il"},meridiemParse:/gecə|səhər|gündüz|axşam/,isPM:function(n){return/^(gündüz|axşam)$/.test(n)},meridiem:function(n){return n<4?"gecə":n<12?"səhər":n<17?"gündüz":"axşam"},ordinalParse:/\d{1,2}-(ıncı|inci|nci|üncü|ncı|uncu)/,ordinal:function(n){if(n===0)return n+"-ıncı";var t=n%10,i=n%100-t,r=n>=100?100:null;return n+(rf[t]||rf[i]||rf[r])},week:{dow:1,doy:7}});
//! moment.js locale configuration
//! locale : belarusian (be)
//! author : Dmitry Demidov : https://github.com/demidov91
//! author: Praleska: http://praleska.pro/
//! Author : Menelion Elensúle : https://github.com/Oire
uk=n.defineLocale("be",{months:{format:"студзеня_лютага_сакавіка_красавіка_траўня_чэрвеня_ліпеня_жніўня_верасня_кастрычніка_лістапада_снежня".split("_"),standalone:"студзень_люты_сакавік_красавік_травень_чэрвень_ліпень_жнівень_верасень_кастрычнік_лістапад_снежань".split("_")},monthsShort:"студ_лют_сак_крас_трав_чэрв_ліп_жнів_вер_каст_ліст_снеж".split("_"),weekdays:{format:"нядзелю_панядзелак_аўторак_сераду_чацвер_пятніцу_суботу".split("_"),standalone:"нядзеля_панядзелак_аўторак_серада_чацвер_пятніца_субота".split("_"),isFormat:/\[ ?[Вв] ?(?:мінулую|наступную)? ?\] ?dddd/},weekdaysShort:"нд_пн_ат_ср_чц_пт_сб".split("_"),weekdaysMin:"нд_пн_ат_ср_чц_пт_сб".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY г.",LLL:"D MMMM YYYY г., HH:mm",LLLL:"dddd, D MMMM YYYY г., HH:mm"},calendar:{sameDay:"[Сёння ў] LT",nextDay:"[Заўтра ў] LT",lastDay:"[Учора ў] LT",nextWeek:function(){return"[У] dddd [ў] LT"},lastWeek:function(){switch(this.day()){case 0:case 3:case 5:case 6:return"[У мінулую] dddd [ў] LT";case 1:case 2:case 4:return"[У мінулы] dddd [ў] LT"}},sameElse:"L"},relativeTime:{future:"праз %s",past:"%s таму",s:"некалькі секунд",m:pi,mm:pi,h:pi,hh:pi,d:"дзень",dd:pi,M:"месяц",MM:pi,y:"год",yy:pi},meridiemParse:/ночы|раніцы|дня|вечара/,isPM:function(n){return/^(дня|вечара)$/.test(n)},meridiem:function(n){return n<4?"ночы":n<12?"раніцы":n<17?"дня":"вечара"},ordinalParse:/\d{1,2}-(і|ы|га)/,ordinal:function(n,t){switch(t){case"M":case"d":case"DDD":case"w":case"W":return(n%10==2||n%10==3)&&n%100!=12&&n%100!=13?n+"-і":n+"-ы";case"D":return n+"-га";default:return n}},week:{dow:1,doy:7}});
//! moment.js locale configuration
//! locale : bulgarian (bg)
//! author : Krasen Borisov : https://github.com/kraz
fk=n.defineLocale("bg",{months:"януари_февруари_март_април_май_юни_юли_август_септември_октомври_ноември_декември".split("_"),monthsShort:"янр_фев_мар_апр_май_юни_юли_авг_сеп_окт_ное_дек".split("_"),weekdays:"неделя_понеделник_вторник_сряда_четвъртък_петък_събота".split("_"),weekdaysShort:"нед_пон_вто_сря_чет_пет_съб".split("_"),weekdaysMin:"нд_пн_вт_ср_чт_пт_сб".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"D.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY H:mm",LLLL:"dddd, D MMMM YYYY H:mm"},calendar:{sameDay:"[Днес в] LT",nextDay:"[Утре в] LT",nextWeek:"dddd [в] LT",lastDay:"[Вчера в] LT",lastWeek:function(){switch(this.day()){case 0:case 3:case 6:return"[В изминалата] dddd [в] LT";case 1:case 2:case 4:case 5:return"[В изминалия] dddd [в] LT"}},sameElse:"L"},relativeTime:{future:"след %s",past:"преди %s",s:"няколко секунди",m:"минута",mm:"%d минути",h:"час",hh:"%d часа",d:"ден",dd:"%d дни",M:"месец",MM:"%d месеца",y:"година",yy:"%d години"},ordinalParse:/\d{1,2}-(ев|ен|ти|ви|ри|ми)/,ordinal:function(n){var t=n%10,i=n%100;return n===0?n+"-ев":i===0?n+"-ен":i>10&&i<20?n+"-ти":t===1?n+"-ви":t===2?n+"-ри":t===7||t===8?n+"-ми":n+"-ти"},week:{dow:1,doy:7}});
//! moment.js locale configuration
//! locale : Bengali (bn)
//! author : Kaushik Gandhi : https://github.com/kaushikgandhi
var ek={"1":"১","2":"২","3":"৩","4":"৪","5":"৫","6":"৬","7":"৭","8":"৮","9":"৯","0":"০"},ok={"১":"1","২":"2","৩":"3","৪":"4","৫":"5","৬":"6","৭":"7","৮":"8","৯":"9","০":"0"},yit=n.defineLocale("bn",{months:"জানুয়ারী_ফেবুয়ারী_মার্চ_এপ্রিল_মে_জুন_জুলাই_অগাস্ট_সেপ্টেম্বর_অক্টোবর_নভেম্বর_ডিসেম্বর".split("_"),monthsShort:"জানু_ফেব_মার্চ_এপর_মে_জুন_জুল_অগ_সেপ্ট_অক্টো_নভ_ডিসেম্".split("_"),weekdays:"রবিবার_সোমবার_মঙ্গলবার_বুধবার_বৃহস্পত্তিবার_শুক্রবার_শনিবার".split("_"),weekdaysShort:"রবি_সোম_মঙ্গল_বুধ_বৃহস্পত্তি_শুক্র_শনি".split("_"),weekdaysMin:"রব_সম_মঙ্গ_বু_ব্রিহ_শু_শনি".split("_"),longDateFormat:{LT:"A h:mm সময়",LTS:"A h:mm:ss সময়",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm সময়",LLLL:"dddd, D MMMM YYYY, A h:mm সময়"},calendar:{sameDay:"[আজ] LT",nextDay:"[আগামীকাল] LT",nextWeek:"dddd, LT",lastDay:"[গতকাল] LT",lastWeek:"[গত] dddd, LT",sameElse:"L"},relativeTime:{future:"%s পরে",past:"%s আগে",s:"কয়েক সেকেন্ড",m:"এক মিনিট",mm:"%d মিনিট",h:"এক ঘন্টা",hh:"%d ঘন্টা",d:"এক দিন",dd:"%d দিন",M:"এক মাস",MM:"%d মাস",y:"এক বছর",yy:"%d বছর"},preparse:function(n){return n.replace(/[১২৩৪৫৬৭৮৯০]/g,function(n){return ok[n]})},postformat:function(n){return n.replace(/\d/g,function(n){return ek[n]})},meridiemParse:/রাত|সকাল|দুপুর|বিকাল|রাত/,isPM:function(n){return/^(দুপুর|বিকাল|রাত)$/.test(n)},meridiem:function(n){return n<4?"রাত":n<10?"সকাল":n<17?"দুপুর":n<20?"বিকাল":"রাত"},week:{dow:0,doy:6}});
//! moment.js locale configuration
//! locale : tibetan (bo)
//! author : Thupten N. Chakrishar : https://github.com/vajradog
var sk={"1":"༡","2":"༢","3":"༣","4":"༤","5":"༥","6":"༦","7":"༧","8":"༨","9":"༩","0":"༠"},hk={"༡":"1","༢":"2","༣":"3","༤":"4","༥":"5","༦":"6","༧":"7","༨":"8","༩":"9","༠":"0"},pit=n.defineLocale("bo",{months:"ཟླ་བ་དང་པོ_ཟླ་བ་གཉིས་པ_ཟླ་བ་གསུམ་པ_ཟླ་བ་བཞི་པ_ཟླ་བ་ལྔ་པ_ཟླ་བ་དྲུག་པ_ཟླ་བ་བདུན་པ_ཟླ་བ་བརྒྱད་པ_ཟླ་བ་དགུ་པ_ཟླ་བ་བཅུ་པ_ཟླ་བ་བཅུ་གཅིག་པ_ཟླ་བ་བཅུ་གཉིས་པ".split("_"),monthsShort:"ཟླ་བ་དང་པོ_ཟླ་བ་གཉིས་པ_ཟླ་བ་གསུམ་པ_ཟླ་བ་བཞི་པ_ཟླ་བ་ལྔ་པ_ཟླ་བ་དྲུག་པ_ཟླ་བ་བདུན་པ_ཟླ་བ་བརྒྱད་པ_ཟླ་བ་དགུ་པ_ཟླ་བ་བཅུ་པ_ཟླ་བ་བཅུ་གཅིག་པ_ཟླ་བ་བཅུ་གཉིས་པ".split("_"),weekdays:"གཟའ་ཉི་མ་_གཟའ་ཟླ་བ་_གཟའ་མིག་དམར་_གཟའ་ལྷག་པ་_གཟའ་ཕུར་བུ_གཟའ་པ་སངས་_གཟའ་སྤེན་པ་".split("_"),weekdaysShort:"ཉི་མ་_ཟླ་བ་_མིག་དམར་_ལྷག་པ་_ཕུར་བུ_པ་སངས་_སྤེན་པ་".split("_"),weekdaysMin:"ཉི་མ་_ཟླ་བ་_མིག་དམར་_ལྷག་པ་_ཕུར་བུ_པ་སངས་_སྤེན་པ་".split("_"),longDateFormat:{LT:"A h:mm",LTS:"A h:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm",LLLL:"dddd, D MMMM YYYY, A h:mm"},calendar:{sameDay:"[དི་རིང] LT",nextDay:"[སང་ཉིན] LT",nextWeek:"[བདུན་ཕྲག་རྗེས་མ], LT",lastDay:"[ཁ་སང] LT",lastWeek:"[བདུན་ཕྲག་མཐའ་མ] dddd, LT",sameElse:"L"},relativeTime:{future:"%s ལ་",past:"%s སྔན་ལ",s:"ལམ་སང",m:"སྐར་མ་གཅིག",mm:"%d སྐར་མ",h:"ཆུ་ཚོད་གཅིག",hh:"%d ཆུ་ཚོད",d:"ཉིན་གཅིག",dd:"%d ཉིན་",M:"ཟླ་བ་གཅིག",MM:"%d ཟླ་བ",y:"ལོ་གཅིག",yy:"%d ལོ"},preparse:function(n){return n.replace(/[༡༢༣༤༥༦༧༨༩༠]/g,function(n){return hk[n]})},postformat:function(n){return n.replace(/\d/g,function(n){return sk[n]})},meridiemParse:/མཚན་མོ|ཞོགས་ཀས|ཉིན་གུང|དགོང་དག|མཚན་མོ/,isPM:function(n){return/^(ཉིན་གུང|དགོང་དག|མཚན་མོ)$/.test(n)},meridiem:function(n){return n<4?"མཚན་མོ":n<10?"ཞོགས་ཀས":n<17?"ཉིན་གུང":n<20?"དགོང་དག":"མཚན་མོ"},week:{dow:0,doy:6}});
//! moment.js locale configuration
//! locale : breton (br)
//! author : Jean-Baptiste Le Duigou : https://github.com/jbleduigou
vk=n.defineLocale("br",{months:"Genver_C'hwevrer_Meurzh_Ebrel_Mae_Mezheven_Gouere_Eost_Gwengolo_Here_Du_Kerzu".split("_"),monthsShort:"Gen_C'hwe_Meu_Ebr_Mae_Eve_Gou_Eos_Gwe_Her_Du_Ker".split("_"),weekdays:"Sul_Lun_Meurzh_Merc'her_Yaou_Gwener_Sadorn".split("_"),weekdaysShort:"Sul_Lun_Meu_Mer_Yao_Gwe_Sad".split("_"),weekdaysMin:"Su_Lu_Me_Mer_Ya_Gw_Sa".split("_"),longDateFormat:{LT:"h[e]mm A",LTS:"h[e]mm:ss A",L:"DD/MM/YYYY",LL:"D [a viz] MMMM YYYY",LLL:"D [a viz] MMMM YYYY h[e]mm A",LLLL:"dddd, D [a viz] MMMM YYYY h[e]mm A"},calendar:{sameDay:"[Hiziv da] LT",nextDay:"[Warc'hoazh da] LT",nextWeek:"dddd [da] LT",lastDay:"[Dec'h da] LT",lastWeek:"dddd [paset da] LT",sameElse:"L"},relativeTime:{future:"a-benn %s",past:"%s 'zo",s:"un nebeud segondennoù",m:"ur vunutenn",mm:be,h:"un eur",hh:"%d eur",d:"un devezh",dd:be,M:"ur miz",MM:be,y:"ur bloaz",yy:ck},ordinalParse:/\d{1,2}(añ|vet)/,ordinal:function(n){var t=n===1?"añ":"vet";return n+t},week:{dow:1,doy:4}});
//! moment.js locale configuration
//! locale : bosnian (bs)
//! author : Nedim Cholich : https://github.com/frontyard
//! based on (hr) translation by Bojan Marković
yk=n.defineLocale("bs",{months:"januar_februar_mart_april_maj_juni_juli_august_septembar_oktobar_novembar_decembar".split("_"),monthsShort:"jan._feb._mar._apr._maj._jun._jul._aug._sep._okt._nov._dec.".split("_"),weekdays:"nedjelja_ponedjeljak_utorak_srijeda_četvrtak_petak_subota".split("_"),weekdaysShort:"ned._pon._uto._sri._čet._pet._sub.".split("_"),weekdaysMin:"ne_po_ut_sr_če_pe_su".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD. MM. YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd, D. MMMM YYYY H:mm"},calendar:{sameDay:"[danas u] LT",nextDay:"[sutra u] LT",nextWeek:function(){switch(this.day()){case 0:return"[u] [nedjelju] [u] LT";case 3:return"[u] [srijedu] [u] LT";case 6:return"[u] [subotu] [u] LT";case 1:case 2:case 4:case 5:return"[u] dddd [u] LT"}},lastDay:"[jučer u] LT",lastWeek:function(){switch(this.day()){case 0:case 3:return"[prošlu] dddd [u] LT";case 6:return"[prošle] [subote] [u] LT";case 1:case 2:case 4:case 5:return"[prošli] dddd [u] LT"}},sameElse:"L"},relativeTime:{future:"za %s",past:"prije %s",s:"par sekundi",m:wi,mm:wi,h:wi,hh:wi,d:"dan",dd:wi,M:"mjesec",MM:wi,y:"godinu",yy:wi},ordinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:7}});
//! moment.js locale configuration
//! locale : catalan (ca)
//! author : Juan G. Hurtado : https://github.com/juanghurtado
pk=n.defineLocale("ca",{months:"gener_febrer_març_abril_maig_juny_juliol_agost_setembre_octubre_novembre_desembre".split("_"),monthsShort:"gen._febr._mar._abr._mai._jun._jul._ag._set._oct._nov._des.".split("_"),weekdays:"diumenge_dilluns_dimarts_dimecres_dijous_divendres_dissabte".split("_"),weekdaysShort:"dg._dl._dt._dc._dj._dv._ds.".split("_"),weekdaysMin:"Dg_Dl_Dt_Dc_Dj_Dv_Ds".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY H:mm",LLLL:"dddd D MMMM YYYY H:mm"},calendar:{sameDay:function(){return"[avui a "+(this.hours()!==1?"les":"la")+"] LT"},nextDay:function(){return"[demà a "+(this.hours()!==1?"les":"la")+"] LT"},nextWeek:function(){return"dddd [a "+(this.hours()!==1?"les":"la")+"] LT"},lastDay:function(){return"[ahir a "+(this.hours()!==1?"les":"la")+"] LT"},lastWeek:function(){return"[el] dddd [passat a "+(this.hours()!==1?"les":"la")+"] LT"},sameElse:"L"},relativeTime:{future:"en %s",past:"fa %s",s:"uns segons",m:"un minut",mm:"%d minuts",h:"una hora",hh:"%d hores",d:"un dia",dd:"%d dies",M:"un mes",MM:"%d mesos",y:"un any",yy:"%d anys"},ordinalParse:/\d{1,2}(r|n|t|è|a)/,ordinal:function(n,t){var i=n===1?"r":n===2?"n":n===3?"r":n===4?"t":"è";return(t==="w"||t==="W")&&(i="a"),n+i},week:{dow:1,doy:4}});
//! moment.js locale configuration
//! locale : czech (cs)
//! author : petrbela : https://github.com/petrbela
uf="leden_únor_březen_duben_květen_červen_červenec_srpen_září_říjen_listopad_prosinec".split("_");ff="led_úno_bře_dub_kvě_čvn_čvc_srp_zář_říj_lis_pro".split("_");wk=n.defineLocale("cs",{months:uf,monthsShort:ff,monthsParse:function(n,t){for(var r=[],i=0;i<12;i++)r[i]=new RegExp("^"+n[i]+"$|^"+t[i]+"$","i");return r}(uf,ff),shortMonthsParse:function(n){for(var i=[],t=0;t<12;t++)i[t]=new RegExp("^"+n[t]+"$","i");return i}(ff),longMonthsParse:function(n){for(var i=[],t=0;t<12;t++)i[t]=new RegExp("^"+n[t]+"$","i");return i}(uf),weekdays:"neděle_pondělí_úterý_středa_čtvrtek_pátek_sobota".split("_"),weekdaysShort:"ne_po_út_st_čt_pá_so".split("_"),weekdaysMin:"ne_po_út_st_čt_pá_so".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd D. MMMM YYYY H:mm"},calendar:{sameDay:"[dnes v] LT",nextDay:"[zítra v] LT",nextWeek:function(){switch(this.day()){case 0:return"[v neděli v] LT";case 1:case 2:return"[v] dddd [v] LT";case 3:return"[ve středu v] LT";case 4:return"[ve čtvrtek v] LT";case 5:return"[v pátek v] LT";case 6:return"[v sobotu v] LT"}},lastDay:"[včera v] LT",lastWeek:function(){switch(this.day()){case 0:return"[minulou neděli v] LT";case 1:case 2:return"[minulé] dddd [v] LT";case 3:return"[minulou středu v] LT";case 4:case 5:return"[minulý] dddd [v] LT";case 6:return"[minulou sobotu v] LT"}},sameElse:"L"},relativeTime:{future:"za %s",past:"před %s",s:tt,m:tt,mm:tt,h:tt,hh:tt,d:tt,dd:tt,M:tt,MM:tt,y:tt,yy:tt},ordinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});
//! moment.js locale configuration
//! locale : chuvash (cv)
//! author : Anatoly Mironov : https://github.com/mirontoli
bk=n.defineLocale("cv",{months:"кӑрлач_нарӑс_пуш_ака_май_ҫӗртме_утӑ_ҫурла_авӑн_юпа_чӳк_раштав".split("_"),monthsShort:"кӑр_нар_пуш_ака_май_ҫӗр_утӑ_ҫур_авн_юпа_чӳк_раш".split("_"),weekdays:"вырсарникун_тунтикун_ытларикун_юнкун_кӗҫнерникун_эрнекун_шӑматкун".split("_"),weekdaysShort:"выр_тун_ытл_юн_кӗҫ_эрн_шӑм".split("_"),weekdaysMin:"вр_тн_ыт_юн_кҫ_эр_шм".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD-MM-YYYY",LL:"YYYY [ҫулхи] MMMM [уйӑхӗн] D[-мӗшӗ]",LLL:"YYYY [ҫулхи] MMMM [уйӑхӗн] D[-мӗшӗ], HH:mm",LLLL:"dddd, YYYY [ҫулхи] MMMM [уйӑхӗн] D[-мӗшӗ], HH:mm"},calendar:{sameDay:"[Паян] LT [сехетре]",nextDay:"[Ыран] LT [сехетре]",lastDay:"[Ӗнер] LT [сехетре]",nextWeek:"[Ҫитес] dddd LT [сехетре]",lastWeek:"[Иртнӗ] dddd LT [сехетре]",sameElse:"L"},relativeTime:{future:function(n){var t=/сехет$/i.exec(n)?"рен":/ҫул$/i.exec(n)?"тан":"ран";return n+t},past:"%s каялла",s:"пӗр-ик ҫеккунт",m:"пӗр минут",mm:"%d минут",h:"пӗр сехет",hh:"%d сехет",d:"пӗр кун",dd:"%d кун",M:"пӗр уйӑх",MM:"%d уйӑх",y:"пӗр ҫул",yy:"%d ҫул"},ordinalParse:/\d{1,2}-мӗш/,ordinal:"%d-мӗш",week:{dow:1,doy:7}});
//! moment.js locale configuration
//! locale : Welsh (cy)
//! author : Robert Allen
kk=n.defineLocale("cy",{months:"Ionawr_Chwefror_Mawrth_Ebrill_Mai_Mehefin_Gorffennaf_Awst_Medi_Hydref_Tachwedd_Rhagfyr".split("_"),monthsShort:"Ion_Chwe_Maw_Ebr_Mai_Meh_Gor_Aws_Med_Hyd_Tach_Rhag".split("_"),weekdays:"Dydd Sul_Dydd Llun_Dydd Mawrth_Dydd Mercher_Dydd Iau_Dydd Gwener_Dydd Sadwrn".split("_"),weekdaysShort:"Sul_Llun_Maw_Mer_Iau_Gwe_Sad".split("_"),weekdaysMin:"Su_Ll_Ma_Me_Ia_Gw_Sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Heddiw am] LT",nextDay:"[Yfory am] LT",nextWeek:"dddd [am] LT",lastDay:"[Ddoe am] LT",lastWeek:"dddd [diwethaf am] LT",sameElse:"L"},relativeTime:{future:"mewn %s",past:"%s yn ôl",s:"ychydig eiliadau",m:"munud",mm:"%d munud",h:"awr",hh:"%d awr",d:"diwrnod",dd:"%d diwrnod",M:"mis",MM:"%d mis",y:"blwyddyn",yy:"%d flynedd"},ordinalParse:/\d{1,2}(fed|ain|af|il|ydd|ed|eg)/,ordinal:function(n){var t=n,i="";return t>20?i=t===40||t===50||t===60||t===80||t===100?"fed":"ain":t>0&&(i=["","af","il","ydd","ydd","ed","ed","ed","fed","fed","fed","eg","fed","eg","eg","fed","eg","eg","fed","eg","fed"][t]),n+i},week:{dow:1,doy:4}});
//! moment.js locale configuration
//! locale : danish (da)
//! author : Ulrik Nielsen : https://github.com/mrbase
dk=n.defineLocale("da",{months:"januar_februar_marts_april_maj_juni_juli_august_september_oktober_november_december".split("_"),monthsShort:"jan_feb_mar_apr_maj_jun_jul_aug_sep_okt_nov_dec".split("_"),weekdays:"søndag_mandag_tirsdag_onsdag_torsdag_fredag_lørdag".split("_"),weekdaysShort:"søn_man_tir_ons_tor_fre_lør".split("_"),weekdaysMin:"sø_ma_ti_on_to_fr_lø".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY HH:mm",LLLL:"dddd [d.] D. MMMM YYYY HH:mm"},calendar:{sameDay:"[I dag kl.] LT",nextDay:"[I morgen kl.] LT",nextWeek:"dddd [kl.] LT",lastDay:"[I går kl.] LT",lastWeek:"[sidste] dddd [kl] LT",sameElse:"L"},relativeTime:{future:"om %s",past:"%s siden",s:"få sekunder",m:"et minut",mm:"%d minutter",h:"en time",hh:"%d timer",d:"en dag",dd:"%d dage",M:"en måned",MM:"%d måneder",y:"et år",yy:"%d år"},ordinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});
//! moment.js locale configuration
//! locale : austrian german (de-at)
//! author : lluchs : https://github.com/lluchs
//! author: Menelion Elensúle: https://github.com/Oire
//! author : Martin Groller : https://github.com/MadMG
//! author : Mikolaj Dadela : https://github.com/mik01aj
gk=n.defineLocale("de-at",{months:"Jänner_Februar_März_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember".split("_"),monthsShort:"Jän._Febr._Mrz._Apr._Mai_Jun._Jul._Aug._Sept._Okt._Nov._Dez.".split("_"),weekdays:"Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag".split("_"),weekdaysShort:"So._Mo._Di._Mi._Do._Fr._Sa.".split("_"),weekdaysMin:"So_Mo_Di_Mi_Do_Fr_Sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY HH:mm",LLLL:"dddd, D. MMMM YYYY HH:mm"},calendar:{sameDay:"[heute um] LT [Uhr]",sameElse:"L",nextDay:"[morgen um] LT [Uhr]",nextWeek:"dddd [um] LT [Uhr]",lastDay:"[gestern um] LT [Uhr]",lastWeek:"[letzten] dddd [um] LT [Uhr]"},relativeTime:{future:"in %s",past:"vor %s",s:"ein paar Sekunden",m:ui,mm:"%d Minuten",h:ui,hh:"%d Stunden",d:ui,dd:ui,M:ui,MM:ui,y:ui,yy:ui},ordinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});
//! moment.js locale configuration
//! locale : german (de)
//! author : lluchs : https://github.com/lluchs
//! author: Menelion Elensúle: https://github.com/Oire
//! author : Mikolaj Dadela : https://github.com/mik01aj
nd=n.defineLocale("de",{months:"Januar_Februar_März_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember".split("_"),monthsShort:"Jan._Febr._Mrz._Apr._Mai_Jun._Jul._Aug._Sept._Okt._Nov._Dez.".split("_"),weekdays:"Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag".split("_"),weekdaysShort:"So._Mo._Di._Mi._Do._Fr._Sa.".split("_"),weekdaysMin:"So_Mo_Di_Mi_Do_Fr_Sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY HH:mm",LLLL:"dddd, D. MMMM YYYY HH:mm"},calendar:{sameDay:"[heute um] LT [Uhr]",sameElse:"L",nextDay:"[morgen um] LT [Uhr]",nextWeek:"dddd [um] LT [Uhr]",lastDay:"[gestern um] LT [Uhr]",lastWeek:"[letzten] dddd [um] LT [Uhr]"},relativeTime:{future:"in %s",past:"vor %s",s:"ein paar Sekunden",m:fi,mm:"%d Minuten",h:fi,hh:"%d Stunden",d:fi,dd:fi,M:fi,MM:fi,y:fi,yy:fi},ordinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});
//! moment.js locale configuration
//! locale : dhivehi (dv)
//! author : Jawish Hameed : https://github.com/jawish
var cc=["ޖެނުއަރީ","ފެބްރުއަރީ","މާރިޗު","އޭޕްރީލު","މޭ","ޖޫން","ޖުލައި","އޯގަސްޓު","ސެޕްޓެމްބަރު","އޮކްޓޯބަރު","ނޮވެމްބަރު","ޑިސެމްބަރު"],lc=["އާދިއްތަ","ހޯމަ","އަންގާރަ","ބުދަ","ބުރާސްފަތި","ހުކުރު","ހޮނިހިރު"],wit=n.defineLocale("dv",{months:cc,monthsShort:cc,weekdays:lc,weekdaysShort:lc,weekdaysMin:"އާދި_ހޯމަ_އަން_ބުދަ_ބުރާ_ހުކު_ހޮނި".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"D/M/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},meridiemParse:/މކ|މފ/,isPM:function(n){return""===n},meridiem:function(n){return n<12?"މކ":"މފ"},calendar:{sameDay:"[މިއަދު] LT",nextDay:"[މާދަމާ] LT",nextWeek:"dddd LT",lastDay:"[އިއްޔެ] LT",lastWeek:"[ފާއިތުވި] dddd LT",sameElse:"L"},relativeTime:{future:"ތެރޭގައި %s",past:"ކުރިން %s",s:"ސިކުންތުކޮޅެއް",m:"މިނިޓެއް",mm:"މިނިޓު %d",h:"ގަޑިއިރެއް",hh:"ގަޑިއިރު %d",d:"ދުވަހެއް",dd:"ދުވަސް %d",M:"މަހެއް",MM:"މަސް %d",y:"އަހަރެއް",yy:"އަހަރު %d"},preparse:function(n){return n.replace(/،/g,",")},postformat:function(n){return n.replace(/,/g,"،")},week:{dow:7,doy:12}});
//! moment.js locale configuration
//! locale : modern greek (el)
//! author : Aggelos Karalias : https://github.com/mehiel
td=n.defineLocale("el",{monthsNominativeEl:"Ιανουάριος_Φεβρουάριος_Μάρτιος_Απρίλιος_Μάιος_Ιούνιος_Ιούλιος_Αύγουστος_Σεπτέμβριος_Οκτώβριος_Νοέμβριος_Δεκέμβριος".split("_"),monthsGenitiveEl:"Ιανουαρίου_Φεβρουαρίου_Μαρτίου_Απριλίου_Μαΐου_Ιουνίου_Ιουλίου_Αυγούστου_Σεπτεμβρίου_Οκτωβρίου_Νοεμβρίου_Δεκεμβρίου".split("_"),months:function(n,t){return/D/.test(t.substring(0,t.indexOf("MMMM")))?this._monthsGenitiveEl[n.month()]:this._monthsNominativeEl[n.month()]},monthsShort:"Ιαν_Φεβ_Μαρ_Απρ_Μαϊ_Ιουν_Ιουλ_Αυγ_Σεπ_Οκτ_Νοε_Δεκ".split("_"),weekdays:"Κυριακή_Δευτέρα_Τρίτη_Τετάρτη_Πέμπτη_Παρασκευή_Σάββατο".split("_"),weekdaysShort:"Κυρ_Δευ_Τρι_Τετ_Πεμ_Παρ_Σαβ".split("_"),weekdaysMin:"Κυ_Δε_Τρ_Τε_Πε_Πα_Σα".split("_"),meridiem:function(n,t,i){return n>11?i?"μμ":"ΜΜ":i?"πμ":"ΠΜ"},isPM:function(n){return(n+"").toLowerCase()[0]==="μ"},meridiemParse:/[ΠΜ]\.?Μ?\.?/i,longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY h:mm A",LLLL:"dddd, D MMMM YYYY h:mm A"},calendarEl:{sameDay:"[Σήμερα {}] LT",nextDay:"[Αύριο {}] LT",nextWeek:"dddd [{}] LT",lastDay:"[Χθες {}] LT",lastWeek:function(){switch(this.day()){case 6:return"[το προηγούμενο] dddd [{}] LT";default:return"[την προηγούμενη] dddd [{}] LT"}},sameElse:"L"},calendar:function(n,t){var i=this._calendarEl[n],r=t&&t.hours();return wt(i)&&(i=i.apply(t)),i.replace("{}",r%12==1?"στη":"στις")},relativeTime:{future:"σε %s",past:"%s πριν",s:"λίγα δευτερόλεπτα",m:"ένα λεπτό",mm:"%d λεπτά",h:"μία ώρα",hh:"%d ώρες",d:"μία μέρα",dd:"%d μέρες",M:"ένας μήνας",MM:"%d μήνες",y:"ένας χρόνος",yy:"%d χρόνια"},ordinalParse:/\d{1,2}η/,ordinal:"%dη",week:{dow:1,doy:4}});
//! moment.js locale configuration
//! locale : australian english (en-au)
id=n.defineLocale("en-au",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY h:mm A",LLLL:"dddd, D MMMM YYYY h:mm A"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},ordinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(n){var t=n%10,i=~~(n%100/10)==1?"th":t===1?"st":t===2?"nd":t===3?"rd":"th";return n+i},week:{dow:1,doy:4}});
//! moment.js locale configuration
//! locale : canadian english (en-ca)
//! author : Jonathan Abourbih : https://github.com/jonbca
rd=n.defineLocale("en-ca",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"YYYY-MM-DD",LL:"D MMMM, YYYY",LLL:"D MMMM, YYYY h:mm A",LLLL:"dddd, D MMMM, YYYY h:mm A"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},ordinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(n){var t=n%10,i=~~(n%100/10)==1?"th":t===1?"st":t===2?"nd":t===3?"rd":"th";return n+i}});
//! moment.js locale configuration
//! locale : great britain english (en-gb)
//! author : Chris Gedrim : https://github.com/chrisgedrim
ud=n.defineLocale("en-gb",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},ordinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(n){var t=n%10,i=~~(n%100/10)==1?"th":t===1?"st":t===2?"nd":t===3?"rd":"th";return n+i},week:{dow:1,doy:4}});
//! moment.js locale configuration
//! locale : Irish english (en-ie)
//! author : Chris Cartlidge : https://github.com/chriscartlidge
fd=n.defineLocale("en-ie",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD-MM-YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},ordinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(n){var t=n%10,i=~~(n%100/10)==1?"th":t===1?"st":t===2?"nd":t===3?"rd":"th";return n+i},week:{dow:1,doy:4}});
//! moment.js locale configuration
//! locale : New Zealand english (en-nz)
ed=n.defineLocale("en-nz",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY h:mm A",LLLL:"dddd, D MMMM YYYY h:mm A"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},ordinalParse:/\d{1,2}(st|nd|rd|th)/,ordinal:function(n){var t=n%10,i=~~(n%100/10)==1?"th":t===1?"st":t===2?"nd":t===3?"rd":"th";return n+i},week:{dow:1,doy:4}});
//! moment.js locale configuration
//! locale : esperanto (eo)
//! author : Colin Dean : https://github.com/colindean
//! komento: Mi estas malcerta se mi korekte traktis akuzativojn en tiu traduko.
//!          Se ne, bonvolu korekti kaj avizi min por ke mi povas lerni!
od=n.defineLocale("eo",{months:"januaro_februaro_marto_aprilo_majo_junio_julio_aŭgusto_septembro_oktobro_novembro_decembro".split("_"),monthsShort:"jan_feb_mar_apr_maj_jun_jul_aŭg_sep_okt_nov_dec".split("_"),weekdays:"Dimanĉo_Lundo_Mardo_Merkredo_Ĵaŭdo_Vendredo_Sabato".split("_"),weekdaysShort:"Dim_Lun_Mard_Merk_Ĵaŭ_Ven_Sab".split("_"),weekdaysMin:"Di_Lu_Ma_Me_Ĵa_Ve_Sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY-MM-DD",LL:"D[-an de] MMMM, YYYY",LLL:"D[-an de] MMMM, YYYY HH:mm",LLLL:"dddd, [la] D[-an de] MMMM, YYYY HH:mm"},meridiemParse:/[ap]\.t\.m/i,isPM:function(n){return n.charAt(0).toLowerCase()==="p"},meridiem:function(n,t,i){return n>11?i?"p.t.m.":"P.T.M.":i?"a.t.m.":"A.T.M."},calendar:{sameDay:"[Hodiaŭ je] LT",nextDay:"[Morgaŭ je] LT",nextWeek:"dddd [je] LT",lastDay:"[Hieraŭ je] LT",lastWeek:"[pasinta] dddd [je] LT",sameElse:"L"},relativeTime:{future:"je %s",past:"antaŭ %s",s:"sekundoj",m:"minuto",mm:"%d minutoj",h:"horo",hh:"%d horoj",d:"tago",dd:"%d tagoj",M:"monato",MM:"%d monatoj",y:"jaro",yy:"%d jaroj"},ordinalParse:/\d{1,2}a/,ordinal:"%da",week:{dow:1,doy:7}});
//! moment.js locale configuration
//! locale : spanish (es)
//! author : Julio Napurí : https://github.com/julionc
var sd="ene._feb._mar._abr._may._jun._jul._ago._sep._oct._nov._dic.".split("_"),hd="ene_feb_mar_abr_may_jun_jul_ago_sep_oct_nov_dic".split("_"),bit=n.defineLocale("es",{months:"enero_febrero_marzo_abril_mayo_junio_julio_agosto_septiembre_octubre_noviembre_diciembre".split("_"),monthsShort:function(n,t){return/-MMM-/.test(t)?hd[n.month()]:sd[n.month()]},weekdays:"domingo_lunes_martes_miércoles_jueves_viernes_sábado".split("_"),weekdaysShort:"dom._lun._mar._mié._jue._vie._sáb.".split("_"),weekdaysMin:"do_lu_ma_mi_ju_vi_sá".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD/MM/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY H:mm",LLLL:"dddd, D [de] MMMM [de] YYYY H:mm"},calendar:{sameDay:function(){return"[hoy a la"+(this.hours()!==1?"s":"")+"] LT"},nextDay:function(){return"[mañana a la"+(this.hours()!==1?"s":"")+"] LT"},nextWeek:function(){return"dddd [a la"+(this.hours()!==1?"s":"")+"] LT"},lastDay:function(){return"[ayer a la"+(this.hours()!==1?"s":"")+"] LT"},lastWeek:function(){return"[el] dddd [pasado a la"+(this.hours()!==1?"s":"")+"] LT"},sameElse:"L"},relativeTime:{future:"en %s",past:"hace %s",s:"unos segundos",m:"un minuto",mm:"%d minutos",h:"una hora",hh:"%d horas",d:"un día",dd:"%d días",M:"un mes",MM:"%d meses",y:"un año",yy:"%d años"},ordinalParse:/\d{1,2}º/,ordinal:"%dº",week:{dow:1,doy:4}});
//! moment.js locale configuration
//! locale : estonian (et)
//! author : Henry Kehlmann : https://github.com/madhenry
//! improvements : Illimar Tambek : https://github.com/ragulka
cd=n.defineLocale("et",{months:"jaanuar_veebruar_märts_aprill_mai_juuni_juuli_august_september_oktoober_november_detsember".split("_"),monthsShort:"jaan_veebr_märts_apr_mai_juuni_juuli_aug_sept_okt_nov_dets".split("_"),weekdays:"pühapäev_esmaspäev_teisipäev_kolmapäev_neljapäev_reede_laupäev".split("_"),weekdaysShort:"P_E_T_K_N_R_L".split("_"),weekdaysMin:"P_E_T_K_N_R_L".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd, D. MMMM YYYY H:mm"},calendar:{sameDay:"[Täna,] LT",nextDay:"[Homme,] LT",nextWeek:"[Järgmine] dddd LT",lastDay:"[Eile,] LT",lastWeek:"[Eelmine] dddd LT",sameElse:"L"},relativeTime:{future:"%s pärast",past:"%s tagasi",s:ct,m:ct,mm:ct,h:ct,hh:ct,d:ct,dd:"%d päeva",M:ct,MM:ct,y:ct,yy:ct},ordinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});
//! moment.js locale configuration
//! locale : euskara (eu)
//! author : Eneko Illarramendi : https://github.com/eillarra
ld=n.defineLocale("eu",{months:"urtarrila_otsaila_martxoa_apirila_maiatza_ekaina_uztaila_abuztua_iraila_urria_azaroa_abendua".split("_"),monthsShort:"urt._ots._mar._api._mai._eka._uzt._abu._ira._urr._aza._abe.".split("_"),weekdays:"igandea_astelehena_asteartea_asteazkena_osteguna_ostirala_larunbata".split("_"),weekdaysShort:"ig._al._ar._az._og._ol._lr.".split("_"),weekdaysMin:"ig_al_ar_az_og_ol_lr".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY-MM-DD",LL:"YYYY[ko] MMMM[ren] D[a]",LLL:"YYYY[ko] MMMM[ren] D[a] HH:mm",LLLL:"dddd, YYYY[ko] MMMM[ren] D[a] HH:mm",l:"YYYY-M-D",ll:"YYYY[ko] MMM D[a]",lll:"YYYY[ko] MMM D[a] HH:mm",llll:"ddd, YYYY[ko] MMM D[a] HH:mm"},calendar:{sameDay:"[gaur] LT[etan]",nextDay:"[bihar] LT[etan]",nextWeek:"dddd LT[etan]",lastDay:"[atzo] LT[etan]",lastWeek:"[aurreko] dddd LT[etan]",sameElse:"L"},relativeTime:{future:"%s barru",past:"duela %s",s:"segundo batzuk",m:"minutu bat",mm:"%d minutu",h:"ordu bat",hh:"%d ordu",d:"egun bat",dd:"%d egun",M:"hilabete bat",MM:"%d hilabete",y:"urte bat",yy:"%d urte"},ordinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:7}});
//! moment.js locale configuration
//! locale : Persian (fa)
//! author : Ebrahim Byagowi : https://github.com/ebraminio
var ad={"1":"۱","2":"۲","3":"۳","4":"۴","5":"۵","6":"۶","7":"۷","8":"۸","9":"۹","0":"۰"},vd={"۱":"1","۲":"2","۳":"3","۴":"4","۵":"5","۶":"6","۷":"7","۸":"8","۹":"9","۰":"0"},kit=n.defineLocale("fa",{months:"ژانویه_فوریه_مارس_آوریل_مه_ژوئن_ژوئیه_اوت_سپتامبر_اکتبر_نوامبر_دسامبر".split("_"),monthsShort:"ژانویه_فوریه_مارس_آوریل_مه_ژوئن_ژوئیه_اوت_سپتامبر_اکتبر_نوامبر_دسامبر".split("_"),weekdays:"یک‌شنبه_دوشنبه_سه‌شنبه_چهارشنبه_پنج‌شنبه_جمعه_شنبه".split("_"),weekdaysShort:"یک‌شنبه_دوشنبه_سه‌شنبه_چهارشنبه_پنج‌شنبه_جمعه_شنبه".split("_"),weekdaysMin:"ی_د_س_چ_پ_ج_ش".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},meridiemParse:/قبل از ظهر|بعد از ظهر/,isPM:function(n){return/بعد از ظهر/.test(n)},meridiem:function(n){return n<12?"قبل از ظهر":"بعد از ظهر"},calendar:{sameDay:"[امروز ساعت] LT",nextDay:"[فردا ساعت] LT",nextWeek:"dddd [ساعت] LT",lastDay:"[دیروز ساعت] LT",lastWeek:"dddd [پیش] [ساعت] LT",sameElse:"L"},relativeTime:{future:"در %s",past:"%s پیش",s:"چندین ثانیه",m:"یک دقیقه",mm:"%d دقیقه",h:"یک ساعت",hh:"%d ساعت",d:"یک روز",dd:"%d روز",M:"یک ماه",MM:"%d ماه",y:"یک سال",yy:"%d سال"},preparse:function(n){return n.replace(/[۰-۹]/g,function(n){return vd[n]}).replace(/،/g,",")},postformat:function(n){return n.replace(/\d/g,function(n){return ad[n]}).replace(/,/g,"،")},ordinalParse:/\d{1,2}م/,ordinal:"%dم",week:{dow:6,doy:12}});
//! moment.js locale configuration
//! locale : finnish (fi)
//! author : Tarmo Aidantausta : https://github.com/bleadof
wr="nolla yksi kaksi kolme neljä viisi kuusi seitsemän kahdeksan yhdeksän".split(" ");ac=["nolla","yhden","kahden","kolmen","neljän","viiden","kuuden",wr[7],wr[8],wr[9]];pd=n.defineLocale("fi",{months:"tammikuu_helmikuu_maaliskuu_huhtikuu_toukokuu_kesäkuu_heinäkuu_elokuu_syyskuu_lokakuu_marraskuu_joulukuu".split("_"),monthsShort:"tammi_helmi_maalis_huhti_touko_kesä_heinä_elo_syys_loka_marras_joulu".split("_"),weekdays:"sunnuntai_maanantai_tiistai_keskiviikko_torstai_perjantai_lauantai".split("_"),weekdaysShort:"su_ma_ti_ke_to_pe_la".split("_"),weekdaysMin:"su_ma_ti_ke_to_pe_la".split("_"),longDateFormat:{LT:"HH.mm",LTS:"HH.mm.ss",L:"DD.MM.YYYY",LL:"Do MMMM[ta] YYYY",LLL:"Do MMMM[ta] YYYY, [klo] HH.mm",LLLL:"dddd, Do MMMM[ta] YYYY, [klo] HH.mm",l:"D.M.YYYY",ll:"Do MMM YYYY",lll:"Do MMM YYYY, [klo] HH.mm",llll:"ddd, Do MMM YYYY, [klo] HH.mm"},calendar:{sameDay:"[tänään] [klo] LT",nextDay:"[huomenna] [klo] LT",nextWeek:"dddd [klo] LT",lastDay:"[eilen] [klo] LT",lastWeek:"[viime] dddd[na] [klo] LT",sameElse:"L"},relativeTime:{future:"%s päästä",past:"%s sitten",s:it,m:it,mm:it,h:it,hh:it,d:it,dd:it,M:it,MM:it,y:it,yy:it},ordinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});
//! moment.js locale configuration
//! locale : faroese (fo)
//! author : Ragnar Johannesen : https://github.com/ragnar123
wd=n.defineLocale("fo",{months:"januar_februar_mars_apríl_mai_juni_juli_august_september_oktober_november_desember".split("_"),monthsShort:"jan_feb_mar_apr_mai_jun_jul_aug_sep_okt_nov_des".split("_"),weekdays:"sunnudagur_mánadagur_týsdagur_mikudagur_hósdagur_fríggjadagur_leygardagur".split("_"),weekdaysShort:"sun_mán_týs_mik_hós_frí_ley".split("_"),weekdaysMin:"su_má_tý_mi_hó_fr_le".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D. MMMM, YYYY HH:mm"},calendar:{sameDay:"[Í dag kl.] LT",nextDay:"[Í morgin kl.] LT",nextWeek:"dddd [kl.] LT",lastDay:"[Í gjár kl.] LT",lastWeek:"[síðstu] dddd [kl] LT",sameElse:"L"},relativeTime:{future:"um %s",past:"%s síðani",s:"fá sekund",m:"ein minutt",mm:"%d minuttir",h:"ein tími",hh:"%d tímar",d:"ein dagur",dd:"%d dagar",M:"ein mánaði",MM:"%d mánaðir",y:"eitt ár",yy:"%d ár"},ordinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});
//! moment.js locale configuration
//! locale : canadian french (fr-ca)
//! author : Jonathan Abourbih : https://github.com/jonbca
bd=n.defineLocale("fr-ca",{months:"janvier_février_mars_avril_mai_juin_juillet_août_septembre_octobre_novembre_décembre".split("_"),monthsShort:"janv._févr._mars_avr._mai_juin_juil._août_sept._oct._nov._déc.".split("_"),weekdays:"dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi".split("_"),weekdaysShort:"dim._lun._mar._mer._jeu._ven._sam.".split("_"),weekdaysMin:"Di_Lu_Ma_Me_Je_Ve_Sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY-MM-DD",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[Aujourd'hui à] LT",nextDay:"[Demain à] LT",nextWeek:"dddd [à] LT",lastDay:"[Hier à] LT",lastWeek:"dddd [dernier à] LT",sameElse:"L"},relativeTime:{future:"dans %s",past:"il y a %s",s:"quelques secondes",m:"une minute",mm:"%d minutes",h:"une heure",hh:"%d heures",d:"un jour",dd:"%d jours",M:"un mois",MM:"%d mois",y:"un an",yy:"%d ans"},ordinalParse:/\d{1,2}(er|e)/,ordinal:function(n){return n+(n===1?"er":"e")}});
//! moment.js locale configuration
//! locale : swiss french (fr)
//! author : Gaspard Bucher : https://github.com/gaspard
kd=n.defineLocale("fr-ch",{months:"janvier_février_mars_avril_mai_juin_juillet_août_septembre_octobre_novembre_décembre".split("_"),monthsShort:"janv._févr._mars_avr._mai_juin_juil._août_sept._oct._nov._déc.".split("_"),weekdays:"dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi".split("_"),weekdaysShort:"dim._lun._mar._mer._jeu._ven._sam.".split("_"),weekdaysMin:"Di_Lu_Ma_Me_Je_Ve_Sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[Aujourd'hui à] LT",nextDay:"[Demain à] LT",nextWeek:"dddd [à] LT",lastDay:"[Hier à] LT",lastWeek:"dddd [dernier à] LT",sameElse:"L"},relativeTime:{future:"dans %s",past:"il y a %s",s:"quelques secondes",m:"une minute",mm:"%d minutes",h:"une heure",hh:"%d heures",d:"un jour",dd:"%d jours",M:"un mois",MM:"%d mois",y:"un an",yy:"%d ans"},ordinalParse:/\d{1,2}(er|e)/,ordinal:function(n){return n+(n===1?"er":"e")},week:{dow:1,doy:4}});
//! moment.js locale configuration
//! locale : french (fr)
//! author : John Fischer : https://github.com/jfroffice
dd=n.defineLocale("fr",{months:"janvier_février_mars_avril_mai_juin_juillet_août_septembre_octobre_novembre_décembre".split("_"),monthsShort:"janv._févr._mars_avr._mai_juin_juil._août_sept._oct._nov._déc.".split("_"),weekdays:"dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi".split("_"),weekdaysShort:"dim._lun._mar._mer._jeu._ven._sam.".split("_"),weekdaysMin:"Di_Lu_Ma_Me_Je_Ve_Sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[Aujourd'hui à] LT",nextDay:"[Demain à] LT",nextWeek:"dddd [à] LT",lastDay:"[Hier à] LT",lastWeek:"dddd [dernier à] LT",sameElse:"L"},relativeTime:{future:"dans %s",past:"il y a %s",s:"quelques secondes",m:"une minute",mm:"%d minutes",h:"une heure",hh:"%d heures",d:"un jour",dd:"%d jours",M:"un mois",MM:"%d mois",y:"un an",yy:"%d ans"},ordinalParse:/\d{1,2}(er|)/,ordinal:function(n){return n+(n===1?"er":"")},week:{dow:1,doy:4}});
//! moment.js locale configuration
//! locale : frisian (fy)
//! author : Robin van der Vliet : https://github.com/robin0van0der0v
var gd="jan._feb._mrt._apr._mai_jun._jul._aug._sep._okt._nov._des.".split("_"),ng="jan_feb_mrt_apr_mai_jun_jul_aug_sep_okt_nov_des".split("_"),dit=n.defineLocale("fy",{months:"jannewaris_febrewaris_maart_april_maaie_juny_july_augustus_septimber_oktober_novimber_desimber".split("_"),monthsShort:function(n,t){return/-MMM-/.test(t)?ng[n.month()]:gd[n.month()]},weekdays:"snein_moandei_tiisdei_woansdei_tongersdei_freed_sneon".split("_"),weekdaysShort:"si._mo._ti._wo._to._fr._so.".split("_"),weekdaysMin:"Si_Mo_Ti_Wo_To_Fr_So".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD-MM-YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[hjoed om] LT",nextDay:"[moarn om] LT",nextWeek:"dddd [om] LT",lastDay:"[juster om] LT",lastWeek:"[ôfrûne] dddd [om] LT",sameElse:"L"},relativeTime:{future:"oer %s",past:"%s lyn",s:"in pear sekonden",m:"ien minút",mm:"%d minuten",h:"ien oere",hh:"%d oeren",d:"ien dei",dd:"%d dagen",M:"ien moanne",MM:"%d moannen",y:"ien jier",yy:"%d jierren"},ordinalParse:/\d{1,2}(ste|de)/,ordinal:function(n){return n+(n===1||n===8||n>=20?"ste":"de")},week:{dow:1,doy:4}});
//! moment.js locale configuration
//! locale : great britain scottish gealic (gd)
//! author : Jon Ashdown : https://github.com/jonashdown
var git=n.defineLocale("gd",{months:["Am Faoilleach","An Gearran","Am Màrt","An Giblean","An Cèitean","An t-Ògmhios","An t-Iuchar","An Lùnastal","An t-Sultain","An Dàmhair","An t-Samhain","An Dùbhlachd"],monthsShort:["Faoi","Gear","Màrt","Gibl","Cèit","Ògmh","Iuch","Lùn","Sult","Dàmh","Samh","Dùbh"],monthsParseExact:!0,weekdays:["Didòmhnaich","Diluain","Dimàirt","Diciadain","Diardaoin","Dihaoine","Disathairne"],weekdaysShort:["Did","Dil","Dim","Dic","Dia","Dih","Dis"],weekdaysMin:["Dò","Lu","Mà","Ci","Ar","Ha","Sa"],longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[An-diugh aig] LT",nextDay:"[A-màireach aig] LT",nextWeek:"dddd [aig] LT",lastDay:"[An-dè aig] LT",lastWeek:"dddd [seo chaidh] [aig] LT",sameElse:"L"},relativeTime:{future:"ann an %s",past:"bho chionn %s",s:"beagan diogan",m:"mionaid",mm:"%d mionaidean",h:"uair",hh:"%d uairean",d:"latha",dd:"%d latha",M:"mìos",MM:"%d mìosan",y:"bliadhna",yy:"%d bliadhna"},ordinalParse:/\d{1,2}(d|na|mh)/,ordinal:function(n){var t=n===1?"d":n%10==2?"na":"mh";return n+t},week:{dow:1,doy:4}});
//! moment.js locale configuration
//! locale : galician (gl)
//! author : Juan G. Hurtado : https://github.com/juanghurtado
tg=n.defineLocale("gl",{months:"Xaneiro_Febreiro_Marzo_Abril_Maio_Xuño_Xullo_Agosto_Setembro_Outubro_Novembro_Decembro".split("_"),monthsShort:"Xan._Feb._Mar._Abr._Mai._Xuñ._Xul._Ago._Set._Out._Nov._Dec.".split("_"),weekdays:"Domingo_Luns_Martes_Mércores_Xoves_Venres_Sábado".split("_"),weekdaysShort:"Dom._Lun._Mar._Mér._Xov._Ven._Sáb.".split("_"),weekdaysMin:"Do_Lu_Ma_Mé_Xo_Ve_Sá".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY H:mm",LLLL:"dddd D MMMM YYYY H:mm"},calendar:{sameDay:function(){return"[hoxe "+(this.hours()!==1?"ás":"á")+"] LT"},nextDay:function(){return"[mañá "+(this.hours()!==1?"ás":"á")+"] LT"},nextWeek:function(){return"dddd ["+(this.hours()!==1?"ás":"a")+"] LT"},lastDay:function(){return"[onte "+(this.hours()!==1?"á":"a")+"] LT"},lastWeek:function(){return"[o] dddd [pasado "+(this.hours()!==1?"ás":"a")+"] LT"},sameElse:"L"},relativeTime:{future:function(n){return n==="uns segundos"?"nuns segundos":"en "+n},past:"hai %s",s:"uns segundos",m:"un minuto",mm:"%d minutos",h:"unha hora",hh:"%d horas",d:"un día",dd:"%d días",M:"un mes",MM:"%d meses",y:"un ano",yy:"%d anos"},ordinalParse:/\d{1,2}º/,ordinal:"%dº",week:{dow:1,doy:7}});
//! moment.js locale configuration
//! locale : Hebrew (he)
//! author : Tomer Cohen : https://github.com/tomer
//! author : Moshe Simantov : https://github.com/DevelopmentIL
//! author : Tal Ater : https://github.com/TalAter
ig=n.defineLocale("he",{months:"ינואר_פברואר_מרץ_אפריל_מאי_יוני_יולי_אוגוסט_ספטמבר_אוקטובר_נובמבר_דצמבר".split("_"),monthsShort:"ינו׳_פבר׳_מרץ_אפר׳_מאי_יוני_יולי_אוג׳_ספט׳_אוק׳_נוב׳_דצמ׳".split("_"),weekdays:"ראשון_שני_שלישי_רביעי_חמישי_שישי_שבת".split("_"),weekdaysShort:"א׳_ב׳_ג׳_ד׳_ה׳_ו׳_ש׳".split("_"),weekdaysMin:"א_ב_ג_ד_ה_ו_ש".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D [ב]MMMM YYYY",LLL:"D [ב]MMMM YYYY HH:mm",LLLL:"dddd, D [ב]MMMM YYYY HH:mm",l:"D/M/YYYY",ll:"D MMM YYYY",lll:"D MMM YYYY HH:mm",llll:"ddd, D MMM YYYY HH:mm"},calendar:{sameDay:"[היום ב־]LT",nextDay:"[מחר ב־]LT",nextWeek:"dddd [בשעה] LT",lastDay:"[אתמול ב־]LT",lastWeek:"[ביום] dddd [האחרון בשעה] LT",sameElse:"L"},relativeTime:{future:"בעוד %s",past:"לפני %s",s:"מספר שניות",m:"דקה",mm:"%d דקות",h:"שעה",hh:function(n){return n===2?"שעתיים":n+" שעות"},d:"יום",dd:function(n){return n===2?"יומיים":n+" ימים"},M:"חודש",MM:function(n){return n===2?"חודשיים":n+" חודשים"},y:"שנה",yy:function(n){return n===2?"שנתיים":n%10==0&&n!==10?n+" שנה":n+" שנים"}}});
//! moment.js locale configuration
//! locale : hindi (hi)
//! author : Mayank Singhal : https://github.com/mayanksinghal
var rg={"1":"१","2":"२","3":"३","4":"४","5":"५","6":"६","7":"७","8":"८","9":"९","0":"०"},ug={"१":"1","२":"2","३":"3","४":"4","५":"5","६":"6","७":"7","८":"8","९":"9","०":"0"},nrt=n.defineLocale("hi",{months:"जनवरी_फ़रवरी_मार्च_अप्रैल_मई_जून_जुलाई_अगस्त_सितम्बर_अक्टूबर_नवम्बर_दिसम्बर".split("_"),monthsShort:"जन._फ़र._मार्च_अप्रै._मई_जून_जुल._अग._सित._अक्टू._नव._दिस.".split("_"),weekdays:"रविवार_सोमवार_मंगलवार_बुधवार_गुरूवार_शुक्रवार_शनिवार".split("_"),weekdaysShort:"रवि_सोम_मंगल_बुध_गुरू_शुक्र_शनि".split("_"),weekdaysMin:"र_सो_मं_बु_गु_शु_श".split("_"),longDateFormat:{LT:"A h:mm बजे",LTS:"A h:mm:ss बजे",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm बजे",LLLL:"dddd, D MMMM YYYY, A h:mm बजे"},calendar:{sameDay:"[आज] LT",nextDay:"[कल] LT",nextWeek:"dddd, LT",lastDay:"[कल] LT",lastWeek:"[पिछले] dddd, LT",sameElse:"L"},relativeTime:{future:"%s में",past:"%s पहले",s:"कुछ ही क्षण",m:"एक मिनट",mm:"%d मिनट",h:"एक घंटा",hh:"%d घंटे",d:"एक दिन",dd:"%d दिन",M:"एक महीने",MM:"%d महीने",y:"एक वर्ष",yy:"%d वर्ष"},preparse:function(n){return n.replace(/[१२३४५६७८९०]/g,function(n){return ug[n]})},postformat:function(n){return n.replace(/\d/g,function(n){return rg[n]})},meridiemParse:/रात|सुबह|दोपहर|शाम/,meridiemHour:function(n,t){return(n===12&&(n=0),t==="रात")?n<4?n:n+12:t==="सुबह"?n:t==="दोपहर"?n>=10?n:n+12:t==="शाम"?n+12:void 0},meridiem:function(n){return n<4?"रात":n<10?"सुबह":n<17?"दोपहर":n<20?"शाम":"रात"},week:{dow:0,doy:6}});
//! moment.js locale configuration
//! locale : hrvatski (hr)
//! author : Bojan Marković : https://github.com/bmarkovic
fg=n.defineLocale("hr",{months:{format:"siječnja_veljače_ožujka_travnja_svibnja_lipnja_srpnja_kolovoza_rujna_listopada_studenoga_prosinca".split("_"),standalone:"siječanj_veljača_ožujak_travanj_svibanj_lipanj_srpanj_kolovoz_rujan_listopad_studeni_prosinac".split("_")},monthsShort:"sij._velj._ožu._tra._svi._lip._srp._kol._ruj._lis._stu._pro.".split("_"),weekdays:"nedjelja_ponedjeljak_utorak_srijeda_četvrtak_petak_subota".split("_"),weekdaysShort:"ned._pon._uto._sri._čet._pet._sub.".split("_"),weekdaysMin:"ne_po_ut_sr_če_pe_su".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD. MM. YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd, D. MMMM YYYY H:mm"},calendar:{sameDay:"[danas u] LT",nextDay:"[sutra u] LT",nextWeek:function(){switch(this.day()){case 0:return"[u] [nedjelju] [u] LT";case 3:return"[u] [srijedu] [u] LT";case 6:return"[u] [subotu] [u] LT";case 1:case 2:case 4:case 5:return"[u] dddd [u] LT"}},lastDay:"[jučer u] LT",lastWeek:function(){switch(this.day()){case 0:case 3:return"[prošlu] dddd [u] LT";case 6:return"[prošle] [subote] [u] LT";case 1:case 2:case 4:case 5:return"[prošli] dddd [u] LT"}},sameElse:"L"},relativeTime:{future:"za %s",past:"prije %s",s:"par sekundi",m:bi,mm:bi,h:bi,hh:bi,d:"dan",dd:bi,M:"mjesec",MM:bi,y:"godinu",yy:bi},ordinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:7}});
//! moment.js locale configuration
//! locale : hungarian (hu)
//! author : Adam Brunner : https://github.com/adambrunner
vc="vasárnap hétfőn kedden szerdán csütörtökön pénteken szombaton".split(" ");eg=n.defineLocale("hu",{months:"január_február_március_április_május_június_július_augusztus_szeptember_október_november_december".split("_"),monthsShort:"jan_feb_márc_ápr_máj_jún_júl_aug_szept_okt_nov_dec".split("_"),weekdays:"vasárnap_hétfő_kedd_szerda_csütörtök_péntek_szombat".split("_"),weekdaysShort:"vas_hét_kedd_sze_csüt_pén_szo".split("_"),weekdaysMin:"v_h_k_sze_cs_p_szo".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"YYYY.MM.DD.",LL:"YYYY. MMMM D.",LLL:"YYYY. MMMM D. H:mm",LLLL:"YYYY. MMMM D., dddd H:mm"},meridiemParse:/de|du/i,isPM:function(n){return n.charAt(1).toLowerCase()==="u"},meridiem:function(n,t,i){return n<12?i===!0?"de":"DE":i===!0?"du":"DU"},calendar:{sameDay:"[ma] LT[-kor]",nextDay:"[holnap] LT[-kor]",nextWeek:function(){return yc.call(this,!0)},lastDay:"[tegnap] LT[-kor]",lastWeek:function(){return yc.call(this,!1)},sameElse:"L"},relativeTime:{future:"%s múlva",past:"%s",s:rt,m:rt,mm:rt,h:rt,hh:rt,d:rt,dd:rt,M:rt,MM:rt,y:rt,yy:rt},ordinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:7}});
//! moment.js locale configuration
//! locale : Armenian (hy-am)
//! author : Armendarabyan : https://github.com/armendarabyan
og=n.defineLocale("hy-am",{months:{format:"հունվարի_փետրվարի_մարտի_ապրիլի_մայիսի_հունիսի_հուլիսի_օգոստոսի_սեպտեմբերի_հոկտեմբերի_նոյեմբերի_դեկտեմբերի".split("_"),standalone:"հունվար_փետրվար_մարտ_ապրիլ_մայիս_հունիս_հուլիս_օգոստոս_սեպտեմբեր_հոկտեմբեր_նոյեմբեր_դեկտեմբեր".split("_")},monthsShort:"հնվ_փտր_մրտ_ապր_մյս_հնս_հլս_օգս_սպտ_հկտ_նմբ_դկտ".split("_"),weekdays:"կիրակի_երկուշաբթի_երեքշաբթի_չորեքշաբթի_հինգշաբթի_ուրբաթ_շաբաթ".split("_"),weekdaysShort:"կրկ_երկ_երք_չրք_հնգ_ուրբ_շբթ".split("_"),weekdaysMin:"կրկ_երկ_երք_չրք_հնգ_ուրբ_շբթ".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY թ.",LLL:"D MMMM YYYY թ., HH:mm",LLLL:"dddd, D MMMM YYYY թ., HH:mm"},calendar:{sameDay:"[այսօր] LT",nextDay:"[վաղը] LT",lastDay:"[երեկ] LT",nextWeek:function(){return"dddd [օրը ժամը] LT"},lastWeek:function(){return"[անցած] dddd [օրը ժամը] LT"},sameElse:"L"},relativeTime:{future:"%s հետո",past:"%s առաջ",s:"մի քանի վայրկյան",m:"րոպե",mm:"%d րոպե",h:"ժամ",hh:"%d ժամ",d:"օր",dd:"%d օր",M:"ամիս",MM:"%d ամիս",y:"տարի",yy:"%d տարի"},meridiemParse:/գիշերվա|առավոտվա|ցերեկվա|երեկոյան/,isPM:function(n){return/^(ցերեկվա|երեկոյան)$/.test(n)},meridiem:function(n){return n<4?"գիշերվա":n<12?"առավոտվա":n<17?"ցերեկվա":"երեկոյան"},ordinalParse:/\d{1,2}|\d{1,2}-(ին|րդ)/,ordinal:function(n,t){switch(t){case"DDD":case"w":case"W":case"DDDo":return n===1?n+"-ին":n+"-րդ";default:return n}},week:{dow:1,doy:7}});
//! moment.js locale configuration
//! locale : Bahasa Indonesia (id)
//! author : Mohammad Satrio Utomo : https://github.com/tyok
//! reference: http://id.wikisource.org/wiki/Pedoman_Umum_Ejaan_Bahasa_Indonesia_yang_Disempurnakan
sg=n.defineLocale("id",{months:"Januari_Februari_Maret_April_Mei_Juni_Juli_Agustus_September_Oktober_November_Desember".split("_"),monthsShort:"Jan_Feb_Mar_Apr_Mei_Jun_Jul_Ags_Sep_Okt_Nov_Des".split("_"),weekdays:"Minggu_Senin_Selasa_Rabu_Kamis_Jumat_Sabtu".split("_"),weekdaysShort:"Min_Sen_Sel_Rab_Kam_Jum_Sab".split("_"),weekdaysMin:"Mg_Sn_Sl_Rb_Km_Jm_Sb".split("_"),longDateFormat:{LT:"HH.mm",LTS:"HH.mm.ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY [pukul] HH.mm",LLLL:"dddd, D MMMM YYYY [pukul] HH.mm"},meridiemParse:/pagi|siang|sore|malam/,meridiemHour:function(n,t){return(n===12&&(n=0),t==="pagi")?n:t==="siang"?n>=11?n:n+12:t==="sore"||t==="malam"?n+12:void 0},meridiem:function(n){return n<11?"pagi":n<15?"siang":n<19?"sore":"malam"},calendar:{sameDay:"[Hari ini pukul] LT",nextDay:"[Besok pukul] LT",nextWeek:"dddd [pukul] LT",lastDay:"[Kemarin pukul] LT",lastWeek:"dddd [lalu pukul] LT",sameElse:"L"},relativeTime:{future:"dalam %s",past:"%s yang lalu",s:"beberapa detik",m:"semenit",mm:"%d menit",h:"sejam",hh:"%d jam",d:"sehari",dd:"%d hari",M:"sebulan",MM:"%d bulan",y:"setahun",yy:"%d tahun"},week:{dow:1,doy:7}});
//! moment.js locale configuration
//! locale : icelandic (is)
//! author : Hinrik Örn Sigurðsson : https://github.com/hinrik
hg=n.defineLocale("is",{months:"janúar_febrúar_mars_apríl_maí_júní_júlí_ágúst_september_október_nóvember_desember".split("_"),monthsShort:"jan_feb_mar_apr_maí_jún_júl_ágú_sep_okt_nóv_des".split("_"),weekdays:"sunnudagur_mánudagur_þriðjudagur_miðvikudagur_fimmtudagur_föstudagur_laugardagur".split("_"),weekdaysShort:"sun_mán_þri_mið_fim_fös_lau".split("_"),weekdaysMin:"Su_Má_Þr_Mi_Fi_Fö_La".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD/MM/YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY [kl.] H:mm",LLLL:"dddd, D. MMMM YYYY [kl.] H:mm"},calendar:{sameDay:"[í dag kl.] LT",nextDay:"[á morgun kl.] LT",nextWeek:"dddd [kl.] LT",lastDay:"[í gær kl.] LT",lastWeek:"[síðasta] dddd [kl.] LT",sameElse:"L"},relativeTime:{future:"eftir %s",past:"fyrir %s síðan",s:lt,m:lt,mm:lt,h:"klukkustund",hh:lt,d:lt,dd:lt,M:lt,MM:lt,y:lt,yy:lt},ordinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});
//! moment.js locale configuration
//! locale : italian (it)
//! author : Lorenzo : https://github.com/aliem
//! author: Mattia Larentis: https://github.com/nostalgiaz
cg=n.defineLocale("it",{months:"gennaio_febbraio_marzo_aprile_maggio_giugno_luglio_agosto_settembre_ottobre_novembre_dicembre".split("_"),monthsShort:"gen_feb_mar_apr_mag_giu_lug_ago_set_ott_nov_dic".split("_"),weekdays:"Domenica_Lunedì_Martedì_Mercoledì_Giovedì_Venerdì_Sabato".split("_"),weekdaysShort:"Dom_Lun_Mar_Mer_Gio_Ven_Sab".split("_"),weekdaysMin:"Do_Lu_Ma_Me_Gi_Ve_Sa".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Oggi alle] LT",nextDay:"[Domani alle] LT",nextWeek:"dddd [alle] LT",lastDay:"[Ieri alle] LT",lastWeek:function(){switch(this.day()){case 0:return"[la scorsa] dddd [alle] LT";default:return"[lo scorso] dddd [alle] LT"}},sameElse:"L"},relativeTime:{future:function(n){return(/^[0-9].+$/.test(n)?"tra":"in")+" "+n},past:"%s fa",s:"alcuni secondi",m:"un minuto",mm:"%d minuti",h:"un'ora",hh:"%d ore",d:"un giorno",dd:"%d giorni",M:"un mese",MM:"%d mesi",y:"un anno",yy:"%d anni"},ordinalParse:/\d{1,2}º/,ordinal:"%dº",week:{dow:1,doy:4}});
//! moment.js locale configuration
//! locale : japanese (ja)
//! author : LI Long : https://github.com/baryon
lg=n.defineLocale("ja",{months:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),weekdays:"日曜日_月曜日_火曜日_水曜日_木曜日_金曜日_土曜日".split("_"),weekdaysShort:"日_月_火_水_木_金_土".split("_"),weekdaysMin:"日_月_火_水_木_金_土".split("_"),longDateFormat:{LT:"Ah時m分",LTS:"Ah時m分s秒",L:"YYYY/MM/DD",LL:"YYYY年M月D日",LLL:"YYYY年M月D日Ah時m分",LLLL:"YYYY年M月D日Ah時m分 dddd"},meridiemParse:/午前|午後/i,isPM:function(n){return n==="午後"},meridiem:function(n){return n<12?"午前":"午後"},calendar:{sameDay:"[今日] LT",nextDay:"[明日] LT",nextWeek:"[来週]dddd LT",lastDay:"[昨日] LT",lastWeek:"[前週]dddd LT",sameElse:"L"},relativeTime:{future:"%s後",past:"%s前",s:"数秒",m:"1分",mm:"%d分",h:"1時間",hh:"%d時間",d:"1日",dd:"%d日",M:"1ヶ月",MM:"%dヶ月",y:"1年",yy:"%d年"}});
//! moment.js locale configuration
//! locale : Boso Jowo (jv)
//! author : Rony Lantip : https://github.com/lantip
//! reference: http://jv.wikipedia.org/wiki/Basa_Jawa
ag=n.defineLocale("jv",{months:"Januari_Februari_Maret_April_Mei_Juni_Juli_Agustus_September_Oktober_Nopember_Desember".split("_"),monthsShort:"Jan_Feb_Mar_Apr_Mei_Jun_Jul_Ags_Sep_Okt_Nop_Des".split("_"),weekdays:"Minggu_Senen_Seloso_Rebu_Kemis_Jemuwah_Septu".split("_"),weekdaysShort:"Min_Sen_Sel_Reb_Kem_Jem_Sep".split("_"),weekdaysMin:"Mg_Sn_Sl_Rb_Km_Jm_Sp".split("_"),longDateFormat:{LT:"HH.mm",LTS:"HH.mm.ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY [pukul] HH.mm",LLLL:"dddd, D MMMM YYYY [pukul] HH.mm"},meridiemParse:/enjing|siyang|sonten|ndalu/,meridiemHour:function(n,t){return(n===12&&(n=0),t==="enjing")?n:t==="siyang"?n>=11?n:n+12:t==="sonten"||t==="ndalu"?n+12:void 0},meridiem:function(n){return n<11?"enjing":n<15?"siyang":n<19?"sonten":"ndalu"},calendar:{sameDay:"[Dinten puniko pukul] LT",nextDay:"[Mbenjang pukul] LT",nextWeek:"dddd [pukul] LT",lastDay:"[Kala wingi pukul] LT",lastWeek:"dddd [kepengker pukul] LT",sameElse:"L"},relativeTime:{future:"wonten ing %s",past:"%s ingkang kepengker",s:"sawetawis detik",m:"setunggal menit",mm:"%d menit",h:"setunggal jam",hh:"%d jam",d:"sedinten",dd:"%d dinten",M:"sewulan",MM:"%d wulan",y:"setaun",yy:"%d taun"},week:{dow:1,doy:7}});
//! moment.js locale configuration
//! locale : Georgian (ka)
//! author : Irakli Janiashvili : https://github.com/irakli-janiashvili
vg=n.defineLocale("ka",{months:{standalone:"იანვარი_თებერვალი_მარტი_აპრილი_მაისი_ივნისი_ივლისი_აგვისტო_სექტემბერი_ოქტომბერი_ნოემბერი_დეკემბერი".split("_"),format:"იანვარს_თებერვალს_მარტს_აპრილის_მაისს_ივნისს_ივლისს_აგვისტს_სექტემბერს_ოქტომბერს_ნოემბერს_დეკემბერს".split("_")},monthsShort:"იან_თებ_მარ_აპრ_მაი_ივნ_ივლ_აგვ_სექ_ოქტ_ნოე_დეკ".split("_"),weekdays:{standalone:"კვირა_ორშაბათი_სამშაბათი_ოთხშაბათი_ხუთშაბათი_პარასკევი_შაბათი".split("_"),format:"კვირას_ორშაბათს_სამშაბათს_ოთხშაბათს_ხუთშაბათს_პარასკევს_შაბათს".split("_"),isFormat:/(წინა|შემდეგ)/},weekdaysShort:"კვი_ორშ_სამ_ოთხ_ხუთ_პარ_შაბ".split("_"),weekdaysMin:"კვ_ორ_სა_ოთ_ხუ_პა_შა".split("_"),longDateFormat:{LT:"h:mm A",LTS:"h:mm:ss A",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY h:mm A",LLLL:"dddd, D MMMM YYYY h:mm A"},calendar:{sameDay:"[დღეს] LT[-ზე]",nextDay:"[ხვალ] LT[-ზე]",lastDay:"[გუშინ] LT[-ზე]",nextWeek:"[შემდეგ] dddd LT[-ზე]",lastWeek:"[წინა] dddd LT-ზე",sameElse:"L"},relativeTime:{future:function(n){return/(წამი|წუთი|საათი|წელი)/.test(n)?n.replace(/ი$/,"ში"):n+"ში"},past:function(n){return/(წამი|წუთი|საათი|დღე|თვე)/.test(n)?n.replace(/(ი|ე)$/,"ის წინ"):/წელი/.test(n)?n.replace(/წელი$/,"წლის წინ"):void 0},s:"რამდენიმე წამი",m:"წუთი",mm:"%d წუთი",h:"საათი",hh:"%d საათი",d:"დღე",dd:"%d დღე",M:"თვე",MM:"%d თვე",y:"წელი",yy:"%d წელი"},ordinalParse:/0|1-ლი|მე-\d{1,2}|\d{1,2}-ე/,ordinal:function(n){return n===0?n:n===1?n+"-ლი":n<20||n<=100&&n%20==0||n%100==0?"მე-"+n:n+"-ე"},week:{dow:1,doy:7}});
//! moment.js locale configuration
//! locale : kazakh (kk)
//! authors : Nurlan Rakhimzhanov : https://github.com/nurlan
ef={0:"-ші",1:"-ші",2:"-ші",3:"-ші",4:"-ші",5:"-ші",6:"-шы",7:"-ші",8:"-ші",9:"-шы",10:"-шы",20:"-шы",30:"-шы",40:"-шы",50:"-ші",60:"-шы",70:"-ші",80:"-ші",90:"-шы",100:"-ші"};yg=n.defineLocale("kk",{months:"Қаңтар_Ақпан_Наурыз_Сәуір_Мамыр_Маусым_Шілде_Тамыз_Қыркүйек_Қазан_Қараша_Желтоқсан".split("_"),monthsShort:"Қаң_Ақп_Нау_Сәу_Мам_Мау_Шіл_Там_Қыр_Қаз_Қар_Жел".split("_"),weekdays:"Жексенбі_Дүйсенбі_Сейсенбі_Сәрсенбі_Бейсенбі_Жұма_Сенбі".split("_"),weekdaysShort:"Жек_Дүй_Сей_Сәр_Бей_Жұм_Сен".split("_"),weekdaysMin:"Жк_Дй_Сй_Ср_Бй_Жм_Сн".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Бүгін сағат] LT",nextDay:"[Ертең сағат] LT",nextWeek:"dddd [сағат] LT",lastDay:"[Кеше сағат] LT",lastWeek:"[Өткен аптаның] dddd [сағат] LT",sameElse:"L"},relativeTime:{future:"%s ішінде",past:"%s бұрын",s:"бірнеше секунд",m:"бір минут",mm:"%d минут",h:"бір сағат",hh:"%d сағат",d:"бір күн",dd:"%d күн",M:"бір ай",MM:"%d ай",y:"бір жыл",yy:"%d жыл"},ordinalParse:/\d{1,2}-(ші|шы)/,ordinal:function(n){var t=n%10,i=n>=100?100:null;return n+(ef[n]||ef[t]||ef[i])},week:{dow:1,doy:7}});
//! moment.js locale configuration
//! locale : khmer (km)
//! author : Kruy Vanna : https://github.com/kruyvanna
pg=n.defineLocale("km",{months:"មករា_កុម្ភៈ_មិនា_មេសា_ឧសភា_មិថុនា_កក្កដា_សីហា_កញ្ញា_តុលា_វិច្ឆិកា_ធ្នូ".split("_"),monthsShort:"មករា_កុម្ភៈ_មិនា_មេសា_ឧសភា_មិថុនា_កក្កដា_សីហា_កញ្ញា_តុលា_វិច្ឆិកា_ធ្នូ".split("_"),weekdays:"អាទិត្យ_ច័ន្ទ_អង្គារ_ពុធ_ព្រហស្បតិ៍_សុក្រ_សៅរ៍".split("_"),weekdaysShort:"អាទិត្យ_ច័ន្ទ_អង្គារ_ពុធ_ព្រហស្បតិ៍_សុក្រ_សៅរ៍".split("_"),weekdaysMin:"អាទិត្យ_ច័ន្ទ_អង្គារ_ពុធ_ព្រហស្បតិ៍_សុក្រ_សៅរ៍".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[ថ្ងៃនេះ ម៉ោង] LT",nextDay:"[ស្អែក ម៉ោង] LT",nextWeek:"dddd [ម៉ោង] LT",lastDay:"[ម្សិលមិញ ម៉ោង] LT",lastWeek:"dddd [សប្តាហ៍មុន] [ម៉ោង] LT",sameElse:"L"},relativeTime:{future:"%sទៀត",past:"%sមុន",s:"ប៉ុន្មានវិនាទី",m:"មួយនាទី",mm:"%d នាទី",h:"មួយម៉ោង",hh:"%d ម៉ោង",d:"មួយថ្ងៃ",dd:"%d ថ្ងៃ",M:"មួយខែ",MM:"%d ខែ",y:"មួយឆ្នាំ",yy:"%d ឆ្នាំ"},week:{dow:1,doy:4}});
//! moment.js locale configuration
//! locale : korean (ko)
//!
//! authors
//!
//! - Kyungwook, Park : https://github.com/kyungw00k
//! - Jeeeyul Lee <<EMAIL>>
wg=n.defineLocale("ko",{months:"1월_2월_3월_4월_5월_6월_7월_8월_9월_10월_11월_12월".split("_"),monthsShort:"1월_2월_3월_4월_5월_6월_7월_8월_9월_10월_11월_12월".split("_"),weekdays:"일요일_월요일_화요일_수요일_목요일_금요일_토요일".split("_"),weekdaysShort:"일_월_화_수_목_금_토".split("_"),weekdaysMin:"일_월_화_수_목_금_토".split("_"),longDateFormat:{LT:"A h시 m분",LTS:"A h시 m분 s초",L:"YYYY.MM.DD",LL:"YYYY년 MMMM D일",LLL:"YYYY년 MMMM D일 A h시 m분",LLLL:"YYYY년 MMMM D일 dddd A h시 m분"},calendar:{sameDay:"오늘 LT",nextDay:"내일 LT",nextWeek:"dddd LT",lastDay:"어제 LT",lastWeek:"지난주 dddd LT",sameElse:"L"},relativeTime:{future:"%s 후",past:"%s 전",s:"몇초",ss:"%d초",m:"일분",mm:"%d분",h:"한시간",hh:"%d시간",d:"하루",dd:"%d일",M:"한달",MM:"%d달",y:"일년",yy:"%d년"},ordinalParse:/\d{1,2}일/,ordinal:"%d일",meridiemParse:/오전|오후/,isPM:function(n){return n==="오후"},meridiem:function(n){return n<12?"오전":"오후"}});
//! moment.js locale configuration
//! locale : Luxembourgish (lb)
//! author : mweimerskirch : https://github.com/mweimerskirch, David Raison : https://github.com/kwisatz
dg=n.defineLocale("lb",{months:"Januar_Februar_Mäerz_Abrëll_Mee_Juni_Juli_August_September_Oktober_November_Dezember".split("_"),monthsShort:"Jan._Febr._Mrz._Abr._Mee_Jun._Jul._Aug._Sept._Okt._Nov._Dez.".split("_"),weekdays:"Sonndeg_Méindeg_Dënschdeg_Mëttwoch_Donneschdeg_Freideg_Samschdeg".split("_"),weekdaysShort:"So._Mé._Dë._Më._Do._Fr._Sa.".split("_"),weekdaysMin:"So_Mé_Dë_Më_Do_Fr_Sa".split("_"),longDateFormat:{LT:"H:mm [Auer]",LTS:"H:mm:ss [Auer]",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm [Auer]",LLLL:"dddd, D. MMMM YYYY H:mm [Auer]"},calendar:{sameDay:"[Haut um] LT",sameElse:"L",nextDay:"[Muer um] LT",nextWeek:"dddd [um] LT",lastDay:"[Gëschter um] LT",lastWeek:function(){switch(this.day()){case 2:case 4:return"[Leschten] dddd [um] LT";default:return"[Leschte] dddd [um] LT"}}},relativeTime:{future:bg,past:kg,s:"e puer Sekonnen",m:kr,mm:"%d Minutten",h:kr,hh:"%d Stonnen",d:kr,dd:"%d Deeg",M:kr,MM:"%d Méint",y:kr,yy:"%d Joer"},ordinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});
//! moment.js locale configuration
//! locale : lao (lo)
//! author : Ryan Hart : https://github.com/ryanhart2
gg=n.defineLocale("lo",{months:"ມັງກອນ_ກຸມພາ_ມີນາ_ເມສາ_ພຶດສະພາ_ມິຖຸນາ_ກໍລະກົດ_ສິງຫາ_ກັນຍາ_ຕຸລາ_ພະຈິກ_ທັນວາ".split("_"),monthsShort:"ມັງກອນ_ກຸມພາ_ມີນາ_ເມສາ_ພຶດສະພາ_ມິຖຸນາ_ກໍລະກົດ_ສິງຫາ_ກັນຍາ_ຕຸລາ_ພະຈິກ_ທັນວາ".split("_"),weekdays:"ອາທິດ_ຈັນ_ອັງຄານ_ພຸດ_ພະຫັດ_ສຸກ_ເສົາ".split("_"),weekdaysShort:"ທິດ_ຈັນ_ອັງຄານ_ພຸດ_ພະຫັດ_ສຸກ_ເສົາ".split("_"),weekdaysMin:"ທ_ຈ_ອຄ_ພ_ພຫ_ສກ_ສ".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"ວັນdddd D MMMM YYYY HH:mm"},meridiemParse:/ຕອນເຊົ້າ|ຕອນແລງ/,isPM:function(n){return n==="ຕອນແລງ"},meridiem:function(n){return n<12?"ຕອນເຊົ້າ":"ຕອນແລງ"},calendar:{sameDay:"[ມື້ນີ້ເວລາ] LT",nextDay:"[ມື້ອື່ນເວລາ] LT",nextWeek:"[ວັນ]dddd[ໜ້າເວລາ] LT",lastDay:"[ມື້ວານນີ້ເວລາ] LT",lastWeek:"[ວັນ]dddd[ແລ້ວນີ້ເວລາ] LT",sameElse:"L"},relativeTime:{future:"ອີກ %s",past:"%sຜ່ານມາ",s:"ບໍ່ເທົ່າໃດວິນາທີ",m:"1 ນາທີ",mm:"%d ນາທີ",h:"1 ຊົ່ວໂມງ",hh:"%d ຊົ່ວໂມງ",d:"1 ມື້",dd:"%d ມື້",M:"1 ເດືອນ",MM:"%d ເດືອນ",y:"1 ປີ",yy:"%d ປີ"},ordinalParse:/(ທີ່)\d{1,2}/,ordinal:function(n){return"ທີ່"+n}});
//! moment.js locale configuration
//! locale : Lithuanian (lt)
//! author : Mindaugas Mozūras : https://github.com/mmozuras
pc={m:"minutė_minutės_minutę",mm:"minutės_minučių_minutes",h:"valanda_valandos_valandą",hh:"valandos_valandų_valandas",d:"diena_dienos_dieną",dd:"dienos_dienų_dienas",M:"mėnuo_mėnesio_mėnesį",MM:"mėnesiai_mėnesių_mėnesius",y:"metai_metų_metus",yy:"metai_metų_metus"};tn=n.defineLocale("lt",{months:{format:"sausio_vasario_kovo_balandžio_gegužės_birželio_liepos_rugpjūčio_rugsėjo_spalio_lapkričio_gruodžio".split("_"),standalone:"sausis_vasaris_kovas_balandis_gegužė_birželis_liepa_rugpjūtis_rugsėjis_spalis_lapkritis_gruodis".split("_")},monthsShort:"sau_vas_kov_bal_geg_bir_lie_rgp_rgs_spa_lap_grd".split("_"),weekdays:{format:"sekmadienį_pirmadienį_antradienį_trečiadienį_ketvirtadienį_penktadienį_šeštadienį".split("_"),standalone:"sekmadienis_pirmadienis_antradienis_trečiadienis_ketvirtadienis_penktadienis_šeštadienis".split("_"),isFormat:/dddd HH:mm/},weekdaysShort:"Sek_Pir_Ant_Tre_Ket_Pen_Šeš".split("_"),weekdaysMin:"S_P_A_T_K_Pn_Š".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY-MM-DD",LL:"YYYY [m.] MMMM D [d.]",LLL:"YYYY [m.] MMMM D [d.], HH:mm [val.]",LLLL:"YYYY [m.] MMMM D [d.], dddd, HH:mm [val.]",l:"YYYY-MM-DD",ll:"YYYY [m.] MMMM D [d.]",lll:"YYYY [m.] MMMM D [d.], HH:mm [val.]",llll:"YYYY [m.] MMMM D [d.], ddd, HH:mm [val.]"},calendar:{sameDay:"[Šiandien] LT",nextDay:"[Rytoj] LT",nextWeek:"dddd LT",lastDay:"[Vakar] LT",lastWeek:"[Praėjusį] dddd LT",sameElse:"L"},relativeTime:{future:"po %s",past:"prieš %s",s:nn,m:ir,mm:dr,h:ir,hh:dr,d:ir,dd:dr,M:ir,MM:dr,y:ir,yy:dr},ordinalParse:/\d{1,2}-oji/,ordinal:function(n){return n+"-oji"},week:{dow:1,doy:4}});
//! moment.js locale configuration
//! locale : latvian (lv)
//! author : Kristaps Karlsons : https://github.com/skakri
//! author : Jānis Elmeris : https://github.com/JanisE
ke={m:"minūtes_minūtēm_minūte_minūtes".split("_"),mm:"minūtes_minūtēm_minūte_minūtes".split("_"),h:"stundas_stundām_stunda_stundas".split("_"),hh:"stundas_stundām_stunda_stundas".split("_"),d:"dienas_dienām_diena_dienas".split("_"),dd:"dienas_dienām_diena_dienas".split("_"),M:"mēneša_mēnešiem_mēnesis_mēneši".split("_"),MM:"mēneša_mēnešiem_mēnesis_mēneši".split("_"),y:"gada_gadiem_gads_gadi".split("_"),yy:"gada_gadiem_gads_gadi".split("_")};un=n.defineLocale("lv",{months:"janvāris_februāris_marts_aprīlis_maijs_jūnijs_jūlijs_augusts_septembris_oktobris_novembris_decembris".split("_"),monthsShort:"jan_feb_mar_apr_mai_jūn_jūl_aug_sep_okt_nov_dec".split("_"),weekdays:"svētdiena_pirmdiena_otrdiena_trešdiena_ceturtdiena_piektdiena_sestdiena".split("_"),weekdaysShort:"Sv_P_O_T_C_Pk_S".split("_"),weekdaysMin:"Sv_P_O_T_C_Pk_S".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY.",LL:"YYYY. [gada] D. MMMM",LLL:"YYYY. [gada] D. MMMM, HH:mm",LLLL:"YYYY. [gada] D. MMMM, dddd, HH:mm"},calendar:{sameDay:"[Šodien pulksten] LT",nextDay:"[Rīt pulksten] LT",nextWeek:"dddd [pulksten] LT",lastDay:"[Vakar pulksten] LT",lastWeek:"[Pagājušā] dddd [pulksten] LT",sameElse:"L"},relativeTime:{future:"pēc %s",past:"pirms %s",s:rn,m:nu,mm:gr,h:nu,hh:gr,d:nu,dd:gr,M:nu,MM:gr,y:nu,yy:gr},ordinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});
//! moment.js locale configuration
//! locale : Montenegrin (me)
//! author : Miodrag Nikač <<EMAIL>> : https://github.com/miodragnikac
at={words:{m:["jedan minut","jednog minuta"],mm:["minut","minuta","minuta"],h:["jedan sat","jednog sata"],hh:["sat","sata","sati"],dd:["dan","dana","dana"],MM:["mjesec","mjeseca","mjeseci"],yy:["godina","godine","godina"]},correctGrammaticalCase:function(n,t){return n===1?t[0]:n>=2&&n<=4?t[1]:t[2]},translate:function(n,t,i){var r=at.words[i];return i.length===1?t?r[0]:r[1]:n+" "+at.correctGrammaticalCase(n,r)}};fn=n.defineLocale("me",{months:["januar","februar","mart","april","maj","jun","jul","avgust","septembar","oktobar","novembar","decembar"],monthsShort:["jan.","feb.","mar.","apr.","maj","jun","jul","avg.","sep.","okt.","nov.","dec."],weekdays:["nedjelja","ponedjeljak","utorak","srijeda","četvrtak","petak","subota"],weekdaysShort:["ned.","pon.","uto.","sri.","čet.","pet.","sub."],weekdaysMin:["ne","po","ut","sr","če","pe","su"],longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD. MM. YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd, D. MMMM YYYY H:mm"},calendar:{sameDay:"[danas u] LT",nextDay:"[sjutra u] LT",nextWeek:function(){switch(this.day()){case 0:return"[u] [nedjelju] [u] LT";case 3:return"[u] [srijedu] [u] LT";case 6:return"[u] [subotu] [u] LT";case 1:case 2:case 4:case 5:return"[u] dddd [u] LT"}},lastDay:"[juče u] LT",lastWeek:function(){return["[prošle] [nedjelje] [u] LT","[prošlog] [ponedjeljka] [u] LT","[prošlog] [utorka] [u] LT","[prošle] [srijede] [u] LT","[prošlog] [četvrtka] [u] LT","[prošlog] [petka] [u] LT","[prošle] [subote] [u] LT"][this.day()]},sameElse:"L"},relativeTime:{future:"za %s",past:"prije %s",s:"nekoliko sekundi",m:at.translate,mm:at.translate,h:at.translate,hh:at.translate,d:"dan",dd:at.translate,M:"mjesec",MM:at.translate,y:"godinu",yy:at.translate},ordinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:7}});
//! moment.js locale configuration
//! locale : macedonian (mk)
//! author : Borislav Mickov : https://github.com/B0k0
en=n.defineLocale("mk",{months:"јануари_февруари_март_април_мај_јуни_јули_август_септември_октомври_ноември_декември".split("_"),monthsShort:"јан_фев_мар_апр_мај_јун_јул_авг_сеп_окт_ное_дек".split("_"),weekdays:"недела_понеделник_вторник_среда_четврток_петок_сабота".split("_"),weekdaysShort:"нед_пон_вто_сре_чет_пет_саб".split("_"),weekdaysMin:"нe_пo_вт_ср_че_пе_сa".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"D.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY H:mm",LLLL:"dddd, D MMMM YYYY H:mm"},calendar:{sameDay:"[Денес во] LT",nextDay:"[Утре во] LT",nextWeek:"[Во] dddd [во] LT",lastDay:"[Вчера во] LT",lastWeek:function(){switch(this.day()){case 0:case 3:case 6:return"[Изминатата] dddd [во] LT";case 1:case 2:case 4:case 5:return"[Изминатиот] dddd [во] LT"}},sameElse:"L"},relativeTime:{future:"после %s",past:"пред %s",s:"неколку секунди",m:"минута",mm:"%d минути",h:"час",hh:"%d часа",d:"ден",dd:"%d дена",M:"месец",MM:"%d месеци",y:"година",yy:"%d години"},ordinalParse:/\d{1,2}-(ев|ен|ти|ви|ри|ми)/,ordinal:function(n){var t=n%10,i=n%100;return n===0?n+"-ев":i===0?n+"-ен":i>10&&i<20?n+"-ти":t===1?n+"-ви":t===2?n+"-ри":t===7||t===8?n+"-ми":n+"-ти"},week:{dow:1,doy:7}});
//! moment.js locale configuration
//! locale : malayalam (ml)
//! author : Floyd Pink : https://github.com/floydpink
on=n.defineLocale("ml",{months:"ജനുവരി_ഫെബ്രുവരി_മാർച്ച്_ഏപ്രിൽ_മേയ്_ജൂൺ_ജൂലൈ_ഓഗസ്റ്റ്_സെപ്റ്റംബർ_ഒക്ടോബർ_നവംബർ_ഡിസംബർ".split("_"),monthsShort:"ജനു._ഫെബ്രു._മാർ._ഏപ്രി._മേയ്_ജൂൺ_ജൂലൈ._ഓഗ._സെപ്റ്റ._ഒക്ടോ._നവം._ഡിസം.".split("_"),weekdays:"ഞായറാഴ്ച_തിങ്കളാഴ്ച_ചൊവ്വാഴ്ച_ബുധനാഴ്ച_വ്യാഴാഴ്ച_വെള്ളിയാഴ്ച_ശനിയാഴ്ച".split("_"),weekdaysShort:"ഞായർ_തിങ്കൾ_ചൊവ്വ_ബുധൻ_വ്യാഴം_വെള്ളി_ശനി".split("_"),weekdaysMin:"ഞാ_തി_ചൊ_ബു_വ്യാ_വെ_ശ".split("_"),longDateFormat:{LT:"A h:mm -നു",LTS:"A h:mm:ss -നു",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm -നു",LLLL:"dddd, D MMMM YYYY, A h:mm -നു"},calendar:{sameDay:"[ഇന്ന്] LT",nextDay:"[നാളെ] LT",nextWeek:"dddd, LT",lastDay:"[ഇന്നലെ] LT",lastWeek:"[കഴിഞ്ഞ] dddd, LT",sameElse:"L"},relativeTime:{future:"%s കഴിഞ്ഞ്",past:"%s മുൻപ്",s:"അൽപ നിമിഷങ്ങൾ",m:"ഒരു മിനിറ്റ്",mm:"%d മിനിറ്റ്",h:"ഒരു മണിക്കൂർ",hh:"%d മണിക്കൂർ",d:"ഒരു ദിവസം",dd:"%d ദിവസം",M:"ഒരു മാസം",MM:"%d മാസം",y:"ഒരു വർഷം",yy:"%d വർഷം"},meridiemParse:/രാത്രി|രാവിലെ|ഉച്ച കഴിഞ്ഞ്|വൈകുന്നേരം|രാത്രി/i,isPM:function(n){return/^(ഉച്ച കഴിഞ്ഞ്|വൈകുന്നേരം|രാത്രി)$/.test(n)},meridiem:function(n){return n<4?"രാത്രി":n<12?"രാവിലെ":n<17?"ഉച്ച കഴിഞ്ഞ്":n<20?"വൈകുന്നേരം":"രാത്രി"}});
//! moment.js locale configuration
//! locale : Marathi (mr)
//! author : Harshad Kale : https://github.com/kalehv
//! author : Vivek Athalye : https://github.com/vnathalye
kc={"1":"१","2":"२","3":"३","4":"४","5":"५","6":"६","7":"७","8":"८","9":"९","0":"०"};dc={"१":"1","२":"2","३":"3","४":"4","५":"5","६":"6","७":"7","८":"8","९":"9","०":"0"};sn=n.defineLocale("mr",{months:"जानेवारी_फेब्रुवारी_मार्च_एप्रिल_मे_जून_जुलै_ऑगस्ट_सप्टेंबर_ऑक्टोबर_नोव्हेंबर_डिसेंबर".split("_"),monthsShort:"जाने._फेब्रु._मार्च._एप्रि._मे._जून._जुलै._ऑग._सप्टें._ऑक्टो._नोव्हें._डिसें.".split("_"),weekdays:"रविवार_सोमवार_मंगळवार_बुधवार_गुरूवार_शुक्रवार_शनिवार".split("_"),weekdaysShort:"रवि_सोम_मंगळ_बुध_गुरू_शुक्र_शनि".split("_"),weekdaysMin:"र_सो_मं_बु_गु_शु_श".split("_"),longDateFormat:{LT:"A h:mm वाजता",LTS:"A h:mm:ss वाजता",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm वाजता",LLLL:"dddd, D MMMM YYYY, A h:mm वाजता"},calendar:{sameDay:"[आज] LT",nextDay:"[उद्या] LT",nextWeek:"dddd, LT",lastDay:"[काल] LT",lastWeek:"[मागील] dddd, LT",sameElse:"L"},relativeTime:{future:"%sमध्ये",past:"%sपूर्वी",s:ut,m:ut,mm:ut,h:ut,hh:ut,d:ut,dd:ut,M:ut,MM:ut,y:ut,yy:ut},preparse:function(n){return n.replace(/[१२३४५६७८९०]/g,function(n){return dc[n]})},postformat:function(n){return n.replace(/\d/g,function(n){return kc[n]})},meridiemParse:/रात्री|सकाळी|दुपारी|सायंकाळी/,meridiemHour:function(n,t){return(n===12&&(n=0),t==="रात्री")?n<4?n:n+12:t==="सकाळी"?n:t==="दुपारी"?n>=10?n:n+12:t==="सायंकाळी"?n+12:void 0},meridiem:function(n){return n<4?"रात्री":n<10?"सकाळी":n<17?"दुपारी":n<20?"सायंकाळी":"रात्री"},week:{dow:0,doy:6}});
//! moment.js locale configuration
//! locale : Bahasa Malaysia (ms-MY)
//! author : Weldan Jamili : https://github.com/weldan
hn=n.defineLocale("ms-my",{months:"Januari_Februari_Mac_April_Mei_Jun_Julai_Ogos_September_Oktober_November_Disember".split("_"),monthsShort:"Jan_Feb_Mac_Apr_Mei_Jun_Jul_Ogs_Sep_Okt_Nov_Dis".split("_"),weekdays:"Ahad_Isnin_Selasa_Rabu_Khamis_Jumaat_Sabtu".split("_"),weekdaysShort:"Ahd_Isn_Sel_Rab_Kha_Jum_Sab".split("_"),weekdaysMin:"Ah_Is_Sl_Rb_Km_Jm_Sb".split("_"),longDateFormat:{LT:"HH.mm",LTS:"HH.mm.ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY [pukul] HH.mm",LLLL:"dddd, D MMMM YYYY [pukul] HH.mm"},meridiemParse:/pagi|tengahari|petang|malam/,meridiemHour:function(n,t){return(n===12&&(n=0),t==="pagi")?n:t==="tengahari"?n>=11?n:n+12:t==="petang"||t==="malam"?n+12:void 0},meridiem:function(n){return n<11?"pagi":n<15?"tengahari":n<19?"petang":"malam"},calendar:{sameDay:"[Hari ini pukul] LT",nextDay:"[Esok pukul] LT",nextWeek:"dddd [pukul] LT",lastDay:"[Kelmarin pukul] LT",lastWeek:"dddd [lepas pukul] LT",sameElse:"L"},relativeTime:{future:"dalam %s",past:"%s yang lepas",s:"beberapa saat",m:"seminit",mm:"%d minit",h:"sejam",hh:"%d jam",d:"sehari",dd:"%d hari",M:"sebulan",MM:"%d bulan",y:"setahun",yy:"%d tahun"},week:{dow:1,doy:7}});
//! moment.js locale configuration
//! locale : Bahasa Malaysia (ms-MY)
//! author : Weldan Jamili : https://github.com/weldan
cn=n.defineLocale("ms",{months:"Januari_Februari_Mac_April_Mei_Jun_Julai_Ogos_September_Oktober_November_Disember".split("_"),monthsShort:"Jan_Feb_Mac_Apr_Mei_Jun_Jul_Ogs_Sep_Okt_Nov_Dis".split("_"),weekdays:"Ahad_Isnin_Selasa_Rabu_Khamis_Jumaat_Sabtu".split("_"),weekdaysShort:"Ahd_Isn_Sel_Rab_Kha_Jum_Sab".split("_"),weekdaysMin:"Ah_Is_Sl_Rb_Km_Jm_Sb".split("_"),longDateFormat:{LT:"HH.mm",LTS:"HH.mm.ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY [pukul] HH.mm",LLLL:"dddd, D MMMM YYYY [pukul] HH.mm"},meridiemParse:/pagi|tengahari|petang|malam/,meridiemHour:function(n,t){return(n===12&&(n=0),t==="pagi")?n:t==="tengahari"?n>=11?n:n+12:t==="petang"||t==="malam"?n+12:void 0},meridiem:function(n){return n<11?"pagi":n<15?"tengahari":n<19?"petang":"malam"},calendar:{sameDay:"[Hari ini pukul] LT",nextDay:"[Esok pukul] LT",nextWeek:"dddd [pukul] LT",lastDay:"[Kelmarin pukul] LT",lastWeek:"dddd [lepas pukul] LT",sameElse:"L"},relativeTime:{future:"dalam %s",past:"%s yang lepas",s:"beberapa saat",m:"seminit",mm:"%d minit",h:"sejam",hh:"%d jam",d:"sehari",dd:"%d hari",M:"sebulan",MM:"%d bulan",y:"setahun",yy:"%d tahun"},week:{dow:1,doy:7}});
//! moment.js locale configuration
//! locale : Burmese (my)
//! author : Squar team, mysquar.com
var ln={"1":"၁","2":"၂","3":"၃","4":"၄","5":"၅","6":"၆","7":"၇","8":"၈","9":"၉","0":"၀"},an={"၁":"1","၂":"2","၃":"3","၄":"4","၅":"5","၆":"6","၇":"7","၈":"8","၉":"9","၀":"0"},trt=n.defineLocale("my",{months:"ဇန်နဝါရီ_ဖေဖော်ဝါရီ_မတ်_ဧပြီ_မေ_ဇွန်_ဇူလိုင်_သြဂုတ်_စက်တင်ဘာ_အောက်တိုဘာ_နိုဝင်ဘာ_ဒီဇင်ဘာ".split("_"),monthsShort:"ဇန်_ဖေ_မတ်_ပြီ_မေ_ဇွန်_လိုင်_သြ_စက်_အောက်_နို_ဒီ".split("_"),weekdays:"တနင်္ဂနွေ_တနင်္လာ_အင်္ဂါ_ဗုဒ္ဓဟူး_ကြာသပတေး_သောကြာ_စနေ".split("_"),weekdaysShort:"နွေ_လာ_ဂါ_ဟူး_ကြာ_သော_နေ".split("_"),weekdaysMin:"နွေ_လာ_ဂါ_ဟူး_ကြာ_သော_နေ".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[ယနေ.] LT [မှာ]",nextDay:"[မနက်ဖြန်] LT [မှာ]",nextWeek:"dddd LT [မှာ]",lastDay:"[မနေ.က] LT [မှာ]",lastWeek:"[ပြီးခဲ့သော] dddd LT [မှာ]",sameElse:"L"},relativeTime:{future:"လာမည့် %s မှာ",past:"လွန်ခဲ့သော %s က",s:"စက္ကန်.အနည်းငယ်",m:"တစ်မိနစ်",mm:"%d မိနစ်",h:"တစ်နာရီ",hh:"%d နာရီ",d:"တစ်ရက်",dd:"%d ရက်",M:"တစ်လ",MM:"%d လ",y:"တစ်နှစ်",yy:"%d နှစ်"},preparse:function(n){return n.replace(/[၁၂၃၄၅၆၇၈၉၀]/g,function(n){return an[n]})},postformat:function(n){return n.replace(/\d/g,function(n){return ln[n]})},week:{dow:1,doy:4}});
//! moment.js locale configuration
//! locale : norwegian bokmål (nb)
//! authors : Espen Hovlandsdal : https://github.com/rexxars
//!           Sigurd Gartmann : https://github.com/sigurdga
vn=n.defineLocale("nb",{months:"januar_februar_mars_april_mai_juni_juli_august_september_oktober_november_desember".split("_"),monthsShort:"jan._feb._mars_april_mai_juni_juli_aug._sep._okt._nov._des.".split("_"),weekdays:"søndag_mandag_tirsdag_onsdag_torsdag_fredag_lørdag".split("_"),weekdaysShort:"sø._ma._ti._on._to._fr._lø.".split("_"),weekdaysMin:"sø_ma_ti_on_to_fr_lø".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY [kl.] HH:mm",LLLL:"dddd D. MMMM YYYY [kl.] HH:mm"},calendar:{sameDay:"[i dag kl.] LT",nextDay:"[i morgen kl.] LT",nextWeek:"dddd [kl.] LT",lastDay:"[i går kl.] LT",lastWeek:"[forrige] dddd [kl.] LT",sameElse:"L"},relativeTime:{future:"om %s",past:"for %s siden",s:"noen sekunder",m:"ett minutt",mm:"%d minutter",h:"en time",hh:"%d timer",d:"en dag",dd:"%d dager",M:"en måned",MM:"%d måneder",y:"ett år",yy:"%d år"},ordinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});
//! moment.js locale configuration
//! locale : nepali/nepalese
//! author : suvash : https://github.com/suvash
var yn={"1":"१","2":"२","3":"३","4":"४","5":"५","6":"६","7":"७","8":"८","9":"९","0":"०"},pn={"१":"1","२":"2","३":"3","४":"4","५":"5","६":"6","७":"7","८":"8","९":"9","०":"0"},irt=n.defineLocale("ne",{months:"जनवरी_फेब्रुवरी_मार्च_अप्रिल_मई_जुन_जुलाई_अगष्ट_सेप्टेम्बर_अक्टोबर_नोभेम्बर_डिसेम्बर".split("_"),monthsShort:"जन._फेब्रु._मार्च_अप्रि._मई_जुन_जुलाई._अग._सेप्ट._अक्टो._नोभे._डिसे.".split("_"),weekdays:"आइतबार_सोमबार_मङ्गलबार_बुधबार_बिहिबार_शुक्रबार_शनिबार".split("_"),weekdaysShort:"आइत._सोम._मङ्गल._बुध._बिहि._शुक्र._शनि.".split("_"),weekdaysMin:"आ._सो._मं._बु._बि._शु._श.".split("_"),longDateFormat:{LT:"Aको h:mm बजे",LTS:"Aको h:mm:ss बजे",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, Aको h:mm बजे",LLLL:"dddd, D MMMM YYYY, Aको h:mm बजे"},preparse:function(n){return n.replace(/[१२३४५६७८९०]/g,function(n){return pn[n]})},postformat:function(n){return n.replace(/\d/g,function(n){return yn[n]})},meridiemParse:/राति|बिहान|दिउँसो|साँझ/,meridiemHour:function(n,t){return(n===12&&(n=0),t==="राति")?n<4?n:n+12:t==="बिहान"?n:t==="दिउँसो"?n>=10?n:n+12:t==="साँझ"?n+12:void 0},meridiem:function(n){return n<3?"राति":n<12?"बिहान":n<16?"दिउँसो":n<20?"साँझ":"राति"},calendar:{sameDay:"[आज] LT",nextDay:"[भोलि] LT",nextWeek:"[आउँदो] dddd[,] LT",lastDay:"[हिजो] LT",lastWeek:"[गएको] dddd[,] LT",sameElse:"L"},relativeTime:{future:"%sमा",past:"%s अगाडि",s:"केही क्षण",m:"एक मिनेट",mm:"%d मिनेट",h:"एक घण्टा",hh:"%d घण्टा",d:"एक दिन",dd:"%d दिन",M:"एक महिना",MM:"%d महिना",y:"एक बर्ष",yy:"%d बर्ष"},week:{dow:0,doy:6}});
//! moment.js locale configuration
//! locale : dutch (nl)
//! author : Joris Röling : https://github.com/jjupiter
var wn="jan._feb._mrt._apr._mei_jun._jul._aug._sep._okt._nov._dec.".split("_"),bn="jan_feb_mrt_apr_mei_jun_jul_aug_sep_okt_nov_dec".split("_"),rrt=n.defineLocale("nl",{months:"januari_februari_maart_april_mei_juni_juli_augustus_september_oktober_november_december".split("_"),monthsShort:function(n,t){return/-MMM-/.test(t)?bn[n.month()]:wn[n.month()]},weekdays:"zondag_maandag_dinsdag_woensdag_donderdag_vrijdag_zaterdag".split("_"),weekdaysShort:"zo._ma._di._wo._do._vr._za.".split("_"),weekdaysMin:"Zo_Ma_Di_Wo_Do_Vr_Za".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD-MM-YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[vandaag om] LT",nextDay:"[morgen om] LT",nextWeek:"dddd [om] LT",lastDay:"[gisteren om] LT",lastWeek:"[afgelopen] dddd [om] LT",sameElse:"L"},relativeTime:{future:"over %s",past:"%s geleden",s:"een paar seconden",m:"één minuut",mm:"%d minuten",h:"één uur",hh:"%d uur",d:"één dag",dd:"%d dagen",M:"één maand",MM:"%d maanden",y:"één jaar",yy:"%d jaar"},ordinalParse:/\d{1,2}(ste|de)/,ordinal:function(n){return n+(n===1||n===8||n>=20?"ste":"de")},week:{dow:1,doy:4}});
//! moment.js locale configuration
//! locale : norwegian nynorsk (nn)
//! author : https://github.com/mechuwind
kn=n.defineLocale("nn",{months:"januar_februar_mars_april_mai_juni_juli_august_september_oktober_november_desember".split("_"),monthsShort:"jan_feb_mar_apr_mai_jun_jul_aug_sep_okt_nov_des".split("_"),weekdays:"sundag_måndag_tysdag_onsdag_torsdag_fredag_laurdag".split("_"),weekdaysShort:"sun_mån_tys_ons_tor_fre_lau".split("_"),weekdaysMin:"su_må_ty_on_to_fr_lø".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY [kl.] H:mm",LLLL:"dddd D. MMMM YYYY [kl.] HH:mm"},calendar:{sameDay:"[I dag klokka] LT",nextDay:"[I morgon klokka] LT",nextWeek:"dddd [klokka] LT",lastDay:"[I går klokka] LT",lastWeek:"[Føregåande] dddd [klokka] LT",sameElse:"L"},relativeTime:{future:"om %s",past:"for %s sidan",s:"nokre sekund",m:"eit minutt",mm:"%d minutt",h:"ein time",hh:"%d timar",d:"ein dag",dd:"%d dagar",M:"ein månad",MM:"%d månader",y:"eit år",yy:"%d år"},ordinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});
//! moment.js locale configuration
//! locale : polish (pl)
//! author : Rafal Hirsz : https://github.com/evoL
de="styczeń_luty_marzec_kwiecień_maj_czerwiec_lipiec_sierpień_wrzesień_październik_listopad_grudzień".split("_");ge="stycznia_lutego_marca_kwietnia_maja_czerwca_lipca_sierpnia_września_października_listopada_grudnia".split("_");dn=n.defineLocale("pl",{months:function(n,t){return t===""?"("+ge[n.month()]+"|"+de[n.month()]+")":/D MMMM/.test(t)?ge[n.month()]:de[n.month()]},monthsShort:"sty_lut_mar_kwi_maj_cze_lip_sie_wrz_paź_lis_gru".split("_"),weekdays:"niedziela_poniedziałek_wtorek_środa_czwartek_piątek_sobota".split("_"),weekdaysShort:"nie_pon_wt_śr_czw_pt_sb".split("_"),weekdaysMin:"Nd_Pn_Wt_Śr_Cz_Pt_So".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Dziś o] LT",nextDay:"[Jutro o] LT",nextWeek:"[W] dddd [o] LT",lastDay:"[Wczoraj o] LT",lastWeek:function(){switch(this.day()){case 0:return"[W zeszłą niedzielę o] LT";case 3:return"[W zeszłą środę o] LT";case 6:return"[W zeszłą sobotę o] LT";default:return"[W zeszły] dddd [o] LT"}},sameElse:"L"},relativeTime:{future:"za %s",past:"%s temu",s:"kilka sekund",m:rr,mm:rr,h:rr,hh:rr,d:"1 dzień",dd:"%d dni",M:"miesiąc",MM:rr,y:"rok",yy:rr},ordinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});
//! moment.js locale configuration
//! locale : brazilian portuguese (pt-br)
//! author : Caio Ribeiro Pereira : https://github.com/caio-ribeiro-pereira
gn=n.defineLocale("pt-br",{months:"Janeiro_Fevereiro_Março_Abril_Maio_Junho_Julho_Agosto_Setembro_Outubro_Novembro_Dezembro".split("_"),monthsShort:"Jan_Fev_Mar_Abr_Mai_Jun_Jul_Ago_Set_Out_Nov_Dez".split("_"),weekdays:"Domingo_Segunda-Feira_Terça-Feira_Quarta-Feira_Quinta-Feira_Sexta-Feira_Sábado".split("_"),weekdaysShort:"Dom_Seg_Ter_Qua_Qui_Sex_Sáb".split("_"),weekdaysMin:"Dom_2ª_3ª_4ª_5ª_6ª_Sáb".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY [às] HH:mm",LLLL:"dddd, D [de] MMMM [de] YYYY [às] HH:mm"},calendar:{sameDay:"[Hoje às] LT",nextDay:"[Amanhã às] LT",nextWeek:"dddd [às] LT",lastDay:"[Ontem às] LT",lastWeek:function(){return this.day()===0||this.day()===6?"[Último] dddd [às] LT":"[Última] dddd [às] LT"},sameElse:"L"},relativeTime:{future:"em %s",past:"%s atrás",s:"poucos segundos",m:"um minuto",mm:"%d minutos",h:"uma hora",hh:"%d horas",d:"um dia",dd:"%d dias",M:"um mês",MM:"%d meses",y:"um ano",yy:"%d anos"},ordinalParse:/\d{1,2}º/,ordinal:"%dº"});
//! moment.js locale configuration
//! locale : portuguese (pt)
//! author : Jefferson : https://github.com/jalex79
ntt=n.defineLocale("pt",{months:"Janeiro_Fevereiro_Março_Abril_Maio_Junho_Julho_Agosto_Setembro_Outubro_Novembro_Dezembro".split("_"),monthsShort:"Jan_Fev_Mar_Abr_Mai_Jun_Jul_Ago_Set_Out_Nov_Dez".split("_"),weekdays:"Domingo_Segunda-Feira_Terça-Feira_Quarta-Feira_Quinta-Feira_Sexta-Feira_Sábado".split("_"),weekdaysShort:"Dom_Seg_Ter_Qua_Qui_Sex_Sáb".split("_"),weekdaysMin:"Dom_2ª_3ª_4ª_5ª_6ª_Sáb".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY HH:mm",LLLL:"dddd, D [de] MMMM [de] YYYY HH:mm"},calendar:{sameDay:"[Hoje às] LT",nextDay:"[Amanhã às] LT",nextWeek:"dddd [às] LT",lastDay:"[Ontem às] LT",lastWeek:function(){return this.day()===0||this.day()===6?"[Último] dddd [às] LT":"[Última] dddd [às] LT"},sameElse:"L"},relativeTime:{future:"em %s",past:"há %s",s:"segundos",m:"um minuto",mm:"%d minutos",h:"uma hora",hh:"%d horas",d:"um dia",dd:"%d dias",M:"um mês",MM:"%d meses",y:"um ano",yy:"%d anos"},ordinalParse:/\d{1,2}º/,ordinal:"%dº",week:{dow:1,doy:4}});
//! moment.js locale configuration
//! locale : romanian (ro)
//! author : Vlad Gurdiga : https://github.com/gurdiga
//! author : Valentin Agachi : https://github.com/avaly
ttt=n.defineLocale("ro",{months:"ianuarie_februarie_martie_aprilie_mai_iunie_iulie_august_septembrie_octombrie_noiembrie_decembrie".split("_"),monthsShort:"ian._febr._mart._apr._mai_iun._iul._aug._sept._oct._nov._dec.".split("_"),weekdays:"duminică_luni_marți_miercuri_joi_vineri_sâmbătă".split("_"),weekdaysShort:"Dum_Lun_Mar_Mie_Joi_Vin_Sâm".split("_"),weekdaysMin:"Du_Lu_Ma_Mi_Jo_Vi_Sâ".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY H:mm",LLLL:"dddd, D MMMM YYYY H:mm"},calendar:{sameDay:"[azi la] LT",nextDay:"[mâine la] LT",nextWeek:"dddd [la] LT",lastDay:"[ieri la] LT",lastWeek:"[fosta] dddd [la] LT",sameElse:"L"},relativeTime:{future:"peste %s",past:"%s în urmă",s:"câteva secunde",m:"un minut",mm:tu,h:"o oră",hh:tu,d:"o zi",dd:tu,M:"o lună",MM:tu,y:"un an",yy:tu},week:{dow:1,doy:7}});
//! moment.js locale configuration
//! locale : russian (ru)
//! author : Viktorminator : https://github.com/Viktorminator
//! Author : Menelion Elensúle : https://github.com/Oire
sf=[/^янв/i,/^фев/i,/^мар/i,/^апр/i,/^ма[й|я]/i,/^июн/i,/^июл/i,/^авг/i,/^сен/i,/^окт/i,/^ноя/i,/^дек/i];rtt=n.defineLocale("ru",{months:{format:"Января_Февраля_Марта_Апреля_Мая_Июня_Июля_Августа_Сентября_Октября_Ноября_Декабря".split("_"),standalone:"Январь_Февраль_Март_Апрель_Май_Июнь_Июль_Август_Сентябрь_Октябрь_Ноябрь_Декабрь".split("_")},monthsShort:{format:"янв_фев_мар_апр_мая_июня_июля_авг_сен_окт_ноя_дек".split("_"),standalone:"янв_фев_март_апр_май_июнь_июль_авг_сен_окт_ноя_дек".split("_")},weekdays:{standalone:"Воскресенье_Понедельник_Вторник_Среда_Четверг_Пятница_Суббота".split("_"),format:"Воскресенье_Понедельник_Вторник_Среду_Четверг_Пятницу_Субботу".split("_"),isFormat:/\[ ?[Вв] ?(?:прошлую|следующую|эту)? ?\] ?dddd/},weekdaysShort:"Вс_Пн_Вт_Ср_Чт_Пт_Сб".split("_"),weekdaysMin:"Вс_Пн_Вт_Ср_Чт_Пт_Сб".split("_"),monthsParse:sf,longMonthsParse:sf,shortMonthsParse:sf,longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY г.",LLL:"D MMMM YYYY г., HH:mm",LLLL:"dddd, D MMMM YYYY г., HH:mm"},calendar:{sameDay:"[Сегодня в] LT",nextDay:"[Завтра в] LT",lastDay:"[Вчера в] LT",nextWeek:function(n){if(n.week()!==this.week())switch(this.day()){case 0:return"[В следующее] dddd [в] LT";case 1:case 2:case 4:return"[В следующий] dddd [в] LT";case 3:case 5:case 6:return"[В следующую] dddd [в] LT"}else return this.day()===2?"[Во] dddd [в] LT":"[В] dddd [в] LT"},lastWeek:function(n){if(n.week()!==this.week())switch(this.day()){case 0:return"[В прошлое] dddd [в] LT";case 1:case 2:case 4:return"[В прошлый] dddd [в] LT";case 3:case 5:case 6:return"[В прошлую] dddd [в] LT"}else return this.day()===2?"[Во] dddd [в] LT":"[В] dddd [в] LT"},sameElse:"L"},relativeTime:{future:"через %s",past:"%s назад",s:"несколько секунд",m:ur,mm:ur,h:"час",hh:ur,d:"день",dd:ur,M:"месяц",MM:ur,y:"год",yy:ur},meridiemParse:/ночи|утра|дня|вечера/i,isPM:function(n){return/^(дня|вечера)$/.test(n)},meridiem:function(n){return n<4?"ночи":n<12?"утра":n<17?"дня":"вечера"},ordinalParse:/\d{1,2}-(й|го|я)/,ordinal:function(n,t){switch(t){case"M":case"d":case"DDD":return n+"-й";case"D":return n+"-го";case"w":case"W":return n+"-я";default:return n}},week:{dow:1,doy:7}});
//! moment.js locale configuration
//! locale : Northern Sami (se)
//! authors : Bård Rolstad Henriksen : https://github.com/karamell
utt=n.defineLocale("se",{months:"ođđajagemánnu_guovvamánnu_njukčamánnu_cuoŋománnu_miessemánnu_geassemánnu_suoidnemánnu_borgemánnu_čakčamánnu_golggotmánnu_skábmamánnu_juovlamánnu".split("_"),monthsShort:"ođđj_guov_njuk_cuo_mies_geas_suoi_borg_čakč_golg_skáb_juov".split("_"),weekdays:"sotnabeaivi_vuossárga_maŋŋebárga_gaskavahkku_duorastat_bearjadat_lávvardat".split("_"),weekdaysShort:"sotn_vuos_maŋ_gask_duor_bear_láv".split("_"),weekdaysMin:"s_v_m_g_d_b_L".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"MMMM D. [b.] YYYY",LLL:"MMMM D. [b.] YYYY [ti.] HH:mm",LLLL:"dddd, MMMM D. [b.] YYYY [ti.] HH:mm"},calendar:{sameDay:"[otne ti] LT",nextDay:"[ihttin ti] LT",nextWeek:"dddd [ti] LT",lastDay:"[ikte ti] LT",lastWeek:"[ovddit] dddd [ti] LT",sameElse:"L"},relativeTime:{future:"%s geažes",past:"maŋit %s",s:"moadde sekunddat",m:"okta minuhta",mm:"%d minuhtat",h:"okta diimmu",hh:"%d diimmut",d:"okta beaivi",dd:"%d beaivvit",M:"okta mánnu",MM:"%d mánut",y:"okta jahki",yy:"%d jagit"},ordinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});
//! moment.js locale configuration
//! locale : Sinhalese (si)
//! author : Sampath Sitinamaluwa : https://github.com/sampathsris
ftt=n.defineLocale("si",{months:"ජනවාරි_පෙබරවාරි_මාර්තු_අප්‍රේල්_මැයි_ජූනි_ජූලි_අගෝස්තු_සැප්තැම්බර්_ඔක්තෝබර්_නොවැම්බර්_දෙසැම්බර්".split("_"),monthsShort:"ජන_පෙබ_මාර්_අප්_මැයි_ජූනි_ජූලි_අගෝ_සැප්_ඔක්_නොවැ_දෙසැ".split("_"),weekdays:"ඉරිදා_සඳුදා_අඟහරුවාදා_බදාදා_බ්‍රහස්පතින්දා_සිකුරාදා_සෙනසුරාදා".split("_"),weekdaysShort:"ඉරි_සඳු_අඟ_බදා_බ්‍රහ_සිකු_සෙන".split("_"),weekdaysMin:"ඉ_ස_අ_බ_බ්‍ර_සි_සෙ".split("_"),longDateFormat:{LT:"a h:mm",LTS:"a h:mm:ss",L:"YYYY/MM/DD",LL:"YYYY MMMM D",LLL:"YYYY MMMM D, a h:mm",LLLL:"YYYY MMMM D [වැනි] dddd, a h:mm:ss"},calendar:{sameDay:"[අද] LT[ට]",nextDay:"[හෙට] LT[ට]",nextWeek:"dddd LT[ට]",lastDay:"[ඊයේ] LT[ට]",lastWeek:"[පසුගිය] dddd LT[ට]",sameElse:"L"},relativeTime:{future:"%sකින්",past:"%sකට පෙර",s:"තත්පර කිහිපය",m:"මිනිත්තුව",mm:"මිනිත්තු %d",h:"පැය",hh:"පැය %d",d:"දිනය",dd:"දින %d",M:"මාසය",MM:"මාස %d",y:"වසර",yy:"වසර %d"},ordinalParse:/\d{1,2} වැනි/,ordinal:function(n){return n+" වැනි"},meridiem:function(n,t,i){return n>11?i?"ප.ව.":"පස් වරු":i?"පෙ.ව.":"පෙර වරු"}});
//! moment.js locale configuration
//! locale : slovak (sk)
//! author : Martin Minka : https://github.com/k2s
//! based on work of petrbela : https://github.com/petrbela
gc="január_február_marec_apríl_máj_jún_júl_august_september_október_november_december".split("_");nl="jan_feb_mar_apr_máj_jún_júl_aug_sep_okt_nov_dec".split("_");ett=n.defineLocale("sk",{months:gc,monthsShort:nl,weekdays:"nedeľa_pondelok_utorok_streda_štvrtok_piatok_sobota".split("_"),weekdaysShort:"ne_po_ut_st_št_pi_so".split("_"),weekdaysMin:"ne_po_ut_st_št_pi_so".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd D. MMMM YYYY H:mm"},calendar:{sameDay:"[dnes o] LT",nextDay:"[zajtra o] LT",nextWeek:function(){switch(this.day()){case 0:return"[v nedeľu o] LT";case 1:case 2:return"[v] dddd [o] LT";case 3:return"[v stredu o] LT";case 4:return"[vo štvrtok o] LT";case 5:return"[v piatok o] LT";case 6:return"[v sobotu o] LT"}},lastDay:"[včera o] LT",lastWeek:function(){switch(this.day()){case 0:return"[minulú nedeľu o] LT";case 1:case 2:return"[minulý] dddd [o] LT";case 3:return"[minulú stredu o] LT";case 4:case 5:return"[minulý] dddd [o] LT";case 6:return"[minulú sobotu o] LT"}},sameElse:"L"},relativeTime:{future:"za %s",past:"pred %s",s:ft,m:ft,mm:ft,h:ft,hh:ft,d:ft,dd:ft,M:ft,MM:ft,y:ft,yy:ft},ordinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});
//! moment.js locale configuration
//! locale : slovenian (sl)
//! author : Robert Sedovšek : https://github.com/sedovsek
ott=n.defineLocale("sl",{months:"januar_februar_marec_april_maj_junij_julij_avgust_september_oktober_november_december".split("_"),monthsShort:"jan._feb._mar._apr._maj._jun._jul._avg._sep._okt._nov._dec.".split("_"),weekdays:"nedelja_ponedeljek_torek_sreda_četrtek_petek_sobota".split("_"),weekdaysShort:"ned._pon._tor._sre._čet._pet._sob.".split("_"),weekdaysMin:"ne_po_to_sr_če_pe_so".split("_"),longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD. MM. YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd, D. MMMM YYYY H:mm"},calendar:{sameDay:"[danes ob] LT",nextDay:"[jutri ob] LT",nextWeek:function(){switch(this.day()){case 0:return"[v] [nedeljo] [ob] LT";case 3:return"[v] [sredo] [ob] LT";case 6:return"[v] [soboto] [ob] LT";case 1:case 2:case 4:case 5:return"[v] dddd [ob] LT"}},lastDay:"[včeraj ob] LT",lastWeek:function(){switch(this.day()){case 0:return"[prejšnjo] [nedeljo] [ob] LT";case 3:return"[prejšnjo] [sredo] [ob] LT";case 6:return"[prejšnjo] [soboto] [ob] LT";case 1:case 2:case 4:case 5:return"[prejšnji] dddd [ob] LT"}},sameElse:"L"},relativeTime:{future:"čez %s",past:"pred %s",s:et,m:et,mm:et,h:et,hh:et,d:et,dd:et,M:et,MM:et,y:et,yy:et},ordinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:7}});
//! moment.js locale configuration
//! locale : Albanian (sq)
//! author : Flakërim Ismani : https://github.com/flakerimi
//! author: Menelion Elensúle: https://github.com/Oire (tests)
//! author : Oerd Cukalla : https://github.com/oerd (fixes)
stt=n.defineLocale("sq",{months:"Janar_Shkurt_Mars_Prill_Maj_Qershor_Korrik_Gusht_Shtator_Tetor_Nëntor_Dhjetor".split("_"),monthsShort:"Jan_Shk_Mar_Pri_Maj_Qer_Kor_Gus_Sht_Tet_Nën_Dhj".split("_"),weekdays:"E Diel_E Hënë_E Martë_E Mërkurë_E Enjte_E Premte_E Shtunë".split("_"),weekdaysShort:"Die_Hën_Mar_Mër_Enj_Pre_Sht".split("_"),weekdaysMin:"D_H_Ma_Më_E_P_Sh".split("_"),meridiemParse:/PD|MD/,isPM:function(n){return n.charAt(0)==="M"},meridiem:function(n){return n<12?"PD":"MD"},longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[Sot në] LT",nextDay:"[Nesër në] LT",nextWeek:"dddd [në] LT",lastDay:"[Dje në] LT",lastWeek:"dddd [e kaluar në] LT",sameElse:"L"},relativeTime:{future:"në %s",past:"%s më parë",s:"disa sekonda",m:"një minutë",mm:"%d minuta",h:"një orë",hh:"%d orë",d:"një ditë",dd:"%d ditë",M:"një muaj",MM:"%d muaj",y:"një vit",yy:"%d vite"},ordinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});
//! moment.js locale configuration
//! locale : Serbian-cyrillic (sr-cyrl)
//! author : Milan Janačković<<EMAIL>> : https://github.com/milan-j
vt={words:{m:["један минут","једне минуте"],mm:["минут","минуте","минута"],h:["један сат","једног сата"],hh:["сат","сата","сати"],dd:["дан","дана","дана"],MM:["месец","месеца","месеци"],yy:["година","године","година"]},correctGrammaticalCase:function(n,t){return n===1?t[0]:n>=2&&n<=4?t[1]:t[2]},translate:function(n,t,i){var r=vt.words[i];return i.length===1?t?r[0]:r[1]:n+" "+vt.correctGrammaticalCase(n,r)}};htt=n.defineLocale("sr-cyrl",{months:["јануар","фебруар","март","април","мај","јун","јул","август","септембар","октобар","новембар","децембар"],monthsShort:["јан.","феб.","мар.","апр.","мај","јун","јул","авг.","сеп.","окт.","нов.","дец."],weekdays:["недеља","понедељак","уторак","среда","четвртак","петак","субота"],weekdaysShort:["нед.","пон.","уто.","сре.","чет.","пет.","суб."],weekdaysMin:["не","по","ут","ср","че","пе","су"],longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD. MM. YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd, D. MMMM YYYY H:mm"},calendar:{sameDay:"[данас у] LT",nextDay:"[сутра у] LT",nextWeek:function(){switch(this.day()){case 0:return"[у] [недељу] [у] LT";case 3:return"[у] [среду] [у] LT";case 6:return"[у] [суботу] [у] LT";case 1:case 2:case 4:case 5:return"[у] dddd [у] LT"}},lastDay:"[јуче у] LT",lastWeek:function(){return["[прошле] [недеље] [у] LT","[прошлог] [понедељка] [у] LT","[прошлог] [уторка] [у] LT","[прошле] [среде] [у] LT","[прошлог] [четвртка] [у] LT","[прошлог] [петка] [у] LT","[прошле] [суботе] [у] LT"][this.day()]},sameElse:"L"},relativeTime:{future:"за %s",past:"пре %s",s:"неколико секунди",m:vt.translate,mm:vt.translate,h:vt.translate,hh:vt.translate,d:"дан",dd:vt.translate,M:"месец",MM:vt.translate,y:"годину",yy:vt.translate},ordinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:7}});
//! moment.js locale configuration
//! locale : Serbian-latin (sr)
//! author : Milan Janačković<<EMAIL>> : https://github.com/milan-j
yt={words:{m:["jedan minut","jedne minute"],mm:["minut","minute","minuta"],h:["jedan sat","jednog sata"],hh:["sat","sata","sati"],dd:["dan","dana","dana"],MM:["mesec","meseca","meseci"],yy:["godina","godine","godina"]},correctGrammaticalCase:function(n,t){return n===1?t[0]:n>=2&&n<=4?t[1]:t[2]},translate:function(n,t,i){var r=yt.words[i];return i.length===1?t?r[0]:r[1]:n+" "+yt.correctGrammaticalCase(n,r)}};ctt=n.defineLocale("sr",{months:["januar","februar","mart","april","maj","jun","jul","avgust","septembar","oktobar","novembar","decembar"],monthsShort:["jan.","feb.","mar.","apr.","maj","jun","jul","avg.","sep.","okt.","nov.","dec."],weekdays:["nedelja","ponedeljak","utorak","sreda","četvrtak","petak","subota"],weekdaysShort:["ned.","pon.","uto.","sre.","čet.","pet.","sub."],weekdaysMin:["ne","po","ut","sr","če","pe","su"],longDateFormat:{LT:"H:mm",LTS:"H:mm:ss",L:"DD. MM. YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY H:mm",LLLL:"dddd, D. MMMM YYYY H:mm"},calendar:{sameDay:"[danas u] LT",nextDay:"[sutra u] LT",nextWeek:function(){switch(this.day()){case 0:return"[u] [nedelju] [u] LT";case 3:return"[u] [sredu] [u] LT";case 6:return"[u] [subotu] [u] LT";case 1:case 2:case 4:case 5:return"[u] dddd [u] LT"}},lastDay:"[juče u] LT",lastWeek:function(){return["[prošle] [nedelje] [u] LT","[prošlog] [ponedeljka] [u] LT","[prošlog] [utorka] [u] LT","[prošle] [srede] [u] LT","[prošlog] [četvrtka] [u] LT","[prošlog] [petka] [u] LT","[prošle] [subote] [u] LT"][this.day()]},sameElse:"L"},relativeTime:{future:"za %s",past:"pre %s",s:"nekoliko sekundi",m:yt.translate,mm:yt.translate,h:yt.translate,hh:yt.translate,d:"dan",dd:yt.translate,M:"mesec",MM:yt.translate,y:"godinu",yy:yt.translate},ordinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:7}});
//! moment.js locale configuration
//! locale : swedish (sv)
//! author : Jens Alm : https://github.com/ulmus
ltt=n.defineLocale("sv",{months:"januari_februari_mars_april_maj_juni_juli_augusti_september_oktober_november_december".split("_"),monthsShort:"jan_feb_mar_apr_maj_jun_jul_aug_sep_okt_nov_dec".split("_"),weekdays:"söndag_måndag_tisdag_onsdag_torsdag_fredag_lördag".split("_"),weekdaysShort:"sön_mån_tis_ons_tor_fre_lör".split("_"),weekdaysMin:"sö_må_ti_on_to_fr_lö".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY-MM-DD",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[Idag] LT",nextDay:"[Imorgon] LT",lastDay:"[Igår] LT",nextWeek:"[På] dddd LT",lastWeek:"[I] dddd[s] LT",sameElse:"L"},relativeTime:{future:"om %s",past:"för %s sedan",s:"några sekunder",m:"en minut",mm:"%d minuter",h:"en timme",hh:"%d timmar",d:"en dag",dd:"%d dagar",M:"en månad",MM:"%d månader",y:"ett år",yy:"%d år"},ordinalParse:/\d{1,2}(e|a)/,ordinal:function(n){var t=n%10,i=~~(n%100/10)==1?"e":t===1?"a":t===2?"a":t===3?"e":"e";return n+i},week:{dow:1,doy:4}});
//! moment.js locale configuration
//! locale : swahili (sw)
//! author : Fahad Kassim : https://github.com/fadsel
att=n.defineLocale("sw",{months:"Januari_Februari_Machi_Aprili_Mei_Juni_Julai_Agosti_Septemba_Oktoba_Novemba_Desemba".split("_"),monthsShort:"Jan_Feb_Mac_Apr_Mei_Jun_Jul_Ago_Sep_Okt_Nov_Des".split("_"),weekdays:"Jumapili_Jumatatu_Jumanne_Jumatano_Alhamisi_Ijumaa_Jumamosi".split("_"),weekdaysShort:"Jpl_Jtat_Jnne_Jtan_Alh_Ijm_Jmos".split("_"),weekdaysMin:"J2_J3_J4_J5_Al_Ij_J1".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[leo saa] LT",nextDay:"[kesho saa] LT",nextWeek:"[wiki ijayo] dddd [saat] LT",lastDay:"[jana] LT",lastWeek:"[wiki iliyopita] dddd [saat] LT",sameElse:"L"},relativeTime:{future:"%s baadaye",past:"tokea %s",s:"hivi punde",m:"dakika moja",mm:"dakika %d",h:"saa limoja",hh:"masaa %d",d:"siku moja",dd:"masiku %d",M:"mwezi mmoja",MM:"miezi %d",y:"mwaka mmoja",yy:"miaka %d"},week:{dow:1,doy:7}});
//! moment.js locale configuration
//! locale : tamil (ta)
//! author : Arjunkumar Krishnamoorthy : https://github.com/tk120404
var vtt={"1":"௧","2":"௨","3":"௩","4":"௪","5":"௫","6":"௬","7":"௭","8":"௮","9":"௯","0":"௦"},ytt={"௧":"1","௨":"2","௩":"3","௪":"4","௫":"5","௬":"6","௭":"7","௮":"8","௯":"9","௦":"0"},urt=n.defineLocale("ta",{months:"ஜனவரி_பிப்ரவரி_மார்ச்_ஏப்ரல்_மே_ஜூன்_ஜூலை_ஆகஸ்ட்_செப்டெம்பர்_அக்டோபர்_நவம்பர்_டிசம்பர்".split("_"),monthsShort:"ஜனவரி_பிப்ரவரி_மார்ச்_ஏப்ரல்_மே_ஜூன்_ஜூலை_ஆகஸ்ட்_செப்டெம்பர்_அக்டோபர்_நவம்பர்_டிசம்பர்".split("_"),weekdays:"ஞாயிற்றுக்கிழமை_திங்கட்கிழமை_செவ்வாய்கிழமை_புதன்கிழமை_வியாழக்கிழமை_வெள்ளிக்கிழமை_சனிக்கிழமை".split("_"),weekdaysShort:"ஞாயிறு_திங்கள்_செவ்வாய்_புதன்_வியாழன்_வெள்ளி_சனி".split("_"),weekdaysMin:"ஞா_தி_செ_பு_வி_வெ_ச".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, HH:mm",LLLL:"dddd, D MMMM YYYY, HH:mm"},calendar:{sameDay:"[இன்று] LT",nextDay:"[நாளை] LT",nextWeek:"dddd, LT",lastDay:"[நேற்று] LT",lastWeek:"[கடந்த வாரம்] dddd, LT",sameElse:"L"},relativeTime:{future:"%s இல்",past:"%s முன்",s:"ஒரு சில விநாடிகள்",m:"ஒரு நிமிடம்",mm:"%d நிமிடங்கள்",h:"ஒரு மணி நேரம்",hh:"%d மணி நேரம்",d:"ஒரு நாள்",dd:"%d நாட்கள்",M:"ஒரு மாதம்",MM:"%d மாதங்கள்",y:"ஒரு வருடம்",yy:"%d ஆண்டுகள்"},ordinalParse:/\d{1,2}வது/,ordinal:function(n){return n+"வது"},preparse:function(n){return n.replace(/[௧௨௩௪௫௬௭௮௯௦]/g,function(n){return ytt[n]})},postformat:function(n){return n.replace(/\d/g,function(n){return vtt[n]})},meridiemParse:/யாமம்|வைகறை|காலை|நண்பகல்|எற்பாடு|மாலை/,meridiem:function(n){return n<2?" யாமம்":n<6?" வைகறை":n<10?" காலை":n<14?" நண்பகல்":n<18?" எற்பாடு":n<22?" மாலை":" யாமம்"},meridiemHour:function(n,t){return n===12&&(n=0),t==="யாமம்"?n<2?n:n+12:t==="வைகறை"||t==="காலை"?n:t==="நண்பகல்"?n>=10?n:n+12:n+12},week:{dow:0,doy:6}});
//! moment.js locale configuration
//! locale : telugu (te)
//! author : Krishna Chaitanya Thota : https://github.com/kcthota
ptt=n.defineLocale("te",{months:"జనవరి_ఫిబ్రవరి_మార్చి_ఏప్రిల్_మే_జూన్_జూలై_ఆగస్టు_సెప్టెంబర్_అక్టోబర్_నవంబర్_డిసెంబర్".split("_"),monthsShort:"జన._ఫిబ్ర._మార్చి_ఏప్రి._మే_జూన్_జూలై_ఆగ._సెప్._అక్టో._నవ._డిసె.".split("_"),weekdays:"ఆదివారం_సోమవారం_మంగళవారం_బుధవారం_గురువారం_శుక్రవారం_శనివారం".split("_"),weekdaysShort:"ఆది_సోమ_మంగళ_బుధ_గురు_శుక్ర_శని".split("_"),weekdaysMin:"ఆ_సో_మం_బు_గు_శు_శ".split("_"),longDateFormat:{LT:"A h:mm",LTS:"A h:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, A h:mm",LLLL:"dddd, D MMMM YYYY, A h:mm"},calendar:{sameDay:"[నేడు] LT",nextDay:"[రేపు] LT",nextWeek:"dddd, LT",lastDay:"[నిన్న] LT",lastWeek:"[గత] dddd, LT",sameElse:"L"},relativeTime:{future:"%s లో",past:"%s క్రితం",s:"కొన్ని క్షణాలు",m:"ఒక నిమిషం",mm:"%d నిమిషాలు",h:"ఒక గంట",hh:"%d గంటలు",d:"ఒక రోజు",dd:"%d రోజులు",M:"ఒక నెల",MM:"%d నెలలు",y:"ఒక సంవత్సరం",yy:"%d సంవత్సరాలు"},ordinalParse:/\d{1,2}వ/,ordinal:"%dవ",meridiemParse:/రాత్రి|ఉదయం|మధ్యాహ్నం|సాయంత్రం/,meridiemHour:function(n,t){return(n===12&&(n=0),t==="రాత్రి")?n<4?n:n+12:t==="ఉదయం"?n:t==="మధ్యాహ్నం"?n>=10?n:n+12:t==="సాయంత్రం"?n+12:void 0},meridiem:function(n){return n<4?"రాత్రి":n<10?"ఉదయం":n<17?"మధ్యాహ్నం":n<20?"సాయంత్రం":"రాత్రి"},week:{dow:0,doy:6}});
//! moment.js locale configuration
//! locale : thai (th)
//! author : Kridsada Thanabulpong : https://github.com/sirn
wtt=n.defineLocale("th",{months:"มกราคม_กุมภาพันธ์_มีนาคม_เมษายน_พฤษภาคม_มิถุนายน_กรกฎาคม_สิงหาคม_กันยายน_ตุลาคม_พฤศจิกายน_ธันวาคม".split("_"),monthsShort:"มกรา_กุมภา_มีนา_เมษา_พฤษภา_มิถุนา_กรกฎา_สิงหา_กันยา_ตุลา_พฤศจิกา_ธันวา".split("_"),weekdays:"อาทิตย์_จันทร์_อังคาร_พุธ_พฤหัสบดี_ศุกร์_เสาร์".split("_"),weekdaysShort:"อาทิตย์_จันทร์_อังคาร_พุธ_พฤหัส_ศุกร์_เสาร์".split("_"),weekdaysMin:"อา._จ._อ._พ._พฤ._ศ._ส.".split("_"),longDateFormat:{LT:"H นาฬิกา m นาที",LTS:"H นาฬิกา m นาที s วินาที",L:"YYYY/MM/DD",LL:"D MMMM YYYY",LLL:"D MMMM YYYY เวลา H นาฬิกา m นาที",LLLL:"วันddddที่ D MMMM YYYY เวลา H นาฬิกา m นาที"},meridiemParse:/ก่อนเที่ยง|หลังเที่ยง/,isPM:function(n){return n==="หลังเที่ยง"},meridiem:function(n){return n<12?"ก่อนเที่ยง":"หลังเที่ยง"},calendar:{sameDay:"[วันนี้ เวลา] LT",nextDay:"[พรุ่งนี้ เวลา] LT",nextWeek:"dddd[หน้า เวลา] LT",lastDay:"[เมื่อวานนี้ เวลา] LT",lastWeek:"[วัน]dddd[ที่แล้ว เวลา] LT",sameElse:"L"},relativeTime:{future:"อีก %s",past:"%sที่แล้ว",s:"ไม่กี่วินาที",m:"1 นาที",mm:"%d นาที",h:"1 ชั่วโมง",hh:"%d ชั่วโมง",d:"1 วัน",dd:"%d วัน",M:"1 เดือน",MM:"%d เดือน",y:"1 ปี",yy:"%d ปี"}});
//! moment.js locale configuration
//! locale : Tagalog/Filipino (tl-ph)
//! author : Dan Hagman
btt=n.defineLocale("tl-ph",{months:"Enero_Pebrero_Marso_Abril_Mayo_Hunyo_Hulyo_Agosto_Setyembre_Oktubre_Nobyembre_Disyembre".split("_"),monthsShort:"Ene_Peb_Mar_Abr_May_Hun_Hul_Ago_Set_Okt_Nob_Dis".split("_"),weekdays:"Linggo_Lunes_Martes_Miyerkules_Huwebes_Biyernes_Sabado".split("_"),weekdaysShort:"Lin_Lun_Mar_Miy_Huw_Biy_Sab".split("_"),weekdaysMin:"Li_Lu_Ma_Mi_Hu_Bi_Sab".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"MM/D/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY HH:mm",LLLL:"dddd, MMMM DD, YYYY HH:mm"},calendar:{sameDay:"[Ngayon sa] LT",nextDay:"[Bukas sa] LT",nextWeek:"dddd [sa] LT",lastDay:"[Kahapon sa] LT",lastWeek:"dddd [huling linggo] LT",sameElse:"L"},relativeTime:{future:"sa loob ng %s",past:"%s ang nakalipas",s:"ilang segundo",m:"isang minuto",mm:"%d minuto",h:"isang oras",hh:"%d oras",d:"isang araw",dd:"%d araw",M:"isang buwan",MM:"%d buwan",y:"isang taon",yy:"%d taon"},ordinalParse:/\d{1,2}/,ordinal:function(n){return n},week:{dow:1,doy:4}});
//! moment.js locale configuration
//! locale : Klingon (tlh)
//! author : Dominika Kruk : https://github.com/amaranthrose
hf="pagh_wa’_cha’_wej_loS_vagh_jav_Soch_chorgh_Hut".split("_");nit=n.defineLocale("tlh",{months:"tera’ jar wa’_tera’ jar cha’_tera’ jar wej_tera’ jar loS_tera’ jar vagh_tera’ jar jav_tera’ jar Soch_tera’ jar chorgh_tera’ jar Hut_tera’ jar wa’maH_tera’ jar wa’maH wa’_tera’ jar wa’maH cha’".split("_"),monthsShort:"jar wa’_jar cha’_jar wej_jar loS_jar vagh_jar jav_jar Soch_jar chorgh_jar Hut_jar wa’maH_jar wa’maH wa’_jar wa’maH cha’".split("_"),weekdays:"lojmItjaj_DaSjaj_povjaj_ghItlhjaj_loghjaj_buqjaj_ghInjaj".split("_"),weekdaysShort:"lojmItjaj_DaSjaj_povjaj_ghItlhjaj_loghjaj_buqjaj_ghInjaj".split("_"),weekdaysMin:"lojmItjaj_DaSjaj_povjaj_ghItlhjaj_loghjaj_buqjaj_ghInjaj".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[DaHjaj] LT",nextDay:"[wa’leS] LT",nextWeek:"LLL",lastDay:"[wa’Hu’] LT",lastWeek:"LLL",sameElse:"L"},relativeTime:{future:ktt,past:dtt,s:"puS lup",m:"wa’ tup",mm:ru,h:"wa’ rep",hh:ru,d:"wa’ jaj",dd:ru,M:"wa’ jar",MM:ru,y:"wa’ DIS",yy:ru},ordinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});
//! moment.js locale configuration
//! locale : turkish (tr)
//! authors : Erhan Gundogan : https://github.com/erhangundogan,
//!           Burak Yiğit Kaya: https://github.com/BYK
cf={1:"'inci",5:"'inci",8:"'inci",70:"'inci",80:"'inci",2:"'nci",7:"'nci",20:"'nci",50:"'nci",3:"'üncü",4:"'üncü",100:"'üncü",6:"'ncı",9:"'uncu",10:"'uncu",30:"'uncu",60:"'ıncı",90:"'ıncı"};tit=n.defineLocale("tr",{months:"Ocak_Şubat_Mart_Nisan_Mayıs_Haziran_Temmuz_Ağustos_Eylül_Ekim_Kasım_Aralık".split("_"),monthsShort:"Oca_Şub_Mar_Nis_May_Haz_Tem_Ağu_Eyl_Eki_Kas_Ara".split("_"),weekdays:"Pazar_Pazartesi_Salı_Çarşamba_Perşembe_Cuma_Cumartesi".split("_"),weekdaysShort:"Paz_Pts_Sal_Çar_Per_Cum_Cts".split("_"),weekdaysMin:"Pz_Pt_Sa_Ça_Pe_Cu_Ct".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[bugün saat] LT",nextDay:"[yarın saat] LT",nextWeek:"[haftaya] dddd [saat] LT",lastDay:"[dün] LT",lastWeek:"[geçen hafta] dddd [saat] LT",sameElse:"L"},relativeTime:{future:"%s sonra",past:"%s önce",s:"birkaç saniye",m:"bir dakika",mm:"%d dakika",h:"bir saat",hh:"%d saat",d:"bir gün",dd:"%d gün",M:"bir ay",MM:"%d ay",y:"bir yıl",yy:"%d yıl"},ordinalParse:/\d{1,2}'(inci|nci|üncü|ncı|uncu|ıncı)/,ordinal:function(n){if(n===0)return n+"'ıncı";var t=n%10,i=n%100-t,r=n>=100?100:null;return n+(cf[t]||cf[i]||cf[r])},week:{dow:1,doy:7}});
//! moment.js locale configuration
//! locale : talossan (tzl)
//! author : Robin van der Vliet : https://github.com/robin0van0der0v with the help of Iustì Canun
iit=n.defineLocale("tzl",{months:"Januar_Fevraglh_Març_Avrïu_Mai_Gün_Julia_Guscht_Setemvar_Listopäts_Noemvar_Zecemvar".split("_"),monthsShort:"Jan_Fev_Mar_Avr_Mai_Gün_Jul_Gus_Set_Lis_Noe_Zec".split("_"),weekdays:"Súladi_Lúneçi_Maitzi_Márcuri_Xhúadi_Viénerçi_Sáturi".split("_"),weekdaysShort:"Súl_Lún_Mai_Már_Xhú_Vié_Sát".split("_"),weekdaysMin:"Sú_Lú_Ma_Má_Xh_Vi_Sá".split("_"),longDateFormat:{LT:"HH.mm",LTS:"HH.mm.ss",L:"DD.MM.YYYY",LL:"D. MMMM [dallas] YYYY",LLL:"D. MMMM [dallas] YYYY HH.mm",LLLL:"dddd, [li] D. MMMM [dallas] YYYY HH.mm"},meridiem:function(n,t,i){return n>11?i?"d'o":"D'O":i?"d'a":"D'A"},calendar:{sameDay:"[oxhi à] LT",nextDay:"[demà à] LT",nextWeek:"dddd [à] LT",lastDay:"[ieiri à] LT",lastWeek:"[sür el] dddd [lasteu à] LT",sameElse:"L"},relativeTime:{future:"osprei %s",past:"ja%s",s:ot,m:ot,mm:ot,h:ot,hh:ot,d:ot,dd:ot,M:ot,MM:ot,y:ot,yy:ot},ordinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}});
//! moment.js locale configuration
//! locale : Morocco Central Atlas Tamaziɣt in Latin (tzm-latn)
//! author : Abdel Said : https://github.com/abdelsaid
rit=n.defineLocale("tzm-latn",{months:"innayr_brˤayrˤ_marˤsˤ_ibrir_mayyw_ywnyw_ywlywz_ɣwšt_šwtanbir_ktˤwbrˤ_nwwanbir_dwjnbir".split("_"),monthsShort:"innayr_brˤayrˤ_marˤsˤ_ibrir_mayyw_ywnyw_ywlywz_ɣwšt_šwtanbir_ktˤwbrˤ_nwwanbir_dwjnbir".split("_"),weekdays:"asamas_aynas_asinas_akras_akwas_asimwas_asiḍyas".split("_"),weekdaysShort:"asamas_aynas_asinas_akras_akwas_asimwas_asiḍyas".split("_"),weekdaysMin:"asamas_aynas_asinas_akras_akwas_asimwas_asiḍyas".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[asdkh g] LT",nextDay:"[aska g] LT",nextWeek:"dddd [g] LT",lastDay:"[assant g] LT",lastWeek:"dddd [g] LT",sameElse:"L"},relativeTime:{future:"dadkh s yan %s",past:"yan %s",s:"imik",m:"minuḍ",mm:"%d minuḍ",h:"saɛa",hh:"%d tassaɛin",d:"ass",dd:"%d ossan",M:"ayowr",MM:"%d iyyirn",y:"asgas",yy:"%d isgasn"},week:{dow:6,doy:12}});
//! moment.js locale configuration
//! locale : Morocco Central Atlas Tamaziɣt (tzm)
//! author : Abdel Said : https://github.com/abdelsaid
uit=n.defineLocale("tzm",{months:"ⵉⵏⵏⴰⵢⵔ_ⴱⵕⴰⵢⵕ_ⵎⴰⵕⵚ_ⵉⴱⵔⵉⵔ_ⵎⴰⵢⵢⵓ_ⵢⵓⵏⵢⵓ_ⵢⵓⵍⵢⵓⵣ_ⵖⵓⵛⵜ_ⵛⵓⵜⴰⵏⴱⵉⵔ_ⴽⵟⵓⴱⵕ_ⵏⵓⵡⴰⵏⴱⵉⵔ_ⴷⵓⵊⵏⴱⵉⵔ".split("_"),monthsShort:"ⵉⵏⵏⴰⵢⵔ_ⴱⵕⴰⵢⵕ_ⵎⴰⵕⵚ_ⵉⴱⵔⵉⵔ_ⵎⴰⵢⵢⵓ_ⵢⵓⵏⵢⵓ_ⵢⵓⵍⵢⵓⵣ_ⵖⵓⵛⵜ_ⵛⵓⵜⴰⵏⴱⵉⵔ_ⴽⵟⵓⴱⵕ_ⵏⵓⵡⴰⵏⴱⵉⵔ_ⴷⵓⵊⵏⴱⵉⵔ".split("_"),weekdays:"ⴰⵙⴰⵎⴰⵙ_ⴰⵢⵏⴰⵙ_ⴰⵙⵉⵏⴰⵙ_ⴰⴽⵔⴰⵙ_ⴰⴽⵡⴰⵙ_ⴰⵙⵉⵎⵡⴰⵙ_ⴰⵙⵉⴹⵢⴰⵙ".split("_"),weekdaysShort:"ⴰⵙⴰⵎⴰⵙ_ⴰⵢⵏⴰⵙ_ⴰⵙⵉⵏⴰⵙ_ⴰⴽⵔⴰⵙ_ⴰⴽⵡⴰⵙ_ⴰⵙⵉⵎⵡⴰⵙ_ⴰⵙⵉⴹⵢⴰⵙ".split("_"),weekdaysMin:"ⴰⵙⴰⵎⴰⵙ_ⴰⵢⵏⴰⵙ_ⴰⵙⵉⵏⴰⵙ_ⴰⴽⵔⴰⵙ_ⴰⴽⵡⴰⵙ_ⴰⵙⵉⵎⵡⴰⵙ_ⴰⵙⵉⴹⵢⴰⵙ".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},calendar:{sameDay:"[ⴰⵙⴷⵅ ⴴ] LT",nextDay:"[ⴰⵙⴽⴰ ⴴ] LT",nextWeek:"dddd [ⴴ] LT",lastDay:"[ⴰⵚⴰⵏⵜ ⴴ] LT",lastWeek:"dddd [ⴴ] LT",sameElse:"L"},relativeTime:{future:"ⴷⴰⴷⵅ ⵙ ⵢⴰⵏ %s",past:"ⵢⴰⵏ %s",s:"ⵉⵎⵉⴽ",m:"ⵎⵉⵏⵓⴺ",mm:"%d ⵎⵉⵏⵓⴺ",h:"ⵙⴰⵄⴰ",hh:"%d ⵜⴰⵙⵙⴰⵄⵉⵏ",d:"ⴰⵙⵙ",dd:"%d oⵙⵙⴰⵏ",M:"ⴰⵢoⵓⵔ",MM:"%d ⵉⵢⵢⵉⵔⵏ",y:"ⴰⵙⴳⴰⵙ",yy:"%d ⵉⵙⴳⴰⵙⵏ"},week:{dow:6,doy:12}});
//! moment.js locale configuration
//! locale : ukrainian (uk)
//! author : zemlanin : https://github.com/zemlanin
//! Author : Menelion Elensúle : https://github.com/Oire
oit=n.defineLocale("uk",{months:{format:"січня_лютого_березня_квітня_травня_червня_липня_серпня_вересня_жовтня_листопада_грудня".split("_"),standalone:"січень_лютий_березень_квітень_травень_червень_липень_серпень_вересень_жовтень_листопад_грудень".split("_")},monthsShort:"січ_лют_бер_квіт_трав_черв_лип_серп_вер_жовт_лист_груд".split("_"),weekdays:eit,weekdaysShort:"нд_пн_вт_ср_чт_пт_сб".split("_"),weekdaysMin:"нд_пн_вт_ср_чт_пт_сб".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD.MM.YYYY",LL:"D MMMM YYYY р.",LLL:"D MMMM YYYY р., HH:mm",LLLL:"dddd, D MMMM YYYY р., HH:mm"},calendar:{sameDay:er("[Сьогодні "),nextDay:er("[Завтра "),lastDay:er("[Вчора "),nextWeek:er("[У] dddd ["),lastWeek:function(){switch(this.day()){case 0:case 3:case 5:case 6:return er("[Минулої] dddd [").call(this);case 1:case 2:case 4:return er("[Минулого] dddd [").call(this)}},sameElse:"L"},relativeTime:{future:"за %s",past:"%s тому",s:"декілька секунд",m:fr,mm:fr,h:"годину",hh:fr,d:"день",dd:fr,M:"місяць",MM:fr,y:"рік",yy:fr},meridiemParse:/ночі|ранку|дня|вечора/,isPM:function(n){return/^(дня|вечора)$/.test(n)},meridiem:function(n){return n<4?"ночі":n<12?"ранку":n<17?"дня":"вечора"},ordinalParse:/\d{1,2}-(й|го)/,ordinal:function(n,t){switch(t){case"M":case"d":case"DDD":case"w":case"W":return n+"-й";case"D":return n+"-го";default:return n}},week:{dow:1,doy:7}});
//! moment.js locale configuration
//! locale : uzbek (uz)
//! author : Sardor Muminov : https://github.com/muminoff
sit=n.defineLocale("uz",{months:"январ_феврал_март_апрел_май_июн_июл_август_сентябр_октябр_ноябр_декабр".split("_"),monthsShort:"янв_фев_мар_апр_май_июн_июл_авг_сен_окт_ноя_дек".split("_"),weekdays:"Якшанба_Душанба_Сешанба_Чоршанба_Пайшанба_Жума_Шанба".split("_"),weekdaysShort:"Якш_Душ_Сеш_Чор_Пай_Жум_Шан".split("_"),weekdaysMin:"Як_Ду_Се_Чо_Па_Жу_Ша".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"D MMMM YYYY, dddd HH:mm"},calendar:{sameDay:"[Бугун соат] LT [да]",nextDay:"[Эртага] LT [да]",nextWeek:"dddd [куни соат] LT [да]",lastDay:"[Кеча соат] LT [да]",lastWeek:"[Утган] dddd [куни соат] LT [да]",sameElse:"L"},relativeTime:{future:"Якин %s ичида",past:"Бир неча %s олдин",s:"фурсат",m:"бир дакика",mm:"%d дакика",h:"бир соат",hh:"%d соат",d:"бир кун",dd:"%d кун",M:"бир ой",MM:"%d ой",y:"бир йил",yy:"%d йил"},week:{dow:1,doy:7}});
//! moment.js locale configuration
//! locale : vietnamese (vi)
//! author : Bang Nguyen : https://github.com/bangnk
hit=n.defineLocale("vi",{months:"tháng 1_tháng 2_tháng 3_tháng 4_tháng 5_tháng 6_tháng 7_tháng 8_tháng 9_tháng 10_tháng 11_tháng 12".split("_"),monthsShort:"Th01_Th02_Th03_Th04_Th05_Th06_Th07_Th08_Th09_Th10_Th11_Th12".split("_"),weekdays:"chủ nhật_thứ hai_thứ ba_thứ tư_thứ năm_thứ sáu_thứ bảy".split("_"),weekdaysShort:"CN_T2_T3_T4_T5_T6_T7".split("_"),weekdaysMin:"CN_T2_T3_T4_T5_T6_T7".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM [năm] YYYY",LLL:"D MMMM [năm] YYYY HH:mm",LLLL:"dddd, D MMMM [năm] YYYY HH:mm",l:"DD/M/YYYY",ll:"D MMM YYYY",lll:"D MMM YYYY HH:mm",llll:"ddd, D MMM YYYY HH:mm"},calendar:{sameDay:"[Hôm nay lúc] LT",nextDay:"[Ngày mai lúc] LT",nextWeek:"dddd [tuần tới lúc] LT",lastDay:"[Hôm qua lúc] LT",lastWeek:"dddd [tuần rồi lúc] LT",sameElse:"L"},relativeTime:{future:"%s tới",past:"%s trước",s:"vài giây",m:"một phút",mm:"%d phút",h:"một giờ",hh:"%d giờ",d:"một ngày",dd:"%d ngày",M:"một tháng",MM:"%d tháng",y:"một năm",yy:"%d năm"},ordinalParse:/\d{1,2}/,ordinal:function(n){return n},week:{dow:1,doy:4}});
//! moment.js locale configuration
//! locale : chinese (zh-cn)
//! author : suupic : https://github.com/suupic
//! author : Zeno Zeng : https://github.com/zenozeng
cit=n.defineLocale("zh-cn",{months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"周日_周一_周二_周三_周四_周五_周六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),longDateFormat:{LT:"Ah点mm分",LTS:"Ah点m分s秒",L:"YYYY-MM-DD",LL:"YYYY年MMMD日",LLL:"YYYY年MMMD日Ah点mm分",LLLL:"YYYY年MMMD日ddddAh点mm分",l:"YYYY-MM-DD",ll:"YYYY年MMMD日",lll:"YYYY年MMMD日Ah点mm分",llll:"YYYY年MMMD日ddddAh点mm分"},meridiemParse:/凌晨|早上|上午|中午|下午|晚上/,meridiemHour:function(n,t){return n===12&&(n=0),t==="凌晨"||t==="早上"||t==="上午"?n:t==="下午"||t==="晚上"?n+12:n>=11?n:n+12},meridiem:function(n,t){var i=n*100+t;return i<600?"凌晨":i<900?"早上":i<1130?"上午":i<1230?"中午":i<1800?"下午":"晚上"},calendar:{sameDay:function(){return this.minutes()===0?"[今天]Ah[点整]":"[今天]LT"},nextDay:function(){return this.minutes()===0?"[明天]Ah[点整]":"[明天]LT"},lastDay:function(){return this.minutes()===0?"[昨天]Ah[点整]":"[昨天]LT"},nextWeek:function(){var i,t;return i=n().startOf("week"),t=this.unix()-i.unix()>=604800?"[下]":"[本]",this.minutes()===0?t+"dddAh点整":t+"dddAh点mm"},lastWeek:function(){var i,t;return i=n().startOf("week"),t=this.unix()<i.unix()?"[上]":"[本]",this.minutes()===0?t+"dddAh点整":t+"dddAh点mm"},sameElse:"LL"},ordinalParse:/\d{1,2}(日|月|周)/,ordinal:function(n,t){switch(t){case"d":case"D":case"DDD":return n+"日";case"M":return n+"月";case"w":case"W":return n+"周";default:return n}},relativeTime:{future:"%s内",past:"%s前",s:"几秒",m:"1 分钟",mm:"%d 分钟",h:"1 小时",hh:"%d 小时",d:"1 天",dd:"%d 天",M:"1 个月",MM:"%d 个月",y:"1 年",yy:"%d 年"},week:{dow:1,doy:4}});
//! moment.js locale configuration
//! locale : traditional chinese (zh-tw)
//! author : Ben : https://github.com/ben-lin
return lit=n.defineLocale("zh-tw",{months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"週日_週一_週二_週三_週四_週五_週六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),longDateFormat:{LT:"Ah點mm分",LTS:"Ah點m分s秒",L:"YYYY年MMMD日",LL:"YYYY年MMMD日",LLL:"YYYY年MMMD日Ah點mm分",LLLL:"YYYY年MMMD日ddddAh點mm分",l:"YYYY年MMMD日",ll:"YYYY年MMMD日",lll:"YYYY年MMMD日Ah點mm分",llll:"YYYY年MMMD日ddddAh點mm分"},meridiemParse:/早上|上午|中午|下午|晚上/,meridiemHour:function(n,t){return(n===12&&(n=0),t==="早上"||t==="上午")?n:t==="中午"?n>=11?n:n+12:t==="下午"||t==="晚上"?n+12:void 0},meridiem:function(n,t){var i=n*100+t;return i<900?"早上":i<1130?"上午":i<1230?"中午":i<1800?"下午":"晚上"},calendar:{sameDay:"[今天]LT",nextDay:"[明天]LT",nextWeek:"[下]ddddLT",lastDay:"[昨天]LT",lastWeek:"[上]ddddLT",sameElse:"L"},ordinalParse:/\d{1,2}(日|月|週)/,ordinal:function(n,t){switch(t){case"d":case"D":case"DDD":return n+"日";case"M":return n+"月";case"w":case"W":return n+"週";default:return n}},relativeTime:{future:"%s內",past:"%s前",s:"幾秒",m:"一分鐘",mm:"%d分鐘",h:"一小時",hh:"%d小時",d:"一天",dd:"%d天",M:"一個月",MM:"%d個月",y:"一年",yy:"%d年"}}),no=n,no.locale("en"),no}),function(n,t){typeof define=="function"&&define.amd?define(["moment"],function(i){return n.moment=t(i),n.moment}):typeof exports=="object"?module.exports=t(require("moment")):n.moment=t(n.moment)}(this,function(n){function ft(n,t){return function(i){return u(n.call(this,i),t)}}function pt(n,t){return function(i){return this.localeData().ordinal(n.call(this,i),t)}}function p(n,t){for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i]);return n}function u(n,t){for(var i=n+"";i.length<t;)i="0"+i;return i}function wt(n){return Object.prototype.toString.call(n)==="[object Array]"}function l(n){return n?yt[n]||n.toLowerCase().replace(/(.)s$/,"$1"):n}function s(n,t,i,r){var u=n._isUTC?"UTC":"";n._d["set"+u+"FullYear"](t);n._d["set"+u+"Month"](i);n._d["set"+u+"Date"](r)}function et(n){function t(){}return t.prototype=n,new t}function bt(n){return Object.getPrototypeOf?Object.getPrototypeOf(n):"".__proto__?n.__proto__:n.constructor.prototype}function kt(n){for(var i=n.match(v),u=i.length,t=0;t<u;t+=1)r[i[t]]&&(i[t]=r[i[t]]);return function(r){var f="";for(t=0;t<u;t+=1)f+=i[t]instanceof Function?"["+i[t].call(r,n)+"]":i[t];return f}}function st(t,i){switch(t){case"iDDDD":return g;case"iYYYY":return nt;case"iYYYYY":return tt;case"iDDD":return d;case"iMMM":case"iMMMM":return it;case"iMM":case"iDD":case"iYY":case"iM":case"iD":return k;case"DDDD":return g;case"YYYY":return nt;case"YYYYY":return tt;case"S":case"SS":case"SSS":case"DDD":return d;case"MMM":case"MMMM":case"dd":case"ddd":case"dddd":return it;case"a":case"A":return n.localeData(i._l)._meridiemParse;case"X":return vt;case"Z":case"ZZ":return lt;case"T":return at;case"MM":case"DD":case"YY":case"HH":case"hh":case"mm":case"ss":case"M":case"D":case"d":case"H":case"h":case"m":case"s":return k;default:return new RegExp(t.replace("\\",""))}}function dt(t,i,r){var f,u=r._a;switch(t){case"iM":case"iMM":u[1]=i==null?0:~~i-1;break;case"iMMM":case"iMMMM":f=n.localeData(r._l).iMonthsParse(i);f!=null?u[1]=f:r._isValid=!1;break;case"iD":case"iDD":case"iDDD":case"iDDDD":i!=null&&(u[2]=~~i);break;case"iYY":u[0]=~~i+(~~i>47?1300:1400);break;case"iYYYY":case"iYYYYY":u[0]=~~i}i==null&&(r._isValid=!1)}function gt(n){var i,o,u=n._a[0],f=n._a[1],r=n._a[2];return u==null&&f==null&&r==null?[0,0,1]:(u=u||0,f=f||0,r=r||1,(r<1||r>t.iDaysInMonth(u,f))&&(n._isValid=!1),i=h(u,f,r),o=e(i.gy,i.gm,i.gd),n._hDiff=0,~~o.hy!==u&&(n._hDiff+=1),~~o.hm!==f&&(n._hDiff+=1),~~o.hd!==r&&(n._hDiff+=1),[i.gy,i.gm,i.gd])}function ni(n){var e=n._f.match(v),t=n._i,o=e.length,u,f,i;for(n._a=[],u=0;u<o;u+=1)f=e[u],i=(st(f,n).exec(t)||[])[0],i&&(t=t.slice(t.indexOf(i)+i.length)),r[f]&&dt(f,i,n);return t&&(n._il=t),gt(n)}function ti(n,t){var e=n._f.length,u,o,i,s,r,f;if(e===0)return a(new Date(NaN));for(u=0;u<e;u+=1)(o=n._f[u],r=0,i=a(n._i,o,n._l,t),i.isValid())&&(r+=i._hDiff,i._il&&(r+=i._il.length),(f==null||r<f)&&(f=r,s=i));return s}function ii(n){for(var i=n._i,e="",o="",s=n._f.match(v),h=s.length,f,t,u=0;u<h;u+=1)f=s[u],t=(st(f,n).exec(i)||[])[0],t&&(i=i.slice(i.indexOf(t)+t.length)),r[f]instanceof Function||(o+=f,t&&(e+=t));n._i=e;n._f=o}function ht(n,i,r){var e=r-i,u=r-n.day(),f;return u>e&&(u-=7),u<e-7&&(u+=7),f=t(n).add(u,"d"),{week:Math.ceil(f.iDayOfYear()/7),year:f.iYear()}}function a(i,r,f,e){var o={_i:i,_f:r,_l:f},s,h,c;if(r){if(wt(r))return ti(o,e);s=ni(o);ii(o);r="YYYY-MM-DD-"+o._f;i=u(s[0],4)+"-"+u(s[1]+1,2)+"-"+u(s[2],2)+"-"+o._i}return h=e?n.utc(i,r,f):n(i,r,f),o._isValid===!1&&(h._isValid=!1),h._hDiff=o._hDiff||0,c=et(t.fn),p(c,h),c}function t(n,t,i){return a(n,t,i,!1)}function e(n,t,i){var r=ui(fi(n,t+1,i));return r.hm-=1,r}function h(n,t,i){var r=ei(ri(n,t+1,i));return r.gm-=1,r}function i(n,t){return~~(n/t)}function c(n,t){return n-~~(n/t)*t}function ri(n,t,i){var r=ct(n,t),u=i+o.ummalquraData[r-1]-1;return u+24e5}function ui(n){var t=n-24e5,i=oi(t),r=i+16260,u=Math.floor((r-1)/12),f=u+1,e=r-12*u,s=t-o.ummalquraData[i-1]+1;return{hy:f,hm:e,hd:s}}function fi(n,t,r){var u=i((n+i(t-8,6)+100100)*1461,4)+i(153*c(t+9,12)+2,5)+r-34840408;return u-i(i(n+100100+i(t-8,6),100)*3,4)+752}function ei(n){var t,r,f,u,e;return t=4*n+139361631,t=t+i(i(4*n+183187720,146097)*3,4)*4-3908,r=i(c(t,1461),4)*5+308,f=i(c(r,153),5)+1,u=c(i(r,153),12)+1,e=i(t,1461)-100100+i(8-u,6),{gy:e,gm:u,gd:f}}function ct(n,t){var i=n-1,r=i*12+1+(t-1);return r-16260}function oi(n){for(var t=0;t<o.ummalquraData.length;t=t+1)if(o.ummalquraData[t]>n)return t}var w,ot;if(n==null)throw new Error("Cannot find moment");for(var o={ummalquraData:[28607,28636,28665,28695,28724,28754,28783,28813,28843,28872,28901,28931,28960,28990,29019,29049,29078,29108,29137,29167,29196,29226,29255,29285,29315,29345,29375,29404,29434,29463,29492,29522,29551,29580,29610,29640,29669,29699,29729,29759,29788,29818,29847,29876,29906,29935,29964,29994,30023,30053,30082,30112,30141,30171,30200,30230,30259,30289,30318,30348,30378,30408,30437,30467,30496,30526,30555,30585,30614,30644,30673,30703,30732,30762,30791,30821,30850,30880,30909,30939,30968,30998,31027,31057,31086,31116,31145,31175,31204,31234,31263,31293,31322,31352,31381,31411,31441,31471,31500,31530,31559,31589,31618,31648,31676,31706,31736,31766,31795,31825,31854,31884,31913,31943,31972,32002,32031,32061,32090,32120,32150,32180,32209,32239,32268,32298,32327,32357,32386,32416,32445,32475,32504,32534,32563,32593,32622,32652,32681,32711,32740,32770,32799,32829,32858,32888,32917,32947,32976,33006,33035,33065,33094,33124,33153,33183,33213,33243,33272,33302,33331,33361,33390,33420,33450,33479,33509,33539,33568,33598,33627,33657,33686,33716,33745,33775,33804,33834,33863,33893,33922,33952,33981,34011,34040,34069,34099,34128,34158,34187,34217,34247,34277,34306,34336,34365,34395,34424,34454,34483,34512,34542,34571,34601,34631,34660,34690,34719,34749,34778,34808,34837,34867,34896,34926,34955,34985,35015,35044,35074,35103,35133,35162,35192,35222,35251,35280,35310,35340,35370,35399,35429,35458,35488,35517,35547,35576,35605,35635,35665,35694,35723,35753,35782,35811,35841,35871,35901,35930,35960,35989,36019,36048,36078,36107,36136,36166,36195,36225,36254,36284,36314,36343,36373,36403,36433,36462,36492,36521,36551,36580,36610,36639,36669,36698,36728,36757,36786,36816,36845,36875,36904,36934,36963,36993,37022,37052,37081,37111,37141,37170,37200,37229,37259,37288,37318,37347,37377,37406,37436,37465,37495,37524,37554,37584,37613,37643,37672,37701,37731,37760,37790,37819,37849,37878,37908,37938,37967,37997,38027,38056,38085,38115,38144,38174,38203,38233,38262,38292,38322,38351,38381,38410,38440,38469,38499,38528,38558,38587,38617,38646,38676,38705,38735,38764,38794,38823,38853,38882,38912,38941,38971,39001,39030,39059,39089,39118,39148,39178,39208,39237,39267,39297,39326,39355,39385,39414,39444,39473,39503,39532,39562,39592,39621,39650,39680,39709,39739,39768,39798,39827,39857,39886,39916,39946,39975,40005,40035,40064,40094,40123,40153,40182,40212,40241,40271,40300,40330,40359,40389,40418,40448,40477,40507,40536,40566,40595,40625,40655,40685,40714,40744,40773,40803,40832,40862,40892,40921,40951,40980,41009,41039,41068,41098,41127,41157,41186,41216,41245,41275,41304,41334,41364,41393,41422,41452,41481,41511,41540,41570,41599,41629,41658,41688,41718,41748,41777,41807,41836,41865,41894,41924,41953,41983,42012,42042,42072,42102,42131,42161,42190,42220,42249,42279,42308,42337,42367,42397,42426,42456,42485,42515,42545,42574,42604,42633,42662,42692,42721,42751,42780,42810,42839,42869,42899,42929,42958,42988,43017,43046,43076,43105,43135,43164,43194,43223,43253,43283,43312,43342,43371,43401,43430,43460,43489,43519,43548,43578,43607,43637,43666,43696,43726,43755,43785,43814,43844,43873,43903,43932,43962,43991,44021,44050,44080,44109,44139,44169,44198,44228,44258,44287,44317,44346,44375,44405,44434,44464,44493,44523,44553,44582,44612,44641,44671,44700,44730,44759,44788,44818,44847,44877,44906,44936,44966,44996,45025,45055,45084,45114,45143,45172,45202,45231,45261,45290,45320,45350,45380,45409,45439,45468,45498,45527,45556,45586,45615,45644,45674,45704,45733,45763,45793,45823,45852,45882,45911,45940,45970,45999,46028,46058,46088,46117,46147,46177,46206,46236,46265,46295,46324,46354,46383,46413,46442,46472,46501,46531,46560,46590,46620,46649,46679,46708,46738,46767,46797,46826,46856,46885,46915,46944,46974,47003,47033,47063,47092,47122,47151,47181,47210,47240,47269,47298,47328,47357,47387,47417,47446,47476,47506,47535,47565,47594,47624,47653,47682,47712,47741,47771,47800,47830,47860,47890,47919,47949,47978,48008,48037,48066,48096,48125,48155,48184,48214,48244,48273,48303,48333,48362,48392,48421,48450,48480,48509,48538,48568,48598,48627,48657,48687,48717,48746,48776,48805,48834,48864,48893,48922,48952,48982,49011,49041,49071,49100,49130,49160,49189,49218,49248,49277,49306,49336,49365,49395,49425,49455,49484,49514,49543,49573,49602,49632,49661,49690,49720,49749,49779,49809,49838,49868,49898,49927,49957,49986,50016,50045,50075,50104,50133,50163,50192,50222,50252,50281,50311,50340,50370,50400,50429,50459,50488,50518,50547,50576,50606,50635,50665,50694,50724,50754,50784,50813,50843,50872,50902,50931,50960,50990,51019,51049,51078,51108,51138,51167,51197,51227,51256,51286,51315,51345,51374,51403,51433,51462,51492,51522,51552,51582,51611,51641,51670,51699,51729,51758,51787,51816,51846,51876,51906,51936,51965,51995,52025,52054,52083,52113,52142,52171,52200,52230,52260,52290,52319,52349,52379,52408,52438,52467,52497,52526,52555,52585,52614,52644,52673,52703,52733,52762,52792,52822,52851,52881,52910,52939,52969,52998,53028,53057,53087,53116,53146,53176,53205,53235,53264,53294,53324,53353,53383,53412,53441,53471,53500,53530,53559,53589,53619,53648,53678,53708,53737,53767,53796,53825,53855,53884,53913,53943,53973,54003,54032,54062,54092,54121,54151,54180,54209,54239,54268,54297,54327,54357,54387,54416,54446,54476,54505,54535,54564,54593,54623,54652,54681,54711,54741,54770,54800,54830,54859,54889,54919,54948,54977,55007,55036,55066,55095,55125,55154,55184,55213,55243,55273,55302,55332,55361,55391,55420,55450,55479,55508,55538,55567,55597,55627,55657,55686,55716,55745,55775,55804,55834,55863,55892,55922,55951,55981,56011,56040,56070,56100,56129,56159,56188,56218,56247,56276,56306,56335,56365,56394,56424,56454,56483,56513,56543,56572,56601,56631,56660,56690,56719,56749,56778,56808,56837,56867,56897,56926,56956,56985,57015,57044,57074,57103,57133,57162,57192,57221,57251,57280,57310,57340,57369,57399,57429,57458,57487,57517,57546,57576,57605,57634,57664,57694,57723,57753,57783,57813,57842,57871,57901,57930,57959,57989,58018,58048,58077,58107,58137,58167,58196,58226,58255,58285,58314,58343,58373,58402,58432,58461,58491,58521,58551,58580,58610,58639,58669,58698,58727,58757,58786,58816,58845,58875,58905,58934,58964,58994,59023,59053,59082,59111,59141,59170,59200,59229,59259,59288,59318,59348,59377,59407,59436,59466,59495,59525,59554,59584,59613,59643,59672,59702,59731,59761,59791,59820,59850,59879,59909,59939,59968,59997,60027,60056,60086,60115,60145,60174,60204,60234,60264,60293,60323,60352,60381,60411,60440,60469,60499,60528,60558,60588,60618,60648,60677,60707,60736,60765,60795,60824,60853,60883,60912,60942,60972,61002,61031,61061,61090,61120,61149,61179,61208,61237,61267,61296,61326,61356,61385,61415,61445,61474,61504,61533,61563,61592,61621,61651,61680,61710,61739,61769,61799,61828,61858,61888,61917,61947,61976,62006,62035,62064,62094,62123,62153,62182,62212,62242,62271,62301,62331,62360,62390,62419,62448,62478,62507,62537,62566,62596,62625,62655,62685,62715,62744,62774,62803,62832,62862,62891,62921,62950,62980,63009,63039,63069,63099,63128,63157,63187,63216,63246,63275,63305,63334,63363,63393,63423,63453,63482,63512,63541,63571,63600,63630,63659,63689,63718,63747,63777,63807,63836,63866,63895,63925,63955,63984,64014,64043,64073,64102,64131,64161,64190,64220,64249,64279,64309,64339,64368,64398,64427,64457,64486,64515,64545,64574,64603,64633,64663,64692,64722,64752,64782,64811,64841,64870,64899,64929,64958,64987,65017,65047,65076,65106,65136,65166,65195,65225,65254,65283,65313,65342,65371,65401,65431,65460,65490,65520,65549,65579,65608,65638,65667,65697,65726,65755,65785,65815,65844,65874,65903,65933,65963,65992,66022,66051,66081,66110,66140,66169,66199,66228,66258,66287,66317,66346,66376,66405,66435,66465,66494,66524,66553,66583,66612,66641,66671,66700,66730,66760,66789,66819,66849,66878,66908,66937,66967,66996,67025,67055,67084,67114,67143,67173,67203,67233,67262,67292,67321,67351,67380,67409,67439,67468,67497,67527,67557,67587,67617,67646,67676,67705,67735,67764,67793,67823,67852,67882,67911,67941,67971,68e3,68030,68060,68089,68119,68148,68177,68207,68236,68266,68295,68325,68354,68384,68414,68443,68473,68502,68532,68561,68591,68620,68650,68679,68708,68738,68768,68797,68827,68857,68886,68916,68946,68975,69004,69034,69063,69092,69122,69152,69181,69211,69240,69270,69300,69330,69359,69388,69418,69447,69476,69506,69535,69565,69595,69624,69654,69684,69713,69743,69772,69802,69831,69861,69890,69919,69949,69978,70008,70038,70067,70097,70126,70156,70186,70215,70245,70274,70303,70333,70362,70392,70421,70451,70481,70510,70540,70570,70599,70629,70658,70687,70717,70746,70776,70805,70835,70864,70894,70924,70954,70983,71013,71042,71071,71101,71130,71159,71189,71218,71248,71278,71308,71337,71367,71397,71426,71455,71485,71514,71543,71573,71602,71632,71662,71691,71721,71751,71781,71810,71839,71869,71898,71927,71957,71986,72016,72046,72075,72105,72135,72164,72194,72223,72253,72282,72311,72341,72370,72400,72429,72459,72489,72518,72548,72577,72607,72637,72666,72695,72725,72754,72784,72813,72843,72872,72902,72931,72961,72991,73020,73050,73080,73109,73139,73168,73197,73227,73256,73286,73315,73345,73375,73404,73434,73464,73493,73523,73552,73581,73611,73640,73669,73699,73729,73758,73788,73818,73848,73877,73907,73936,73965,73995,74024,74053,74083,74113,74142,74172,74202,74231,74261,74291,74320,74349,74379,74408,74437,74467,74497,74526,74556,74586,74615,74645,74675,74704,74733,74763,74792,74822,74851,74881,74910,74940,74969,74999,75029,75058,75088,75117,75147,75176,75206,75235,75264,75294,75323,75353,75383,75412,75442,75472,75501,75531,75560,75590,75619,75648,75678,75707,75737,75766,75796,75826,75856,75885,75915,75944,75974,76003,76032,76062,76091,76121,76150,76180,76210,76239,76269,76299,76328,76358,76387,76416,76446,76475,76505,76534,76564,76593,76623,76653,76682,76712,76741,76771,76801,76830,76859,76889,76918,76948,76977,77007,77036,77066,77096,77125,77155,77185,77214,77243,77273,77302,77332,77361,77390,77420,77450,77479,77509,77539,77569,77598,77627,77657,77686,77715,77745,77774,77804,77833,77863,77893,77923,77952,77982,78011,78041,78070,78099,78129,78158,78188,78217,78247,78277,78307,78336,78366,78395,78425,78454,78483,78513,78542,78572,78601,78631,78661,78690,78720,78750,78779,78808,78838,78867,78897,78926,78956,78985,79015,79044,79074,79104,79133,79163,79192,79222,79251,79281,79310,79340,79369,79399,79428,79458,79487,79517,79546,79576,79606,79635,79665,79695,79724,79753,79783,79812,79841,79871,79900,79930,79960,79990]},v=/(\[[^\[]*\])|(\\)?i(Mo|MM?M?M?|Do|DDDo|DD?D?D?|w[o|w]?|YYYYY|YYYY|YY|gg(ggg?)?)|(\\)?(Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|YYYYY|YYYY|YY|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|mm?|ss?|SS?S?|X|zz?|ZZ?|.)/g,b=/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g,k=/\d\d?/,d=/\d{1,3}/,g=/\d{3}/,nt=/\d{1,4}/,tt=/[+\-]?\d{1,6}/,it=/[0-9]*['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+(\.?)|[\u0600-\u06FF\/]+(\s*?[\u0600-\u06FF]+){1,2}/i,lt=/Z|[\+\-]\d\d:?\d\d/i,at=/T/i,vt=/[\+\-]?\d+(\.\d{1,3})?/,yt={hd:"idate",hm:"imonth",hy:"iyear"},y={},rt="DDD w M D".split(" "),ut="M D w".split(" "),r={iM:function(){return this.iMonth()+1},iMMM:function(n){return this.localeData().iMonthsShort(this,n)},iMMMM:function(n){return this.localeData().iMonths(this,n)},iD:function(){return this.iDate()},iDDD:function(){return this.iDayOfYear()},iw:function(){return this.iWeek()},iYY:function(){return u(this.iYear()%100,2)},iYYYY:function(){return u(this.iYear(),4)},iYYYYY:function(){return u(this.iYear(),5)},igg:function(){return u(this.iWeekYear()%100,2)},igggg:function(){return this.iWeekYear()},iggggg:function(){return u(this.iWeekYear(),5)}},f;rt.length;)f=rt.pop(),r["i"+f+"o"]=pt(r["i"+f],f);while(ut.length)f=ut.pop(),r["i"+f+f]=ft(r["i"+f],2);return r.iDDDD=ft(r.iDDD,3),p(bt(n.localeData()),{_iMonths:["Muharram","Safar","Rabi' al-Awwal","Rabi' al-Thani","Jumada al-Ula","Jumada al-Alkhirah","Rajab","Sha’ban","Ramadhan","Shawwal","Thul-Qi’dah","Thul-Hijjah"],iMonths:function(n){return this._iMonths[n.iMonth()]},_iMonthsShort:["Muh","Saf","Rab-I","Rab-II","Jum-I","Jum-II","Raj","Sha","Ram","Shw","Dhu-Q","Dhu-H"],iMonthsShort:function(n){return this._iMonthsShort[n.iMonth()]},iMonthsParse:function(n){var i,r,u;for(this._iMonthsParse||(this._iMonthsParse=[]),i=0;i<12;i+=1)if(this._iMonthsParse[i]||(r=t([2e3,(2+i)%12,25]),u="^"+this.iMonths(r,"")+"$|^"+this.iMonthsShort(r,"")+"$",this._iMonthsParse[i]=new RegExp(u.replace(".",""),"i")),this._iMonthsParse[i].test(n))return i}}),w={iMonths:"محرم_صفر_ربيع الأول_ربيع الثاني_جمادى الأولى_جمادى الآخرة_رجب_شعبان_رمضان_شوال_ذو القعدة_ذو الحجة".split("_"),iMonthsShort:"محرم_صفر_ربيع ١_ربيع ٢_جمادى ١_جمادى ٢_رجب_شعبان_رمضان_شوال_ذو القعدة_ذو الحجة".split("_")},typeof n.updateLocale=="function"?n.updateLocale("ar-sa",w):(ot=n.locale(),n.defineLocale("ar-sa",w),n.locale(ot)),p(t,n),t.fn=et(n.fn),t.utc=function(n,t,i){return a(n,t,i,!0)},t.fn.format=function(t){var i,r,u=this;if(t){for(i=5,r=function(n){return u.localeData().longDateFormat(n)||n};i>0&&b.test(t);)i-=1,t=t.replace(b,r);y[t]||(y[t]=kt(t));t=y[t](this)}return n.fn.format.call(this,t)},t.fn.iYear=function(i){var f,u,r;return typeof i=="number"?(u=e(this.year(),this.month(),this.date()),f=Math.min(u.hd,t.iDaysInMonth(i,u.hm)),r=h(i,u.hm,f),s(this,r.gy,r.gm,r.gd),(this.month()!==r.gm||this.date()!==r.gd||this.year()!==r.gy)&&s(this,r.gy,r.gm,r.gd),n.updateOffset(this),this):e(this.year(),this.month(),this.date()).hy},t.fn.iMonth=function(r){var o,f,u;if(r!=null){if(typeof r=="string")if(r=this.localeData().iMonthsParse(r),r>=0)r-=1;else return this;return f=e(this.year(),this.month(),this.date()),o=Math.min(f.hd,t.iDaysInMonth(f.hy,r)),this.iYear(f.hy+i(r,12)),r=c(r,12),r<0&&(r+=12,this.iYear(this.iYear()-1)),u=h(this.iYear(),r,o),s(this,u.gy,u.gm,u.gd),(this.month()!==u.gm||this.date()!==u.gd||this.year()!==u.gy)&&s(this,u.gy,u.gm,u.gd),n.updateOffset(this),this}return e(this.year(),this.month(),this.date()).hm},t.fn.iDate=function(t){var r,i;return typeof t=="number"?(r=e(this.year(),this.month(),this.date()),i=h(r.hy,r.hm,t),s(this,i.gy,i.gm,i.gd),(this.month()!==i.gm||this.date()!==i.gd||this.year()!==i.gy)&&s(this,i.gy,i.gm,i.gd),n.updateOffset(this),this):e(this.year(),this.month(),this.date()).hd},t.fn.iDayOfYear=function(n){var i=Math.round((t(this).startOf("day")-t(this).startOf("iYear"))/864e5)+1;return n==null?i:this.add(n-i,"d")},t.fn.iDaysInMonth=function(){return parseInt(t(this).endOf("iMonth").format("iDD"))},t.fn.iWeek=function(n){var t=ht(this,this.localeData()._week.dow,this.localeData()._week.doy).week;return n==null?t:this.add((n-t)*7,"d")},t.fn.iWeekYear=function(n){var t=ht(this,this.localeData()._week.dow,this.localeData()._week.doy).year;return n==null?t:this.add(n-t,"y")},t.fn.add=function(t,i){var r;return i===null||isNaN(+i)||(r=t,t=i,i=r),i=l(i),i==="iyear"?this.iYear(this.iYear()+t):i==="imonth"?this.iMonth(this.iMonth()+t):i==="idate"?this.iDate(this.iDate()+t):n.fn.add.call(this,t,i),this},t.fn.subtract=function(t,i){var r;return i===null||isNaN(+i)||(r=t,t=i,i=r),i=l(i),i==="iyear"?this.iYear(this.iYear()-t):i==="imonth"?this.iMonth(this.iMonth()-t):i==="idate"?this.iDate(this.iDate()-t):n.fn.subtract.call(this,t,i),this},t.fn.startOf=function(t){return t=l(t),t==="iyear"||t==="imonth"?(t==="iyear"&&this.iMonth(0),this.iDate(1),this.hours(0),this.minutes(0),this.seconds(0),this.milliseconds(0),this):n.fn.startOf.call(this,t)},t.fn.endOf=function(n){return(n=l(n),n===undefined||n==="milisecond")?this:this.startOf(n).add(1,n==="isoweek"?"week":n).subtract(1,"milliseconds")},t.fn.clone=function(){return t(this)},t.fn.iYears=t.fn.iYear,t.fn.iMonths=t.fn.iMonth,t.fn.iDates=t.fn.iDate,t.fn.iWeeks=t.fn.iWeek,t.iDaysInMonth=function(n,t){var i=ct(n,t+1);return o.ummalquraData[i]-o.ummalquraData[i-1]},t.iConvert={toHijri:e,toGregorian:h},t});
/*! version : 4.17.37
 =========================================================
 bootstrap-datetimejs
 
 https://github.com/Eonasdan/bootstrap-datetimepicker
 Modified by: @balbarak
 
 Copyright (c) 2015 Jonathan Peterson
 =========================================================
 */
(function(n){"use strict";if(typeof define=="function"&&define.amd)define(["jquery","moment"],n);else if(typeof exports=="object")n(require("jquery"),require("moment"));else{if(typeof jQuery=="undefined")throw"bootstrap-hijri-datepicker requires jQuery to be loaded first";if(typeof moment=="undefined")throw"bootstrap-hijri-datepicker requires Moment.js to be loaded first";n(jQuery,moment)}})(function(n,t){"use strict";if(!t)throw new Error("bootstrap-hijri-datepicker requires Moment.js to be loaded first");var i=function(i,r){var u={},o,f,k=!0,c,l=!1,e=!1,d,nt=0,y,g,b,ht=[{clsName:"days",navFnc:r.hijri?"iMonth":"Month",navStep:1},{clsName:"months",navFnc:r.hijri?"iYear":"y",navStep:1},{clsName:"years",navFnc:r.hijri?"iYear":"y",navStep:10},{clsName:"decades",navFnc:r.hijri?"iYear":"y",navStep:100}],yt=["days","months","years","decades"],bt=["top","bottom","auto"],kt=["left","right","auto"],dt=["default","top","bottom"],gt={up:38,38:"up",down:40,40:"down",left:37,37:"left",right:39,39:"right",tab:9,9:"tab",escape:27,27:"escape",enter:13,13:"enter",pageUp:33,33:"pageUp",pageDown:34,34:"pageDown",shift:16,16:"shift",control:17,17:"control",space:32,32:"space",t:84,84:"t","delete":46,46:"delete"},ct={},tt=function(n){var u=!1,i,f,e,o,s;return t.tz!==undefined&&r.timeZone!==undefined&&r.timeZone!==null&&r.timeZone!==""&&(u=!0),n===undefined||n===null?i=u?t().tz(r.timeZone).startOf("day"):t().startOf("day"):u?(f=t().tz(r.timeZone).utcOffset(),e=t(n,g,r.useStrict).utcOffset(),e!==f?(o=t().tz(r.timeZone).format("Z"),s=t(n,g,r.useStrict).format("YYYY-MM-DD[T]HH:mm:ss")+o,i=t(s,g,r.useStrict).tz(r.timeZone)):i=t(n,g,r.useStrict).tz(r.timeZone)):i=t(n,g,r.useStrict),i},p=function(n){if(typeof n!="string"||n.length>1)throw new TypeError("isEnabled expects a single character string parameter");switch(n){case"y":return y.indexOf("Y")!==-1;case"M":return y.indexOf("M")!==-1;case"d":return y.toLowerCase().indexOf("d")!==-1;case"h":case"H":return y.toLowerCase().indexOf("hh:")!==-1;case"m":return y.indexOf("m")!==-1;case"s":return y.indexOf("s")!==-1;default:return!1}},lt=function(){return p("h")||p("m")||p("s")},ft=function(){return p("y")||p("M")||p("d")},si=function(){var t=n("<thead>").append(n("<tr>").append(n("<th>").addClass("prev").attr("data-action","previous").append(n("<span>").html(r.icons.previous))).append(n("<th>").addClass("picker-switch").attr("data-action","pickerSwitch").attr("colspan",r.calendarWeeks?"6":"5")).append(n("<th>").addClass("next").attr("data-action","next").append(n("<span>").html(r.icons.next)))),i=n("<tbody>").append(n("<tr>").append(n("<td>").attr("colspan",r.calendarWeeks?"8":"7")));return[n("<div>").addClass("datepicker-days").append(n("<table>").addClass("table-condensed").append(t).append(n("<tbody>"))),n("<div>").addClass("datepicker-months").append(n("<table>").addClass("table-condensed").append(t.clone()).append(i.clone())),n("<div>").addClass("datepicker-years").append(n("<table>").addClass("table-condensed").append(t.clone()).append(i.clone())),n("<div>").addClass("datepicker-decades").append(n("<table>").addClass("table-condensed").append(t.clone()).append(i.clone()))]},hi=function(){var t=n("<tr>"),i=n("<tr>"),u=n("<tr>");return p("h")&&(t.append(n("<td>").append(n("<a>").attr({href:"#",tabindex:"-1",title:r.tooltips.incrementHour}).addClass("btn").attr("data-action","incrementHours").append(n("<span>").addClass(r.icons.up)))),i.append(n("<td>").append(n("<span>").addClass("timepicker-hour").attr({"data-time-component":"hours",title:r.tooltips.pickHour}).attr("data-action","showHours"))),u.append(n("<td>").append(n("<a>").attr({href:"#",tabindex:"-1",title:r.tooltips.decrementHour}).addClass("btn").attr("data-action","decrementHours").append(n("<span>").addClass(r.icons.down))))),p("m")&&(p("h")&&(t.append(n("<td>").addClass("separator")),i.append(n("<td>").addClass("separator").html(":")),u.append(n("<td>").addClass("separator"))),t.append(n("<td>").append(n("<a>").attr({href:"#",tabindex:"-1",title:r.tooltips.incrementMinute}).addClass("btn").attr("data-action","incrementMinutes").append(n("<span>").addClass(r.icons.up)))),i.append(n("<td>").append(n("<span>").addClass("timepicker-minute").attr({"data-time-component":"minutes",title:r.tooltips.pickMinute}).attr("data-action","showMinutes"))),u.append(n("<td>").append(n("<a>").attr({href:"#",tabindex:"-1",title:r.tooltips.decrementMinute}).addClass("btn").attr("data-action","decrementMinutes").append(n("<span>").addClass(r.icons.down))))),p("s")&&(p("m")&&(t.append(n("<td>").addClass("separator")),i.append(n("<td>").addClass("separator").html(":")),u.append(n("<td>").addClass("separator"))),t.append(n("<td>").append(n("<a>").attr({href:"#",tabindex:"-1",title:r.tooltips.incrementSecond}).addClass("btn").attr("data-action","incrementSeconds").append(n("<span>").addClass(r.icons.up)))),i.append(n("<td>").append(n("<span>").addClass("timepicker-second").attr({"data-time-component":"seconds",title:r.tooltips.pickSecond}).attr("data-action","showSeconds"))),u.append(n("<td>").append(n("<a>").attr({href:"#",tabindex:"-1",title:r.tooltips.decrementSecond}).addClass("btn").attr("data-action","decrementSeconds").append(n("<span>").addClass(r.icons.down))))),d||(t.append(n("<td>").addClass("separator")),i.append(n("<td>").append(n("<button>").addClass("btn btn-primary").attr({"data-action":"togglePeriod",tabindex:"-1",title:r.tooltips.togglePeriod}))),u.append(n("<td>").addClass("separator"))),n("<div>").addClass("timepicker-picker").append(n("<table>").addClass("table-condensed").append([t,i,u]))},ci=function(){var i=n("<div>").addClass("timepicker-hours").append(n("<table>").addClass("table-condensed")),r=n("<div>").addClass("timepicker-minutes").append(n("<table>").addClass("table-condensed")),u=n("<div>").addClass("timepicker-seconds").append(n("<table>").addClass("table-condensed")),t=[hi()];return p("h")&&t.push(i),p("m")&&t.push(r),p("s")&&t.push(u),t},li=function(){var t=[];return r.showTodayButton&&t.push(n("<td>").append(n("<a>").attr({"data-action":"today",title:r.tooltips.today}).append(n("<span>").html(r.icons.today)))),!r.sideBySide&&ft()&&lt()&&t.push(n("<td>").append(n("<a>").attr({"data-action":"togglePicker",title:r.tooltips.selectTime}).append(n("<span>").addClass(r.icons.time)))),r.showClear&&t.push(n("<td>").append(n("<a>").attr({"data-action":"clear",title:r.tooltips.clear}).append(n("<span>").html(r.icons.clear)))),r.showClose&&t.push(n("<td>").append(n("<a>").attr({"data-action":"close",title:r.tooltips.close}).append(n("<span>").html(r.icons.close)))),r.showSwitcher&&t.push(n("<td>").append(n("<a>").attr({"data-action":"switchDate",title:r.tooltips.close}).append(n("<span>").html("هجري/ميلادي")))),n("<table>").addClass("table-condensed").append(n("<tbody>").append(n("<tr>").append(t)))},ai=function(){var t=n("<div>").addClass("bootstrap-datetimepicker-widget dropdown-menu"),f=n("<div>").addClass("datepicker").append(si()),e=n("<div>").addClass("timepicker").append(ci()),i=n("<ul>").addClass("list-unstyled"),u=n("<li>").addClass("picker-switch"+(r.collapse?" accordion-toggle":"")).append(li());return(r.inline&&t.removeClass("dropdown-menu"),d&&t.addClass("usetwentyfour"),p("s")&&!d&&t.addClass("wider"),r.sideBySide&&ft()&&lt())?(t.addClass("timepicker-sbs"),r.toolbarPlacement==="top"&&t.append(u),t.append(n("<div>").addClass("row").append(f.addClass("col-md-6")).append(e.addClass("col-md-6"))),r.toolbarPlacement==="bottom"&&t.append(u),t):(r.toolbarPlacement==="top"&&i.append(u),ft()&&i.append(n("<li>").addClass(r.collapse&&lt()?"collapse in":"").append(f)),r.toolbarPlacement==="default"&&i.append(u),lt()&&i.append(n("<li>").addClass(r.collapse&&ft()?"collapse":"").append(e)),r.toolbarPlacement==="bottom"&&i.append(u),t.append(i))},vi=function(){var t,u={};return t=i.is("input")||r.inline?i.data():i.find("input").data(),t.dateOptions&&t.dateOptions instanceof Object&&(u=n.extend(!0,u,t.dateOptions)),n.each(r,function(n){var i="date"+n.charAt(0).toUpperCase()+n.slice(1);t[i]!==undefined&&(u[n]=t[i])}),u},pt=function(){var o=(l||i).position(),s=(l||i).offset(),u=r.widgetPositioning.vertical,f=r.widgetPositioning.horizontal,t;if(r.widgetParent)t=r.widgetParent.append(e);else if(i.is("input"))t=i.after(e).parent();else{if(r.inline){t=i.append(e);return}t=i;i.children().first().after(e)}if(u==="auto"&&(u=s.top+e.height()*1.5>=n(window).height()+n(window).scrollTop()&&e.height()+i.outerHeight()<s.top?"top":"bottom"),f==="auto"&&(f=t.width()<s.left+e.outerWidth()/2&&s.left+e.outerWidth()>n(window).width()?"right":"left"),u==="top"?e.addClass("top").removeClass("bottom"):e.addClass("bottom").removeClass("top"),f==="right"?e.addClass("pull-right"):e.removeClass("pull-right"),t.css("position")!=="relative"&&(t=t.parents().filter(function(){return n(this).css("position")==="relative"}).first()),t.length===0)throw new Error("datetimepicker component should be placed within a relative positioned container");e.css({top:u==="top"?"auto":o.top+i.outerHeight(),bottom:u==="top"?o.top+i.outerHeight():"auto",left:f==="left"?t===i?0:o.left:"auto",right:f==="left"?"auto":t.outerWidth()-i.outerWidth()-(t===i?0:o.left)})},et=function(n){(n.type!=="dp.change"||(!n.date||!n.date.isSame(n.oldDate))&&(n.date||n.oldDate))&&i.trigger(n)},it=function(n){n==="y"&&(n="YYYY");n==="M"&&(n="YYYY MMMM");et({type:"dp.update",change:n,viewDate:f.clone()})},ot=function(n){e&&(n&&(b=Math.max(nt,Math.min(2,b+n))),e.find(".datepicker > div").hide().filter(".datepicker-"+ht[b].clsName).show())},yi=function(){var t=n("<tr>"),i=f.clone().startOf("w").startOf("day");for(r.calendarWeeks===!0&&t.append(n("<th>").addClass("cw").text("#"));i.isBefore(f.clone().endOf("w"));)t.append(n("<th>").addClass("dow").text(i.format("dd"))),i.add(1,"days");e.find(".datepicker-days thead").append(t)},pi=function(n){return r.disabledDates[n.format("YYYY-MM-DD")]===!0},wi=function(n){return r.enabledDates[n.format("YYYY-MM-DD")]===!0},bi=function(n){return r.disabledHours[n.format("H")]===!0},ki=function(n){return r.enabledHours[n.format("H")]===!0},h=function(t,i){if(!t.isValid()||r.disabledDates&&i==="d"&&pi(t)||r.enabledDates&&i==="d"&&!wi(t)||r.minDate&&t.isBefore(r.minDate,i)||r.maxDate&&t.isAfter(r.maxDate,i)||r.daysOfWeekDisabled&&i==="d"&&r.daysOfWeekDisabled.indexOf(t.day())!==-1||r.disabledHours&&(i==="h"||i==="m"||i==="s")&&bi(t)||r.enabledHours&&(i==="h"||i==="m"||i==="s")&&!ki(t))return!1;if(r.disabledTimeIntervals&&(i==="h"||i==="m"||i==="s")){var u=!1;if(n.each(r.disabledTimeIntervals,function(){if(t.isBetween(this[0],this[1]))return u=!0,!1}),u)return!1}return!0},ni=function(){for(var i=[],t=f.clone().startOf("y").startOf("day");t.isSame(f,"years");)i.push(n("<span>").attr("data-action","selectMonth").addClass("month").text(t.format("MMM"))),t.add(1,"months");e.find(".datepicker-months td").empty().append(i)},ti=function(){for(var i=[],t=f.clone().startOf("hy").hour(12);t.iYear()===f.iYear();)i.push(n("<span>").attr("data-action","selectMonth").addClass("month").text(t.format("iMMM"))),t.add(1,"iMonth");e.find(".datepicker-months td").empty().append(i)},di=function(){var i=e.find(".datepicker-months"),t=i.find("th"),u=i.find("tbody").find("span");t.eq(0).find("span").attr("title",r.tooltips.prevYear);t.eq(1).attr("title",r.tooltips.selectYear);t.eq(2).find("span").attr("title",r.tooltips.nextYear);i.find(".disabled").removeClass("disabled");h(f.clone().subtract(1,"years"),"y")||t.eq(0).addClass("disabled");t.eq(1).text(f.iYear());h(f.clone().add(1,"y"),"y")||t.eq(2).addClass("disabled");u.removeClass("active");o.isSame(f,"y")&&u.eq(o.month()).addClass("active");u.each(function(t){h(f.clone().month(t),"M")||n(this).addClass("disabled")})},gi=function(){var i=e.find(".datepicker-months"),t=i.find("th"),u=i.find("tbody").find("span");t.eq(0).find("span").attr("title",r.tooltips.prevYear);t.eq(1).attr("title",r.tooltips.selectYear);t.eq(2).find("span").attr("title",r.tooltips.nextYear);i.find(".disabled").removeClass("disabled");h(f.clone().subtract(1,"years"),"y")||t.eq(0).addClass("disabled");t.eq(1).text(f.year());h(f.clone().add(1,"y"),"y")||t.eq(2).addClass("disabled");u.removeClass("active");o.isSame(f,"y")&&!k&&u.eq(o.month()).addClass("active");u.each(function(t){h(f.clone().month(t),"M")||n(this).addClass("disabled")})},nr=function(){var u=e.find(".datepicker-years"),t=u.find("th"),n=f.clone().subtract(5,"hy"),i=f.clone().add(6,"hy"),s="",c,l;for(t.eq(0).find("span").attr("title",r.tooltips.prevDecade),t.eq(1).attr("title",r.tooltips.selectDecade),t.eq(2).find("span").attr("title",r.tooltips.nextDecade),u.find(".disabled").removeClass("disabled"),r.minDate&&r.minDate.isAfter(n,"hy")&&t.eq(0).addClass("disabled"),t.eq(1).text(n.iYear()+"-"+i.iYear()),r.maxDate&&r.maxDate.isBefore(i,"hy")&&t.eq(2).addClass("disabled");!n.isAfter(i,"hy");){if(c=i.format("iYYYY"),l=n.format("iYYYY"),c==="1500"||l==="1355"){n=f.clone().subtract(5,"hy");s+='<span data-action="selectYear" class="year'+(n.iYear()===o.iYear()?" active":"")+(h(n,"hy")?"":" disabled")+'">'+n.iYear()+"<\/span>";break}s+='<span data-action="selectYear" class="year'+(n.iYear()===o.iYear()?" active":"")+(h(n,"hy")?"":" disabled")+'">'+n.iYear()+"<\/span>";n.add(1,"iYear")}u.find("td").html(s)},tr=function(){var i=e.find(".datepicker-years"),t=i.find("th"),n=f.clone().subtract(5,"y"),u=f.clone().add(6,"y"),s="";for(t.eq(0).find("span").attr("title",r.tooltips.prevDecade),t.eq(1).attr("title",r.tooltips.selectDecade),t.eq(2).find("span").attr("title",r.tooltips.nextDecade),i.find(".disabled").removeClass("disabled"),r.minDate&&r.minDate.isAfter(n,"y")&&t.eq(0).addClass("disabled"),t.eq(1).text(n.year()+"-"+u.year()),r.maxDate&&r.maxDate.isBefore(u,"y")&&t.eq(2).addClass("disabled");!n.isAfter(u,"y");)s+='<span data-action="selectYear" class="year'+(n.isSame(o,"y")&&!k?" active":"")+(h(n,"y")?"":" disabled")+'">'+n.year()+"<\/span>",n.add(1,"y");i.find("td").html(s)},hr=function(){var u=e.find(".datepicker-decades"),i=u.find("th"),n=t({y:f.year()-f.year()%100-1}),s=n.clone().add(100,"y"),l=n.clone(),c="";for(i.eq(0).find("span").attr("title",r.tooltips.prevCentury),i.eq(2).find("span").attr("title",r.tooltips.nextCentury),u.find(".disabled").removeClass("disabled"),(n.isSame(t({y:1900}))||r.minDate&&r.minDate.isAfter(n,"y"))&&i.eq(0).addClass("disabled"),i.eq(1).text(n.year()+"-"+s.year()),(n.isSame(t({y:2e3}))||r.maxDate&&r.maxDate.isBefore(s,"y"))&&i.eq(2).addClass("disabled");!n.isAfter(s,"y");)c+='<span data-action="selectDecade" class="decade'+(n.isSame(o,"y")?" active":"")+(h(n,"y")?"":" disabled")+'" data-selection="'+(n.year()+6)+'">'+(n.year()+1)+" - "+(n.year()+12)+"<\/span>",n.add(12,"y");c+="<span><\/span><span><\/span><span><\/span>";u.find("td").html(c);i.eq(1).text(l.year()+1+"-"+n.year())},ut=function(){if(r.hijri){at();return}var c=e.find(".datepicker-days"),u=c.find("th"),t,a=[],s,i,l;if(ft()){for(u.eq(0).find("span").attr("title",r.tooltips.prevMonth),u.eq(1).attr("title",r.tooltips.selectMonth),u.eq(2).find("span").attr("title",r.tooltips.nextMonth),c.find(".disabled").removeClass("disabled"),u.eq(1).text(f.format(r.dayViewHeaderFormat)),h(f.clone().subtract(1,"months"),"months")||u.eq(0).addClass("disabled"),h(f.clone().add(1,"months"),"months")||u.eq(2).addClass("disabled"),t=f.clone().startOf("months").startOf("weeks").startOf("days"),l=0;l<42;l++)t.weekday()===0&&(s=n("<tr>"),r.calendarWeeks&&s.append('<td class="cw">'+t.week()+"<\/td>"),a.push(s)),i="",t.isBefore(f,"months")&&(i+=" old"),t.isAfter(f,"months")&&(i+=" new"),t.isSame(o,"days")&&!k&&(i+=" active"),h(t,"days")||(i+=" disabled"),t.isSame(tt(),"days")&&(i+=" today"),(t.day()===6||t.day()===5)&&(i+=" weekend"),s.append('<td data-action="selectDay" data-day="'+t.format("L")+'" class="day'+i+'">'+t.date()+"<\/td>"),t.add(1,"days");c.find("tbody").empty().append(a);gi();tr()}},at=function(){var l=e.find(".datepicker-days"),s=l.find("th"),i,v=[],c,u,a;if(ft()){for(s.eq(0).find("span").attr("title",r.tooltips.prevMonth),s.eq(1).attr("title",r.tooltips.selectMonth),s.eq(2).find("span").attr("title",r.tooltips.nextMonth),l.find(".disabled").removeClass("disabled"),s.eq(1).text(f.format(r.hijriDayViewHeaderFormat)),h(f.clone().subtract(1,"iMonth"),"iMonth")||s.eq(0).addClass("disabled"),h(f.clone().add(1,"iMonth"),"iMonth")||s.eq(2).addClass("disabled"),i=f.clone().startOf("iMonth").startOf("week"),a=0;a<42;a++)i.weekday()===0&&(c=n("<tr>"),r.calendarWeeks&&c.append('<td class="cw">'+i.week()+"<\/td>"),v.push(c)),u="",i.iMonth()<f.iMonth()&&(u+=" old"),i.iMonth()>f.iMonth()&&(u+=" new"),i.isSame(o,"d")&&!k&&(u+=" active"),h(i,"d")||(u+=" disabled"),i.isSame(t(),"d")&&(u+=" today"),(i.day()===5||i.day()===6)&&(u+=" weekend"),c.append('<td data-action="selectDay" data-mday="'+i.date()+'" data-day="'+i.format("L")+'" class="day'+u+'">'+i.iDate()+"<\/td>"),i.add(1,"days");l.find("tbody").empty().append(v);di();nr()}},ir=function(){var u=e.find(".timepicker-hours table"),t=f.clone().startOf("day"),r=[],i=n("<tr>");for(f.hour()>11&&!d&&t.hour(12);t.isSame(f,"d")&&(d||f.hour()<12&&t.hour()<12||f.hour()>11);)t.hour()%4==0&&(i=n("<tr>"),r.push(i)),i.append('<td data-action="selectHour" class="hour'+(h(t,"h")?"":" disabled")+'">'+t.format(d?"HH":"hh")+"<\/td>"),t.add(1,"h");u.empty().append(r)},rr=function(){for(var s=e.find(".timepicker-minutes table"),t=f.clone().startOf("h"),u=[],i=n("<tr>"),o=r.stepping===1?5:r.stepping;f.isSame(t,"h");)t.minute()%(o*4)==0&&(i=n("<tr>"),u.push(i)),i.append('<td data-action="selectMinute" class="minute'+(h(t,"m")?"":" disabled")+'">'+t.format("mm")+"<\/td>"),t.add(o,"m");s.empty().append(u)},ur=function(){for(var u=e.find(".timepicker-seconds table"),t=f.clone().startOf("m"),r=[],i=n("<tr>");f.isSame(t,"m");)t.second()%20==0&&(i=n("<tr>"),r.push(i)),i.append('<td data-action="selectSecond" class="second'+(h(t,"s")?"":" disabled")+'">'+t.format("ss")+"<\/td>"),t.add(5,"s");u.empty().append(r)},fr=function(){var n,i,t=e.find(".timepicker span[data-time-component]");d||(n=e.find(".timepicker [data-action=togglePeriod]"),i=o.clone().add(o.hours()>=12?-12:12,"h"),n.text(o.format("A")),h(i,"h")?n.removeClass("disabled"):n.addClass("disabled"));t.filter("[data-time-component=hours]").text(o.format(d?"HH":"hh"));t.filter("[data-time-component=minutes]").text(o.format("mm"));t.filter("[data-time-component=seconds]").text(o.format("ss"));ir();rr();ur()},a=function(){e&&(ut(),fr())},s=function(n){var t=k?null:o;if(!n){k=!0;c.val("");i.data("date","");et({type:"dp.change",date:!1,oldDate:t});a();return}n=n.clone().locale(r.locale);r.stepping!==1&&n.minutes(Math.round(n.minutes()/r.stepping)*r.stepping%60).seconds(0);h(n)?(o=n,f=o.clone(),c.val(o.format(y)),i.data("date",o.format(y)),k=!1,a(),et({type:"dp.change",date:o.clone(),oldDate:t})):(r.keepInvalid||c.val(k?"":o.format(y)),et({type:"dp.error",date:n}))},v=function(){var t=!1;return e?(e.find(".collapse").each(function(){var i=n(this).data("collapse");return i&&i.transitioning?(t=!0,!1):!0}),t)?u:(l&&l.hasClass("btn")&&l.toggleClass("active"),e.hide(),n(window).off("resize",pt),e.off("click","[data-action]"),e.off("mousedown",!1),e.remove(),e=!1,et({type:"dp.hide",date:o.clone()}),c.blur(),u):u},ii=function(){s(null)},vt={next:function(){var n=ht[b].navFnc;f.add(ht[b].navStep,n);r.hijri?at():ut();it(n)},previous:function(){var n=ht[b].navFnc;f.subtract(ht[b].navStep,n);r.hijri?at():ut();it(n)},pickerSwitch:function(){ot(1)},selectMonth:function(t){var i=n(t.target).closest("tbody").find("span").index(n(t.target));r.hijri?f.iMonth(i):f.month(i);b===nt?(r.hijri?s(o.clone().year(f.iYear()).month(f.iMonth())):s(o.clone().year(f.year()).month(f.month())),r.inline||v()):(ot(-1),ut());r.hijri?it("iM"):it("M")},selectYear:function(t){var i=parseInt(n(t.target).text(),10)||0;r.hijri?f.iYear(i):f.year(i);b===nt?(r.hijri?s(o.clone().iYear(f.iYear())):s(o.clone().year(f.year())),r.inline||v()):(ot(-1),ut());r.hijri?it("hYYYY"):it("YYYY")},selectDecade:function(t){var i=parseInt(n(t.target).data("selection"),10)||0;r.hijri?f.iYear(i):f.year(i);b===nt?(r.hijri?s(o.clone().iYear(f.iYear())):s(o.clone().year(f.year())),r.inline||v()):(ot(-1),ut());r.hijri?it("hYYYY"):it("YYYY")},selectDay:function(t){var i=f.clone();r.hijri?(n(t.target).is(".old")&&i.subtract(1,"iMonth"),n(t.target).is(".new")&&i.add(1,"iMonth"),s(i.iDate(parseInt(n(t.target).text(),10)))):(n(t.target).is(".old")&&i.subtract(1,"months"),n(t.target).is(".new")&&i.add(1,"months"),s(i.date(parseInt(n(t.target).text(),10))));lt()||r.keepOpen||r.inline||v()},incrementHours:function(){var n=o.clone().add(1,"h");h(n,"h")&&s(n)},incrementMinutes:function(){var n=o.clone().add(r.stepping,"m");h(n,"m")&&s(n)},incrementSeconds:function(){var n=o.clone().add(1,"s");h(n,"s")&&s(n)},decrementHours:function(){var n=o.clone().subtract(1,"h");h(n,"h")&&s(n)},decrementMinutes:function(){var n=o.clone().subtract(r.stepping,"m");h(n,"m")&&s(n)},decrementSeconds:function(){var n=o.clone().subtract(1,"s");h(n,"s")&&s(n)},togglePeriod:function(){s(o.clone().add(o.hours()>=12?-12:12,"h"))},togglePicker:function(t){var u=n(t.target),e=u.closest("ul"),i=e.find(".in"),o=e.find(".collapse:not(.in)"),f;if(i&&i.length){if(f=i.data("collapse"),f&&f.transitioning)return;i.collapse?(i.collapse("hide"),o.collapse("show")):(i.removeClass("in"),o.addClass("in"));u.is("span")?u.toggleClass(r.icons.time+" "+r.icons.date):u.find("span").toggleClass(r.icons.time+" "+r.icons.date)}},showPicker:function(){e.find(".timepicker > div:not(.timepicker-picker)").hide();e.find(".timepicker .timepicker-picker").show()},showHours:function(){e.find(".timepicker .timepicker-picker").hide();e.find(".timepicker .timepicker-hours").show()},showMinutes:function(){e.find(".timepicker .timepicker-picker").hide();e.find(".timepicker .timepicker-minutes").show()},showSeconds:function(){e.find(".timepicker .timepicker-picker").hide();e.find(".timepicker .timepicker-seconds").show()},selectHour:function(t){var i=parseInt(n(t.target).text(),10);d||(o.hours()>=12?i!==12&&(i+=12):i===12&&(i=0));s(o.clone().hours(i));vt.showPicker.call(u)},selectMinute:function(t){s(o.clone().minutes(parseInt(n(t.target).text(),10)));vt.showPicker.call(u)},selectSecond:function(t){s(o.clone().seconds(parseInt(n(t.target).text(),10)));vt.showPicker.call(u)},clear:ii,today:function(){var n=tt();h(n,"d")&&s(n)},close:v,switchDate:function(){r.hijri?(r.hijri=!1,ut(),ni(),st()):(r.hijri=!0,at(),ti(),st())}},er=function(t){return n(t.currentTarget).is(".disabled")?!1:(vt[n(t.currentTarget).data("action")].apply(u,arguments),!1)},w=function(){var t,i={year:function(n){return n.month(0).date(1).hours(0).seconds(0).minutes(0)},month:function(n){return n.date(1).hours(0).seconds(0).minutes(0)},day:function(n){return n.hours(0).seconds(0).minutes(0)},hour:function(n){return n.seconds(0).minutes(0)},minute:function(n){return n.seconds(0)}};if(c.prop("disabled")||!r.ignoreReadonly&&c.prop("readonly")||e)return u;c.val()!==undefined&&c.val().trim().length!==0?s(rt(c.val().trim())):r.useCurrent&&k&&(c.is("input")&&c.val().trim().length===0||r.inline)&&(t=tt(),typeof r.useCurrent=="string"&&(t=i[r.useCurrent](t)),s(t));e=ai();yi();r.hijri?ti():ni();e.find(".timepicker-hours").hide();e.find(".timepicker-minutes").hide();e.find(".timepicker-seconds").hide();a();ot();n(window).on("resize",pt);e.on("click","[data-action]",er);e.on("mousedown",!1);return l&&l.hasClass("btn")&&l.toggleClass("active"),e.show(),pt(),r.focusOnShow&&!c.is(":focus")&&c.focus(),et({type:"dp.show"}),u},wt=function(){return e?v():w()},rt=function(n){return n=r.parseInputDate===undefined?t.isMoment(n)||n instanceof Date?t(n):tt(n):r.parseInputDate(n),n.locale(r.locale),n},ri=function(n){var o=null,t,f,c=[],l={},s=n.which,i,h,a="p";ct[s]=a;for(t in ct)ct.hasOwnProperty(t)&&ct[t]===a&&(c.push(t),parseInt(t,10)!==s&&(l[t]=!0));for(t in r.keyBinds)if(r.keyBinds.hasOwnProperty(t)&&typeof r.keyBinds[t]=="function"&&(i=t.split(" "),i.length===c.length&&gt[s]===i[i.length-1])){for(h=!0,f=i.length-2;f>=0;f--)if(!(gt[i[f]]in l)){h=!1;break}if(h){o=r.keyBinds[t];break}}o&&(o.call(u,e),n.stopPropagation(),n.preventDefault())},ui=function(n){ct[n.which]="r";n.stopPropagation();n.preventDefault()},fi=function(t){var i=n(t.target).val().trim(),r=i?rt(i):null;return s(r),t.stopImmediatePropagation(),!1},or=function(){c.on({change:fi,blur:r.debug?"":v,keydown:ri,keyup:ui,focus:r.allowInputToggle?w:""});if(i.is("input"))c.on({focus:w});else if(l){l.on("click",wt);l.on("mousedown",!1)}},sr=function(){c.off({change:fi,blur:blur,keydown:ri,keyup:ui,focus:r.allowInputToggle?v:""});i.is("input")?c.off({focus:w}):l&&(l.off("click",wt),l.off("mousedown",!1))},ei=function(t){var i={};return n.each(t,function(){var n=rt(this);n.isValid()&&(i[n.format("YYYY-MM-DD")]=!0)}),Object.keys(i).length?i:!1},oi=function(t){var i={};return n.each(t,function(){i[this]=!0}),Object.keys(i).length?i:!1},st=function(){var n=r.format||"L LT";r.hijri&&(n=r.hijriFormat);y=n.replace(/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g,function(n){var t=o.localeData().longDateFormat(n)||n;return t.replace(/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g,function(n){return o.localeData().longDateFormat(n)||n})});g=r.extraFormats?r.extraFormats.slice():[];g.indexOf(n)<0&&g.indexOf(y)<0&&g.push(y);d=y.toLowerCase().indexOf("a")<1&&y.replace(/\[.*?\]/g,"").indexOf("h")<1;p("y")&&(nt=2);p("M")&&(nt=1);p("d")&&(nt=0);b=Math.max(nt,b);k||s(o)};if(u.destroy=function(){v();sr();i.removeData("DateTimePicker");i.removeData("date")},u.toggle=wt,u.show=w,u.hide=v,u.disable=function(){return v(),l&&l.hasClass("btn")&&l.addClass("disabled"),c.prop("disabled",!0),u},u.enable=function(){return l&&l.hasClass("btn")&&l.removeClass("disabled"),c.prop("disabled",!1),u},u.ignoreReadonly=function(n){if(arguments.length===0)return r.ignoreReadonly;if(typeof n!="boolean")throw new TypeError("ignoreReadonly () expects a boolean parameter");return r.ignoreReadonly=n,u},u.options=function(t){if(arguments.length===0)return n.extend(!0,{},r);if(!(t instanceof Object))throw new TypeError("options() options parameter should be an object");return n.extend(!0,r,t),n.each(r,function(n,t){u[n]!==undefined&&u[n](t)}),u},u.date=function(n){if(arguments.length===0)return k?null:o.clone();if(n!==null&&typeof n!="string"&&!t.isMoment(n)&&!(n instanceof Date))throw new TypeError("date() parameter must be one of [null, string, moment or Date]");return s(n===null?null:rt(n)),u},u.format=function(n){if(arguments.length===0)return r.format;if(typeof n!="string"&&(typeof n!="boolean"||n!==!1))throw new TypeError("format() expects a sting or boolean:false parameter "+n);return r.format=n,y&&st(),u},u.hijriFormat=function(){},u.timeZone=function(n){return arguments.length===0?r.timeZone:(r.timeZone=n,u)},u.dayViewHeaderFormat=function(n){if(arguments.length===0)return r.dayViewHeaderFormat;if(typeof n!="string")throw new TypeError("dayViewHeaderFormat() expects a string parameter");return r.dayViewHeaderFormat=n,u},u.hijriDayViewHeaderFormat=function(){},u.extraFormats=function(n){if(arguments.length===0)return r.extraFormats;if(n!==!1&&!(n instanceof Array))throw new TypeError("extraFormats() expects an array or false parameter");return r.extraFormats=n,g&&st(),u},u.disabledDates=function(t){if(arguments.length===0)return r.disabledDates?n.extend({},r.disabledDates):r.disabledDates;if(!t)return r.disabledDates=!1,a(),u;if(!(t instanceof Array))throw new TypeError("disabledDates() expects an array parameter");return r.disabledDates=ei(t),r.enabledDates=!1,a(),u},u.enabledDates=function(t){if(arguments.length===0)return r.enabledDates?n.extend({},r.enabledDates):r.enabledDates;if(!t)return r.enabledDates=!1,a(),u;if(!(t instanceof Array))throw new TypeError("enabledDates() expects an array parameter");return r.enabledDates=ei(t),r.disabledDates=!1,a(),u},u.daysOfWeekDisabled=function(n){if(arguments.length===0)return r.daysOfWeekDisabled.splice(0);if(typeof n=="boolean"&&!n)return r.daysOfWeekDisabled=!1,a(),u;if(!(n instanceof Array))throw new TypeError("daysOfWeekDisabled() expects an array parameter");if(r.daysOfWeekDisabled=n.reduce(function(n,t){return(t=parseInt(t,10),t>6||t<0||isNaN(t))?n:(n.indexOf(t)===-1&&n.push(t),n)},[]).sort(),r.useCurrent&&!r.keepInvalid){for(var t=0;!h(o,"d");){if(o.add(1,"days"),t===7)throw"Tried 7 times to find a valid date";t++}s(o)}return a(),u},u.maxDate=function(n){if(arguments.length===0)return r.maxDate?r.maxDate.clone():r.maxDate;if(typeof n=="boolean"&&n===!1)return r.maxDate=!1,a(),u;typeof n=="string"&&(n==="now"||n==="moment")&&(n=tt());var t=rt(n);if(!t.isValid())throw new TypeError("maxDate() Could not parse date parameter: "+n);if(r.minDate&&t.isBefore(r.minDate))throw new TypeError("maxDate() date parameter is before options.minDate: "+t.format(y));return r.maxDate=t,r.useCurrent&&!r.keepInvalid&&o.isAfter(n)&&s(r.maxDate),f.isAfter(t)&&(f=t.clone().subtract(r.stepping,"m")),a(),u},u.minDate=function(n){if(arguments.length===0)return r.minDate?r.minDate.clone():r.minDate;if(typeof n=="boolean"&&n===!1)return r.minDate=!1,a(),u;typeof n=="string"&&(n==="now"||n==="moment")&&(n=tt());var t=rt(n);if(!t.isValid())throw new TypeError("minDate() Could not parse date parameter: "+n);if(r.maxDate&&t.isAfter(r.maxDate))throw new TypeError("minDate() date parameter is after options.maxDate: "+t.format(y));return r.minDate=t,r.useCurrent&&!r.keepInvalid&&o.isBefore(n)&&s(r.minDate),f.isBefore(t)&&(f=t.clone().add(r.stepping,"m")),a(),u},u.defaultDate=function(n){if(arguments.length===0)return r.defaultDate?r.defaultDate.clone():r.defaultDate;if(!n)return r.defaultDate=!1,u;typeof n=="string"&&(n==="now"||n==="moment")&&(n=tt());var t=rt(n);if(!t.isValid())throw new TypeError("defaultDate() Could not parse date parameter: "+n);if(!h(t))throw new TypeError("defaultDate() date passed is invalid according to component setup validations");return r.defaultDate=t,(r.defaultDate&&r.inline||c.val().trim()==="")&&s(r.defaultDate),u},u.hijri=function(n){if(arguments.length===0)return r.hijri&&(r.viewModes=["days","months","years"]),r.hijri;if(typeof n!="boolean")throw new TypeError("hijri() expects a boolean parameter");return r.hijri=n,r.hijri&&(r.viewModes=["days","months","years"]),u},u.isRTL=function(){return r.isRTL&&(r.icons.next=">>",r.icons.previous="<<"),r.isRTL},u.locale=function(n){if(arguments.length===0)return r.locale;if(!t.localeData(n))throw new TypeError("locale() locale "+n+" is not loaded from moment locales!");return r.locale=n,o.locale(r.locale),f.locale(r.locale),y&&st(),e&&(v(),w()),u},u.stepping=function(n){return arguments.length===0?r.stepping:(n=parseInt(n,10),(isNaN(n)||n<1)&&(n=1),r.stepping=n,u)},u.useCurrent=function(n){var t=["year","month","day","hour","minute"];if(arguments.length===0)return r.useCurrent;if(typeof n!="boolean"&&typeof n!="string")throw new TypeError("useCurrent() expects a boolean or string parameter");if(typeof n=="string"&&t.indexOf(n.toLowerCase())===-1)throw new TypeError("useCurrent() expects a string parameter of "+t.join(", "));return r.useCurrent=n,u},u.collapse=function(n){if(arguments.length===0)return r.collapse;if(typeof n!="boolean")throw new TypeError("collapse() expects a boolean parameter");return r.collapse===n?u:(r.collapse=n,e&&(v(),w()),u)},u.icons=function(t){if(arguments.length===0)return n.extend({},r.icons);if(!(t instanceof Object))throw new TypeError("icons() expects parameter to be an Object");return n.extend(r.icons,t),e&&(v(),w()),u},u.tooltips=function(t){if(arguments.length===0)return n.extend({},r.tooltips);if(!(t instanceof Object))throw new TypeError("tooltips() expects parameter to be an Object");return n.extend(r.tooltips,t),e&&(v(),w()),u},u.useStrict=function(n){if(arguments.length===0)return r.useStrict;if(typeof n!="boolean")throw new TypeError("useStrict() expects a boolean parameter");return r.useStrict=n,u},u.sideBySide=function(n){if(arguments.length===0)return r.sideBySide;if(typeof n!="boolean")throw new TypeError("sideBySide() expects a boolean parameter");return r.sideBySide=n,e&&(v(),w()),u},u.viewMode=function(n){if(arguments.length===0)return r.viewMode;if(typeof n!="string")throw new TypeError("viewMode() expects a string parameter");if(yt.indexOf(n)===-1)throw new TypeError("viewMode() parameter must be one of ("+yt.join(", ")+") value");return r.viewMode=n,b=Math.max(yt.indexOf(n),nt),ot(),u},u.toolbarPlacement=function(n){if(arguments.length===0)return r.toolbarPlacement;if(typeof n!="string")throw new TypeError("toolbarPlacement() expects a string parameter");if(dt.indexOf(n)===-1)throw new TypeError("toolbarPlacement() parameter must be one of ("+dt.join(", ")+") value");return r.toolbarPlacement=n,e&&(v(),w()),u},u.widgetPositioning=function(t){if(arguments.length===0)return n.extend({},r.widgetPositioning);if({}.toString.call(t)!=="[object Object]")throw new TypeError("widgetPositioning() expects an object variable");if(t.horizontal){if(typeof t.horizontal!="string")throw new TypeError("widgetPositioning() horizontal variable must be a string");if(t.horizontal=t.horizontal.toLowerCase(),kt.indexOf(t.horizontal)===-1)throw new TypeError("widgetPositioning() expects horizontal parameter to be one of ("+kt.join(", ")+")");r.widgetPositioning.horizontal=t.horizontal}if(t.vertical){if(typeof t.vertical!="string")throw new TypeError("widgetPositioning() vertical variable must be a string");if(t.vertical=t.vertical.toLowerCase(),bt.indexOf(t.vertical)===-1)throw new TypeError("widgetPositioning() expects vertical parameter to be one of ("+bt.join(", ")+")");r.widgetPositioning.vertical=t.vertical}return a(),u},u.calendarWeeks=function(n){if(arguments.length===0)return r.calendarWeeks;if(typeof n!="boolean")throw new TypeError("calendarWeeks() expects parameter to be a boolean value");return r.calendarWeeks=n,a(),u},u.showTodayButton=function(n){if(arguments.length===0)return r.showTodayButton;if(typeof n!="boolean")throw new TypeError("showTodayButton() expects a boolean parameter");return r.showTodayButton=n,e&&(v(),w()),u},u.showClear=function(n){if(arguments.length===0)return r.showClear;if(typeof n!="boolean")throw new TypeError("showClear() expects a boolean parameter");return r.showClear=n,e&&(v(),w()),u},u.widgetParent=function(t){if(arguments.length===0)return r.widgetParent;if(typeof t=="string"&&(t=n(t)),t!==null&&typeof t!="string"&&!(t instanceof n))throw new TypeError("widgetParent() expects a string or a jQuery object parameter");return r.widgetParent=t,e&&(v(),w()),u},u.keepOpen=function(n){if(arguments.length===0)return r.keepOpen;if(typeof n!="boolean")throw new TypeError("keepOpen() expects a boolean parameter");return r.keepOpen=n,u},u.focusOnShow=function(n){if(arguments.length===0)return r.focusOnShow;if(typeof n!="boolean")throw new TypeError("focusOnShow() expects a boolean parameter");return r.focusOnShow=n,u},u.inline=function(n){if(arguments.length===0)return r.inline;if(typeof n!="boolean")throw new TypeError("inline() expects a boolean parameter");return r.inline=n,u},u.clear=function(){return ii(),u},u.keyBinds=function(n){return r.keyBinds=n,u},u.getMoment=function(n){return tt(n)},u.debug=function(n){if(typeof n!="boolean")throw new TypeError("debug() expects a boolean parameter");return r.debug=n,u},u.allowInputToggle=function(n){if(arguments.length===0)return r.allowInputToggle;if(typeof n!="boolean")throw new TypeError("allowInputToggle() expects a boolean parameter");return r.allowInputToggle=n,u},u.showClose=function(n){if(arguments.length===0)return r.showClose;if(typeof n!="boolean")throw new TypeError("showClose() expects a boolean parameter");return r.showClose=n,u},u.showSwitcher=function(n){if(arguments.length===0)return r.showSwitcher;if(typeof n!="boolean")throw new TypeError("showClose() expects a boolean parameter");return r.showSwitcher=n,u},u.keepInvalid=function(n){if(arguments.length===0)return r.keepInvalid;if(typeof n!="boolean")throw new TypeError("keepInvalid() expects a boolean parameter");return r.keepInvalid=n,u},u.datepickerInput=function(n){if(arguments.length===0)return r.datepickerInput;if(typeof n!="string")throw new TypeError("datepickerInput() expects a string parameter");return r.datepickerInput=n,u},u.parseInputDate=function(n){if(arguments.length===0)return r.parseInputDate;if(typeof n!="function")throw new TypeError("parseInputDate() sholud be as function");return r.parseInputDate=n,u},u.disabledTimeIntervals=function(t){if(arguments.length===0)return r.disabledTimeIntervals?n.extend({},r.disabledTimeIntervals):r.disabledTimeIntervals;if(!t)return r.disabledTimeIntervals=!1,a(),u;if(!(t instanceof Array))throw new TypeError("disabledTimeIntervals() expects an array parameter");return r.disabledTimeIntervals=t,a(),u},u.disabledHours=function(t){if(arguments.length===0)return r.disabledHours?n.extend({},r.disabledHours):r.disabledHours;if(!t)return r.disabledHours=!1,a(),u;if(!(t instanceof Array))throw new TypeError("disabledHours() expects an array parameter");if(r.disabledHours=oi(t),r.enabledHours=!1,r.useCurrent&&!r.keepInvalid){for(var i=0;!h(o,"h");){if(o.add(1,"h"),i===24)throw"Tried 24 times to find a valid date";i++}s(o)}return a(),u},u.enabledHours=function(t){if(arguments.length===0)return r.enabledHours?n.extend({},r.enabledHours):r.enabledHours;if(!t)return r.enabledHours=!1,a(),u;if(!(t instanceof Array))throw new TypeError("enabledHours() expects an array parameter");if(r.enabledHours=oi(t),r.disabledHours=!1,r.useCurrent&&!r.keepInvalid){for(var i=0;!h(o,"h");){if(o.add(1,"h"),i===24)throw"Tried 24 times to find a valid date";i++}s(o)}return a(),u},u.viewDate=function(n){if(arguments.length===0)return f.clone();if(!n)return f=o.clone(),u;if(typeof n!="string"&&!t.isMoment(n)&&!(n instanceof Date))throw new TypeError("viewDate() parameter must be one of [string, moment or Date]");return f=rt(n),it(),u},i.is("input"))c=i;else if(c=i.find(r.datepickerInput),c.size()===0)c=i.find("input");else if(!c.is("input"))throw new Error('CSS class "'+r.datepickerInput+'" cannot be applied to non input element');if(i.hasClass("input-group")&&(l=i.find(".datepickerbutton").size()===0?i.find(".input-group-addon"):i.find(".datepickerbutton")),!r.inline&&!c.is("input"))throw new Error("Could not initialize DateTimePicker without an input element");return o=tt(),f=o.clone(),n.extend(!0,r,vi()),u.options(r),st(),or(),c.prop("disabled")&&u.disable(),c.is("input")&&c.val().trim().length!==0?s(rt(c.val().trim())):r.defaultDate&&c.attr("placeholder")===undefined&&s(r.defaultDate),r.inline&&w(),u};n.fn.hijriDatePicker=function(t){return this.each(function(){var r=n(this);r.data("HijriDatePicker")||(t=n.extend(!0,{},n.fn.hijriDatePicker.defaults,t),r.data("HijriDatePicker",i(r,t)))})};n.fn.hijriDatePicker.defaults={timeZone:"Etc/UTC",format:"DD-MM-YYYY",hijriFormat:"iYYYY-iMM-iDD",hijriDayViewHeaderFormat:"iMMMM iYYYY",dayViewHeaderFormat:"MMMM YYYY",minDate:"1950-01-01",maxDate:"2070-01-01",extraFormats:!1,stepping:1,useCurrent:!0,collapse:!0,locale:"ar-SA",defaultDate:!1,disabledDates:!1,enabledDates:!1,icons:{time:"fa fa-clock text-primary",date:"glyphicon glyphicon-calendar",up:"fa fa-chevron-up text-primary",down:"fa fa-chevron-down text-primary",previous:"<<",next:">>",today:"اليوم",clear:"مسح",close:"اغلاق"},tooltips:{today:"Go to today",clear:"Clear selection",close:"Close the picker",selectMonth:"Select Month",prevMonth:"Previous Month",nextMonth:"Next Month",selectYear:"Select Year",prevYear:"Previous Year",nextYear:"Next Year",selectDecade:"Select Decade",prevDecade:"Previous Decade",nextDecade:"Next Decade",prevCentury:"Previous Century",nextCentury:"Next Century",pickHour:"Pick Hour",incrementHour:"Increment Hour",decrementHour:"Decrement Hour",pickMinute:"Pick Minute",incrementMinute:"Increment Minute",decrementMinute:"Decrement Minute",pickSecond:"Pick Second",incrementSecond:"Increment Second",decrementSecond:"Decrement Second",togglePeriod:"Toggle Period",selectTime:"Select Time"},useStrict:!1,sideBySide:!1,daysOfWeekDisabled:!1,calendarWeeks:!1,viewMode:"days",toolbarPlacement:"default",showTodayButton:!1,showClear:!1,showClose:!1,widgetPositioning:{horizontal:"auto",vertical:"auto"},widgetParent:null,ignoreReadonly:!1,keepOpen:!1,focusOnShow:!0,inline:!1,keepInvalid:!1,datepickerInput:".datepickerinput",keyBinds:{up:function(n){if(n){var t=this.date()||this.getMoment();n.find(".datepicker").is(":visible")?this.date(t.clone().subtract(7,"d")):this.date(t.clone().add(this.stepping(),"m"))}},down:function(n){if(!n){this.show();return}var t=this.date()||this.getMoment();n.find(".datepicker").is(":visible")?this.date(t.clone().add(7,"d")):this.date(t.clone().subtract(this.stepping(),"m"))},"control up":function(n){if(n){var t=this.date()||this.getMoment();n.find(".datepicker").is(":visible")?this.date(t.clone().subtract(1,"years")):this.date(t.clone().add(1,"h"))}},"control down":function(n){if(n){var t=this.date()||this.getMoment();n.find(".datepicker").is(":visible")?this.date(t.clone().add(1,"y")):this.date(t.clone().subtract(1,"h"))}},left:function(n){if(n){var t=this.date()||this.getMoment();n.find(".datepicker").is(":visible")&&this.date(t.clone().subtract(1,"days"))}},right:function(n){if(n){var t=this.date()||this.getMoment();n.find(".datepicker").is(":visible")&&this.date(t.clone().add(1,"days"))}},pageUp:function(n){if(n){var t=this.date()||this.getMoment();n.find(".datepicker").is(":visible")&&this.date(t.clone().subtract(1,"months"))}},pageDown:function(n){if(n){var t=this.date()||this.getMoment();n.find(".datepicker").is(":visible")&&this.date(t.clone().add(1,"months"))}},enter:function(){this.hide()},escape:function(){this.hide()},"control space":function(n){n.find(".timepicker").is(":visible")&&n.find('.btn[data-action="togglePeriod"]').click()},t:function(){this.date(this.getMoment())},"delete":function(){this.clear()}},showSwitcher:!0,debug:!1,allowInputToggle:!1,disabledTimeIntervals:!1,disabledHours:!1,enabledHours:!1,viewDate:!1,hijri:!1,isRTL:!1}});