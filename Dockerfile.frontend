FROM python:3.10-slim

# Set working directory
WORKDIR /app

# Ensure the app directory is on Python's module search path
ENV PYTHONPATH=/app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Install uv for better package management
RUN pip install --no-cache-dir uv

# Copy Python package meta files and install dependencies
COPY requirements-frontend.in ./

# Install dependencies using uv (excluding problematic ones)
# Note: streamlit and streamlit-calendar will be installed via pip
RUN uv pip install --system --no-cache-dir -r requirements-frontend.in

# Install problematic packages separately using pip
RUN pip install --no-cache-dir streamlit streamlit-calendar

# Build the streamlit-calendar component frontend assets
RUN apt-get update && apt-get install -y --no-install-recommends nodejs npm \
    && cd /usr/local/lib/python3.10/site-packages/streamlit_calendar/frontend \
    && npm ci \
    && npm run build \
    && rm -rf /var/lib/apt/lists/*

# Copy only the necessary frontend files
COPY app/frontend/ app/frontend/
COPY app/services/ app/services/
COPY app/config.py app/config.py
COPY .streamlit/ .streamlit/

# Expose Streamlit default port
EXPOSE 8501

# Run the Streamlit dashboard
CMD ["streamlit", "run", "app/frontend/dashboard.py", "--server.address=0.0.0.0", "--server.port=8501", "--server.headless=true"] 